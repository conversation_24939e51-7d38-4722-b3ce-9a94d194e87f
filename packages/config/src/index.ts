export interface AppConfig {
  api: {
    port: number
    host: string
    corsOrigin: string
  }
  database: {
    url: string
  }
  frontend: {
    url: string
  }
}

export const defaultConfig: AppConfig = {
  api: {
    port: 3001,
    host: '0.0.0.0',
    corsOrigin: ''
  },
  database: {
    url: ''
  },
  frontend: {
    url: ''
  }
}

export const getConfig = (): AppConfig => {
  // Throw error if required environment variables are missing
  const requiredEnvVars = ['DATABASE_URL', 'FRONTEND_URL'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}. ` +
      'Please check your .env file.'
    );
  }
  
  return {
    api: {
      port: parseInt(process.env.PORT || '3001'),
      host: process.env.HOST || '0.0.0.0',
      corsOrigin: process.env.FRONTEND_URL!
    },
    database: {
      url: process.env.DATABASE_URL!
    },
    frontend: {
      url: process.env.FRONTEND_URL!
    }
  }
}