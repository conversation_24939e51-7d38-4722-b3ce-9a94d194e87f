# Story 1.5: <PERSON><PERSON> and Save Agent

## Status: Done

## Story
**As a** user, **I want** to be able to create and save a named agent workflow **within a project**, **so that** my work persists between sessions in an organized way.

## Acceptance Criteria
1. A "Create New Agent" button is available **within a project's view**.
2. The user can provide a title for their new agent.
3. Clicking a "Save" button sends the agent's content to a backend API endpoint.
4. The agent is successfully saved in the database and associated with the logged-in user and the **current project**.

## Tasks / Subtasks
- [x] Create agent name input UI in editor page (AC: 2)
  - [x] Add input field for agent title in editor header
  - [x] Implement validation for required agent name
  - [x] Style input to match existing UI patterns
- [x] Add "Create New Agent" button to project view (AC: 1)
  - [x] Create button component following shadcn/ui patterns
  - [x] Position button appropriately in project layout
  - [x] Navigate to editor page on button click
- [x] Implement save button and save functionality (AC: 3)
  - [x] Add save button to editor header
  - [x] Create save handler that collects agent name and editor content
  - [x] Show loading state during save operation
  - [x] Display success/error notifications
- [x] Create agent service for API calls (AC: 3)
  - [x] Create agent.service.ts in /apps/web/src/services/
  - [x] Implement createAgent method with proper typing
  - [x] Use centralized Axios instance from lib/api.ts
  - [x] Handle authentication headers
- [x] Create backend API endpoint for agent creation (AC: 3, 4)
  - [x] Create agent repository in /apps/api/src/repositories/
  - [x] Create agent service in /apps/api/src/services/
  - [x] Implement POST /agents route in Fastify
  - [x] Validate request payload (title, workflowJson, projectId)
  - [x] Associate agent with authenticated user
- [x] Implement database operations (AC: 4)
  - [x] Create Prisma migration for Agent table
  - [x] Implement repository methods for agent creation
  - [x] Ensure proper foreign key relationships with Project and User
- [x] Extend editor store for agent metadata (AC: 2, 3)
  - [x] Add agent title state to editor store
  - [x] Add save status state (idle, saving, saved, error)
  - [x] Add method to reset editor after successful save
- [x] Write unit tests
  - [x] Test agent name input component
  - [x] Test save button functionality
  - [x] Test agent service methods
  - [x] Test API endpoint with mocked Prisma
  - [x] Test editor store extensions
- [x] Manual testing of complete flow (AC: 1-4)
  - [x] Verify "Create New Agent" button appears in project view
  - [x] Test agent naming functionality
  - [x] Test save operation with valid data
  - [x] Verify agent is saved to database
  - [x] Test error handling for failed saves

## Dev Notes

### Previous Story Insights
From Story 1.4 implementation:
- BlockEditor component successfully implemented at `/apps/web/src/components/editor/block-editor.tsx`
- Editor state managed in `/apps/web/src/stores/editor.store.ts` with content stored as `workflowJson`
- Editor page exists at `/apps/web/src/app/(protected)/editor/page.tsx`
- Using Zustand for state management with persistence
- Following shadcn/ui component patterns with Tailwind CSS
- Protected routes working with middleware authentication

### Data Models
**Agent Model** [Source: architecture/4-data-models.md]:
```typescript
export interface Agent {
  id: string;
  projectId: string;
  title: string;
  workflowJson: Record<string, any>;   
  triggerType: 'api' | 'web_app' | 'external_event';
  createdAt: Date;
  updatedAt: Date;
}
```

### Database Schema
**Agent Table** [Source: architecture/8-database-schema.md]:
```sql
CREATE TABLE "Agent" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "title" TEXT NOT NULL,
  "workflowJson" JSONB NOT NULL,
  "triggerType" TEXT,
  "projectId" UUID NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE
);
```

### API Specifications
**Agent Creation Endpoint** (following REST conventions):
- **Endpoint**: `POST /agents`
- **Request Body**:
  ```typescript
  {
    title: string;
    workflowJson: Record<string, any>;
    projectId: string;
    triggerType?: 'api' | 'web_app' | 'external_event';
  }
  ```
- **Response**: Created Agent object with all fields
- **Authentication**: Required (via existing auth middleware)

### File Locations Based on Project Structure
[Source: architecture/11-unified-project-structure.md]
- **Frontend Components**:
  - Agent name input: Add to `/apps/web/src/app/(protected)/editor/page.tsx`
  - Agent service: `/apps/web/src/services/agent.service.ts`
  - Updated editor store: `/apps/web/src/stores/editor.store.ts`
- **Backend Implementation**:
  - Agent repository: `/apps/api/src/repositories/agent.repository.ts`
  - Agent service: `/apps/api/src/services/agent.service.ts`
  - Agent routes: `/apps/api/src/routes/agent.routes.ts`
  - Prisma schema update: `/apps/api/prisma/schema.prisma`

### Technical Constraints
1. **Service Layer Pattern** [Source: architecture/16-coding-standards.md]:
   - Frontend API calls must go through service layer
   - Use centralized Axios instance at `/apps/web/src/lib/api.ts`
   - Backend must use repository pattern for database access

2. **State Management** [Source: architecture/9-frontend-architecture.md]:
   - Extend existing editor store rather than creating new store
   - Follow existing Zustand patterns from auth.store.ts
   - Implement proper TypeScript interfaces

3. **API Gateway** [Source: architecture/10-backend-architecture.md]:
   - All API requests go through API Gateway
   - Implement proper error handling and validation
   - Use Fastify schema validation for request/response

4. **Project Context**:
   - Must obtain current projectId from route params or global state
   - Agent must be associated with both user and project
   - Consider navigation after successful save

### Coding Standards
[Source: architecture/16-coding-standards.md]
- **Naming Conventions**:
  - PascalCase for components (CreateAgentButton)
  - camelCase for services and methods (createAgent)
  - kebab-case for API routes (/agents)
- **TypeScript**: Strict mode, no `any` without justification
- **Imports**: Use absolute imports with @ alias

### Critical Implementation Notes
1. **Editor Integration**:
   - Editor content is available in `workflowJson` from editor store
   - Must validate agent has a title before saving
   - Clear or reset editor after successful save

2. **Error Handling**:
   - Show user-friendly error messages
   - Log errors appropriately
   - Handle network failures gracefully

3. **Project Association**:
   - Need to determine how to get current projectId
   - May need to add project context to editor page
   - Ensure proper authorization for project access

## Testing
[Source: architecture/15-testing-strategy.md]
- **Test Location**: Co-located with source files (*.test.ts, *.test.tsx)
- **Frontend Testing**:
  - Use Jest and React Testing Library
  - Test component rendering and user interactions
  - Mock API calls in service tests
- **Backend Testing**:
  - Use Jest with Fastify testing utilities
  - Mock Prisma client for repository tests
  - Test request validation and error cases
- **Integration Testing**:
  - Test full flow from UI to database
  - Verify agent is properly saved with all associations

## Change Log
- 2025-01-19: Story created by Scrum Master Bob
- 2025-01-19: Implemented by Dev Agent James - Ready for Review
- 2025-01-19: Security fixes implemented by Dev Agent James per QA recommendations

## Dev Agent Record

### Agent Model Used
claude-opus-4-20250514

### Debug Log References
- Successfully implemented agent creation flow from UI to database
- Added Project and Agent models to Prisma schema
- Created comprehensive test coverage for all components
- Implemented all QA-recommended security fixes:
  - Added project access verification to all agent endpoints
  - Implemented rate limiting (10 requests per 15 minutes for agent creation)
  - Added workflowJson size validation (1MB limit)
  - Replaced mock project ID with real project context
  - Created project selection UI with project management

### Completion Notes
- Implemented agent title input with validation in editor page
- Added "Create New Agent" button in projects view 
- Created save functionality with loading states and error handling
- Built complete backend API with repository pattern
- Added Project and Agent models to database schema
- All acceptance criteria met and tested
- **Security Enhancements Completed**:
  - ✅ Project access verification in all endpoints
  - ✅ Rate limiting on agent creation
  - ✅ Payload size validation (1MB limit)
  - ✅ Real project context with ProjectProvider
  - ✅ Project selection UI with create/select functionality

### File List
- /apps/web/src/app/(protected)/editor/page.tsx (modified)
- /apps/web/src/stores/editor.store.ts (modified)
- /apps/web/src/stores/editor.store.test.ts (modified)
- /apps/web/src/app/(protected)/projects/page.tsx (created, modified)
- /apps/web/src/components/dashboard/sidebar.tsx (modified)
- /apps/web/src/services/agent.service.ts (created)
- /apps/web/src/services/agent.service.test.ts (created)
- /apps/api/prisma/schema.prisma (modified)
- /apps/api/prisma/migrations/20250719_add_project_and_agent/migration.sql (created)
- /apps/api/src/repositories/agent.repository.ts (created)
- /apps/api/src/repositories/agent.repository.test.ts (created)
- /apps/api/src/services/agent.service.ts (created)
- /apps/api/src/services/agent.service.test.ts (created)
- /apps/api/src/routes/agent.routes.ts (created, modified)
- /apps/api/src/index.ts (modified)
- /apps/web/src/contexts/project-context.tsx (created)
- /apps/web/src/services/project.service.ts (created)
- /apps/api/src/routes/project.routes.ts (created)
- /apps/web/src/app/(protected)/layout.tsx (modified)
- /apps/api/src/routes/agent.routes.test.ts (created)
- /apps/api/src/test/helper.ts (created)
- /apps/api/jest.config.js (created)

## QA Results

### Review Date: 2025-01-19
**Reviewer**: Quinn (Senior Developer & QA Architect)
**Model**: claude-opus-4-20250514

### Summary
✅ **APPROVED WITH MINOR RECOMMENDATIONS** - The implementation successfully meets all acceptance criteria with solid architecture and clean code. Minor improvements suggested for production readiness.

### Acceptance Criteria Verification
1. ✅ **AC1: "Create New Agent" button in project view** - Implemented in `/projects` page with proper navigation
2. ✅ **AC2: User can provide agent title** - Title input with validation in editor page
3. ✅ **AC3: Save button sends to backend API** - Complete API integration with loading states
4. ✅ **AC4: Agent saved to database with associations** - Proper DB schema with user/project relationships

### Code Quality Assessment

#### Strengths
1. **Excellent Architecture**
   - Clean separation of concerns with service/repository pattern
   - Proper TypeScript interfaces throughout
   - Follows project's established patterns perfectly

2. **State Management**
   - Smart extension of existing editor store
   - Proper dirty state tracking with title consideration
   - Good UX with save status indicators

3. **API Design**
   - RESTful endpoints with proper HTTP semantics
   - Comprehensive Zod validation schemas
   - Good error handling and status codes

4. **Database Design**
   - Proper foreign key relationships with CASCADE deletes
   - Using Prisma effectively with type safety
   - Clean migration structure

5. **Testing Coverage**
   - Comprehensive unit tests for all layers
   - Proper mocking strategies
   - Edge cases covered

#### Areas for Enhancement

1. **Authorization Gaps** (Important for Production)
   ```typescript
   // TODO comments indicate missing project access verification
   // Recommendation: Implement before production
   async function verifyProjectAccess(userId: string, projectId: string) {
     const project = await prisma.project.findFirst({
       where: { id: projectId, userId }
     });
     if (!project) throw new Error('Unauthorized');
   }
   ```

2. **Mock Project ID** (Technical Debt)
   ```typescript
   // Currently using mock-project-id
   router.push('/editor?projectId=mock-project-id');
   
   // Should implement proper project context
   const { projectId } = useProjectContext();
   router.push(`/editor?projectId=${projectId}`);
   ```

3. **Error Message Specificity**
   ```typescript
   // Current: Generic error messages
   setSaveStatus('error', 'Failed to save agent');
   
   // Better: User-friendly specific messages
   if (error.response?.status === 409) {
     setSaveStatus('error', 'An agent with this title already exists');
   }
   ```

### Security Review
⚠️ **MEDIUM PRIORITY ISSUES**:
1. **Missing Project Authorization** - TODOs indicate auth checks not implemented
2. **No Rate Limiting** - Agent creation could be spammed
3. **Large Payload Risk** - No size limits on workflowJson

**Recommendations**:
```typescript
// Add to agent routes
const rateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each user to 100 requests per windowMs
});

// Add payload size validation
workflowJson: z.any().refine(
  (data) => JSON.stringify(data).length < 1048576, // 1MB limit
  { message: "Workflow data too large" }
)
```

### Performance Analysis
✅ **Good Performance**
- Efficient database queries with proper indexing
- No N+1 query issues
- Proper use of Prisma's query optimization

**Enhancement Opportunity**:
- Consider pagination for `getAgentsByProject` when agent count grows

### Best Practices Compliance
✅ **Excellent Adherence**
- Follows all coding standards
- Proper file organization
- Consistent naming conventions
- Clean imports and exports

### Testing Results
✅ **Comprehensive Test Coverage**
- Frontend service tests with proper mocking
- Backend repository/service tests
- Store extension tests
- All edge cases covered

### Refactoring Performed
None required - the code is clean and well-structured.

### Risk Assessment
**MEDIUM RISK** - Due to missing authorization checks. These MUST be implemented before production deployment.

### Recommended Improvements

1. **Immediate (Before Production)**:
   - Implement project access verification in all agent endpoints
   - Add rate limiting to prevent abuse
   - Add workflowJson size validation

2. **Short Term**:
   - Replace mock project ID with real project context
   - Add agent title uniqueness check within project
   - Implement proper project selection UI

3. **Long Term**:
   - Add agent versioning for workflow history
   - Implement agent templates
   - Add collaborative editing support

### Code Snippets for Key Improvements

**Project Authorization Middleware**:
```typescript
// Add to agent routes
async function requireProjectAccess(
  request: FastifyRequest,
  reply: FastifyReply,
  projectId: string,
  userId: string
) {
  const project = await prisma.project.findFirst({
    where: { id: projectId, userId }
  });
  
  if (!project) {
    return reply.status(403).send({ error: 'Access denied' });
  }
}
```

**Proper Project Context**:
```typescript
// Create a project context provider
export const ProjectContext = React.createContext<{
  currentProject: Project | null;
  setCurrentProject: (project: Project) => void;
}>({ currentProject: null, setCurrentProject: () => {} });

// Use in projects page
const { setCurrentProject } = useContext(ProjectContext);
const handleSelectProject = (project: Project) => {
  setCurrentProject(project);
  router.push(`/editor?projectId=${project.id}`);
};
```

### Final Verdict
The implementation is well-architected and meets all functional requirements. The code quality is high with good patterns and testing. However, the missing authorization checks must be addressed before production deployment. With these security enhancements, this will be a robust feature.

**Status**: Approved with required security fixes before production deployment.