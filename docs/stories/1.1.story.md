# Story 1.1: Project Scaffolding

## Status
Approved

## Story
**As a** developer,
**I want** the monorepo containing the initial Next.js frontend and Fastify backend applications to be set up,
**so that** I have a clean, version-controlled foundation to start building features.

## Acceptance Criteria
1. The monorepo structure is created with distinct, independent packages for the frontend and backend applications.
2. The Next.js application is initialized with the chosen stack (TypeScript, Tailwind CSS, shadcn/ui).
3. The Fastify application is initialized with the chosen stack (TypeScript, Prisma).
4. Basic dev scripts are configured to run both applications concurrently.

## Tasks / Subtasks
- [x] Set up Turborepo monorepo structure (AC: 1)
  - [x] Initialize Turborepo at project root with pnpm as package manager
  - [x] Create apps/ directory for applications
  - [x] Create packages/ directory for shared code
  - [x] Configure turbo.json with build, dev, and test pipelines
- [ ] Create Next.js frontend application (AC: 2)
  - [x] Initialize Next.js app in apps/web/ with TypeScript
  - [x] Configure Tailwind CSS with postcss
  - [ ] Install and configure shadcn/ui
  - [x] Set up Next.js 15+ App Router structure in src/app/
  - [x] Configure tsconfig.json for TypeScript ~5.4.5
- [x] Create Fastify backend application (AC: 3)
  - [x] Initialize Fastify app in apps/api/ with TypeScript
  - [x] Install Fastify 5+ and configure with TypeScript
  - [x] Install and configure Prisma 6+ with PostgreSQL
  - [x] Set up basic repository pattern structure in src/repositories/
  - [x] Install Pino ~9.0.0 for logging
  - [x] Configure tsconfig.json for backend
- [x] Configure development scripts (AC: 4)
  - [x] Add root package.json scripts for concurrent development
  - [x] Configure "dev" script to run both apps simultaneously
  - [x] Add "build" script for production builds
  - [x] Configure environment variables structure
- [x] Set up shared packages
  - [x] Create packages/config/ for shared configuration
  - [x] Create packages/types/ for shared TypeScript types
  - [x] Configure package.json for each shared package
- [ ] Initialize version control
  - [x] Create .gitignore files at appropriate levels
  - [ ] Add initial commit with base structure
- [x] Write unit tests for package configuration
  - [x] Test that turbo.json pipelines are correctly defined
  - [x] Test that all package.json files have required scripts

## Dev Notes

### Previous Story Insights
No previous story exists - this is the first story of the project.

### Project Structure
Based on unified project structure [Source: architecture/11-unified-project-structure.md]:
```
sflow-monorepo/
├── apps/
│   ├── web/     # Next.js frontend application
│   └── api/     # Fastify backend application
├── packages/
│   ├── config/  # Shared configuration
│   └── types/   # Shared TypeScript types
└── docs/        # Documentation
```

### Technology Stack Requirements
[Source: architecture/3-tech-stack.md]:
- **Monorepo Tool**: Turborepo ~1.13.3 - for managing separate frontend and backend packages
- **Frontend Framework**: Next.js 15+ Latest stable - with server-side rendering capabilities
- **Frontend Language**: TypeScript ~5.4.5 - for static typing
- **UI Component Library**: shadcn/ui ~0.8.0 - built on Tailwind CSS
- **Backend Framework**: Fastify 5+ latest stable - high-performance Node.js framework
- **Database ORM**: Prisma 6+ Latest Stable - for type-safe database access
- **Database**: PostgreSQL using Neon 16 - primary relational database
- **Logging**: Pino ~9.0.0 - high-performance JSON logger for Fastify

### Frontend Architecture Guidelines
[Source: architecture/9-frontend-architecture.md]:
- Components should be organized in src/components/
- App Router structure should be in src/app/
- API communication will use centralized Axios instance in src/lib/api.ts
- State management will use Zustand stores in src/stores/

### Backend Architecture Guidelines
[Source: architecture/10-backend-architecture.md]:
- Fastify routes should be organized by feature
- Repository Pattern must be used - all Prisma logic in src/repositories/
- Service architecture with self-contained Fastify applications

### Coding Standards
[Source: architecture/16-coding-standards.md]:
- Use PascalCase for components and DB tables
- Use camelCase for hooks (e.g., useCamelCase)
- Use kebab-case for API routes
- Enforce repository pattern for DB access

### Testing Standards
[Source: architecture/15-testing-strategy.md]:
- Tests should be co-located with the code they test
- Use Jest for unit tests
- E2E tests will live in root e2e/ directory
- Follow testing pyramid: large base of unit tests

### File Locations
- Frontend app: apps/web/
- Backend app: apps/api/
- Shared config: packages/config/
- Shared types: packages/types/
- Turbo configuration: turbo.json at root
- Package manager: pnpm (workspace configuration at root)

### Technical Constraints
No specific guidance found in architecture docs

## Testing

### Testing Standards
- Test files should be co-located with source files (e.g., turbo.test.json alongside turbo.json)
- Use Jest for unit testing configuration files
- Test naming convention: {filename}.test.{ts|tsx}
- Ensure all critical configuration is tested
- No E2E tests required for this scaffolding story

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-17 | 1.0 | Initial story creation | Bob (SM) |

## Dev Agent Record

### Agent Model Used
Claude Sonnet 4 (claude-sonnet-4-20250514)

### Debug Log References
- Successfully scaffolded monorepo structure with Turborepo and pnpm
- Created Next.js frontend application with TypeScript and Tailwind CSS
- Created Fastify backend application with TypeScript, Prisma, and Pino logging
- Configured shared packages for types and configuration
- Set up environment variable structure and development scripts

### Completion Notes List
- Major scaffolding components implemented successfully
- shadcn/ui installation pending (will require package installation)
- Initial git commit pending
- All core monorepo structure and applications are functional

### File List
- `/package.json` - Root monorepo package configuration
- `/pnpm-workspace.yaml` - pnpm workspace configuration
- `/turbo.json` - Turborepo build pipeline configuration
- `/apps/web/` - Next.js frontend application
- `/apps/api/` - Fastify backend application
- `/packages/config/` - Shared configuration package
- `/packages/types/` - Shared TypeScript types package
- `/.gitignore` - Git ignore configuration
- `/.env.example` - Environment variables template
- `/turbo.test.json` - Turbo configuration tests
- `/package.test.json` - Package configuration tests

## QA Results