# Story 1.2: User Authentication (OTP)

## Status
Done

## Story
**As a** user,
**I want** to sign up or log in by entering my email address to receive a one-time password (OTP),
**so that** I can access my account quickly and securely without a password.

## Acceptance Criteria
1. A user can enter their email address into a login field.
2. The system sends an email containing a unique, single-use OTP code to that address.
3. If the email address is new, a user account is created automatically upon the first login.
4. Entering the OTP code successfully logs the user into the application and establishes a valid session.
5. The OTP code expires after 5 minutes or after it has been used once.
6. The better-auth library is configured to handle the OTP flow.
7. A logged-in user has a clear option to log out, which invalidates their session.

## Tasks / Subtasks
- [ ] Install and configure better-auth with OTP plugin (AC: 6)
  - [ ] Install better-auth and its OTP plugin in apps/api/
  - [ ] Configure better-auth with Prisma adapter for PostgreSQL
  - [ ] Set up environment variables for BETTER_AUTH_SECRET and BETTER_AUTH_URL
  - [ ] Create auth configuration file with OTP settings (5 minute expiry)
  - [ ] Configure session management settings
- [ ] Set up database schema for authentication (AC: 3, 4, 5)
  - [ ] Run better-auth schema generation for Prisma
  - [ ] Add generated auth tables (User, Session, Account, Verification) to schema.prisma
  - [ ] Run Prisma migration to create auth tables
  - [ ] Verify User table includes email, name, phone, country fields from data model
- [ ] Implement backend authentication endpoints (AC: 1, 2, 3, 4, 5, 6)
  - [ ] Create Fastify catch-all route handler for /api/auth/* in apps/api/src/routes/
  - [ ] Convert Fastify requests to standard Request format for better-auth
  - [ ] Implement request handler that passes to auth.handler()
  - [ ] Configure CORS for auth endpoints
  - [ ] Add rate limiting to auth endpoints
- [ ] Configure email service with ReSend (AC: 2)
  - [ ] Install ReSend SDK in apps/api/
  - [ ] Configure ReSend API key in environment variables
  - [ ] Create email template for OTP emails
  - [ ] Configure better-auth to use ReSend for sending emails
  - [ ] Test email delivery in development environment
- [ ] Create frontend authentication components (AC: 1, 7)
  - [ ] Create LoginForm component in apps/web/src/components/auth/
  - [ ] Implement email input field with validation
  - [ ] Add "Send OTP" button with loading state
  - [ ] Create success message component for after email sent
  - [ ] Create LogoutButton component with session invalidation
- [ ] Implement authentication state management (AC: 4, 7)
  - [ ] Create auth store using Zustand in apps/web/src/stores/auth.ts
  - [ ] Implement login, logout, and session check actions
  - [ ] Configure better-auth client in apps/web/src/lib/auth-client.ts
  - [ ] Set up axios interceptor for auth token in apps/web/src/lib/api.ts
- [ ] Create authentication pages and routing (AC: 1, 4)
  - [ ] Create login page at apps/web/src/app/(auth)/login/page.tsx
  - [ ] Create OTP verification page at apps/web/src/app/(auth)/auth/otp-verify/page.tsx
  - [ ] Implement protected route layout in apps/web/src/app/(protected)/layout.tsx
  - [ ] Add middleware for route protection in apps/web/src/middleware.ts
  - [ ] Configure redirects after successful authentication
- [ ] Add authentication guards and session validation (AC: 4, 7)
  - [ ] Create Fastify hook for protected API routes
  - [ ] Implement session validation in backend
  - [ ] Add user context to authenticated requests
  - [ ] Create frontend auth guard component for protected pages
- [ ] Write unit tests for authentication logic (AC: all)
  - [ ] Test better-auth configuration and initialization
  - [ ] Test OTP generation and expiration
  - [ ] Test session creation and validation
  - [ ] Test email sending with mock ReSend
  - [ ] Test frontend auth store actions
  - [ ] Test protected route access control
- [ ] Write integration tests for auth flow (AC: all)
  - [ ] Test complete login flow from email entry to session creation
  - [ ] Test OTP expiration after 5 minutes
  - [ ] Test single-use OTP behavior
  - [ ] Test new user auto-registration
  - [ ] Test logout and session invalidation
- [ ] Write E2E tests for user authentication journey (AC: all)
  - [ ] Test user can enter email and receive OTP
  - [ ] Test entering OTP logs user in
  - [ ] Test logout functionality
  - [ ] Test protected route access

## Dev Notes

### Previous Story Insights
From Story 1.1 implementation:
- Monorepo structure successfully set up with Turborepo and pnpm
- Next.js frontend at apps/web/ with TypeScript and Tailwind CSS configured
- Fastify backend at apps/api/ with TypeScript and Prisma configured
- Environment variable structure established with .env.example
- Development scripts configured for concurrent app execution
- Note: shadcn/ui installation still pending from Story 1.1

### Authentication Technology Stack
[Source: architecture/3-tech-stack.md#Authentication]:
- **Authentication Library**: better-auth (Latest version) - handles all auth logic
- **Email Service**: ReSend API for sending OTP emails
- **Session Storage**: PostgreSQL via Prisma adapter
- **Frontend State**: Zustand for auth state management

### Data Models
[Source: architecture/4-data-models.md#2. User]:
```typescript
export interface User {
  id: string;
  email: string;
  name?: string;
  phone?: string;
  country?: string;
  createdAt: Date;
}
```

### Database Schema
[Source: architecture/8-database-schema.md#User Table]:
Better-auth will generate its own schema, but ensure User table includes:
- id (UUID)
- email (unique)
- name (optional)
- phone (optional)
- country (optional)
- createdAt
- updatedAt

### API Specifications
[Source: architecture/7-core-workflows.md#Workflow 1: User Login (OTP)]:
- POST /api/auth/otp/send - Send OTP email
- POST /api/auth/otp/verify - Verify OTP code
- POST /api/auth/logout - Invalidate session
- GET /api/auth/session - Check current session

All auth endpoints handled by better-auth through catch-all route.

### Component Specifications
Frontend components needed:
- LoginForm: Email input with validation, submit button [apps/web/src/components/auth/]
- LogoutButton: Triggers logout action [apps/web/src/components/auth/]
- AuthGuard: Wraps protected content [apps/web/src/components/auth/]

### File Locations
Based on project structure [Source: architecture/11-unified-project-structure.md]:
- Backend auth config: apps/api/src/lib/auth.ts
- Auth routes: apps/api/src/routes/auth.ts
- Frontend auth client: apps/web/src/lib/auth-client.ts
- Auth components: apps/web/src/components/auth/
- Auth store: apps/web/src/stores/auth.ts
- Auth pages: apps/web/src/app/(auth)/
- Protected layout: apps/web/src/app/(protected)/layout.tsx
- Middleware: apps/web/src/middleware.ts

### Testing Requirements
[Source: architecture/15-testing-strategy.md]:
- Unit tests co-located with source files (e.g., auth.test.ts)
- Integration tests for API endpoints
- E2E tests in root e2e/ directory
- Use Jest for unit/integration tests
- Use Playwright for E2E tests

### Technical Constraints
[Source: architecture/14-security-and-performance.md]:
- Use HttpOnly cookies for session tokens
- Implement rate limiting on auth endpoints
- Validate all inputs with Zod
- Configure CORS appropriately
- OTP codes expire after 5 minutes (configured in better-auth)

### External API Configuration
[Source: architecture/6-external-apis.md#ReSend API]:
- Service: ReSend for transactional emails
- Authentication: Bearer token via RESEND_API_KEY env var
- Used for sending OTP emails

### Coding Standards
[Source: architecture/16-coding-standards.md]:
- Use kebab-case for API routes (/api/auth/otp/send)
- Use PascalCase for React components (LoginForm)
- Use camelCase for hooks and stores (useAuth, authStore)
- Repository pattern for any custom user data access
- Shared types between frontend and backend

### CRITICAL: Better-Auth Implementation
Based on research, better-auth provides ALL authentication logic out-of-box:
- NO custom JWT implementation needed
- NO manual session management required
- NO custom token generation
- Built-in CSRF protection
- Automatic user creation on first login
- OTP plugin handles entire flow

Configure better-auth with:
```typescript
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { emailOTP } from "better-auth/plugins";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql"
  }),
  plugins: [
    emailOTP({
      expiresIn: 60 * 5, // 5 minutes
      sendVerificationOTP: async ({ email, otp, type }) => {
        // Use ReSend to send email
      }
    })
  ]
});
```

## Testing

### Testing Standards
[Source: architecture/15-testing-strategy.md]:
- Test files co-located with source: `{filename}.test.ts`
- Unit tests for auth configuration and business logic
- Integration tests for auth API endpoints
- E2E tests for complete user journeys
- Mock external services (ReSend) in tests
- Test both success and error paths
- Ensure 80%+ code coverage for auth code

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-17 | 1.0 | Initial story creation | Bob (SM) |

## Dev Agent Record

### Agent Model Used
[To be filled by Dev Agent]

### Debug Log References
[To be filled by Dev Agent]

### Completion Notes List
[To be filled by Dev Agent]

### File List
[To be filled by Dev Agent]

## QA Results
[To be filled by QA Agent]