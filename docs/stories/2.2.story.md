# Story 2.2: Project Dashboard View

## Status: Done

## Story
**As a** user, **I want** to click on a project and see a dedicated view listing all the agents, team members, and resources associated with it, **so that** I have a clear overview of the project's contents.

## Acceptance Criteria
1. Clicking a project on the main dashboard navigates to a dedicated Project Dashboard.
2. This dashboard contains a list of all agents belonging to the project.
3. A "Create New Agent" button exists within the Project Dashboard.
4. The Project Dashboard also provides access to project-specific settings.

## Tasks / Subtasks
- [x] Create project dashboard page component (AC: 1)
  - [x] Create page at `/app/(protected)/projects/[projectId]/page.tsx`
  - [x] Implement route parameter extraction for projectId
  - [x] Add loading state while fetching project data
  - [x] Handle invalid project ID or missing project
- [x] Implement project header section (AC: 1)
  - [x] Display project name and description
  - [x] Show project access type (Personal/Company-wide)
  - [x] Add breadcrumb navigation back to projects list
  - [x] Include last updated timestamp
- [x] Create agents list component (AC: 2)
  - [x] Create AgentsList component in `/components/agents/`
  - [x] Fetch agents for the current project via API
  - [x] Display agents in a grid or list view
  - [x] Show agent title, trigger type, and last modified
  - [x] Implement empty state for projects with no agents
  - [x] Add loading skeleton while fetching agents
- [x] Add Create New Agent functionality (AC: 3)
  - [x] Add "Create New Agent" button to dashboard
  - [x] Implement click handler to navigate to agent creation
  - [x] Pass projectId context to agent creation flow
  - [x] Ensure button is prominently displayed
- [x] Implement project settings access (AC: 4)
  - [x] Add settings button/link in project header
  - [x] Create route for project settings page
  - [x] Implement basic settings page structure
  - [x] Include navigation tabs for future sections (Members, Knowledge Base)
- [x] Update navigation and state management
  - [x] Update project store to track current project
  - [x] Ensure sidebar reflects current project context
  - [x] Handle browser back/forward navigation properly
  - [x] Update breadcrumb navigation component
- [x] Add comprehensive tests
  - [x] Unit tests for project dashboard page component
  - [x] Unit tests for AgentsList component
  - [x] Integration tests for API calls
  - [x] E2E test for full navigation flow from projects list to dashboard

## Dev Notes

### Previous Story Insights
From Story 2.1 completion:
- Project CRUD operations fully implemented with repository pattern
- Project list displays in responsive grid with delete functionality
- AccessType enum uses uppercase values (PERSONAL, COMPANY_WIDE)
- Authentication and authorization patterns established
- Project store manages state with Zustand persist middleware
- Alert dialog pattern established for confirmations
- Comprehensive test coverage patterns established

### Data Models
**Project Model** [Source: architecture/4-data-models.md#Project]:
```typescript
interface Project {
  id: string;
  name: string;
  description?: string;
  accessType: 'PERSONAL' | 'COMPANY_WIDE';
  userId: string; // Note: Will be workspaceId in future
  createdAt: Date;
  updatedAt: Date;
}
```

**Agent Model** [Source: Prisma schema and architecture/4-data-models.md#Agent]:
```typescript
interface Agent {
  id: string;
  title: string;
  workflowJson: Record<string, any>;
  triggerType: string;
  projectId: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### API Specifications
**Existing Project Endpoints** [Source: Current implementation]:
- `GET /api/projects/:id` - Get single project details

**Agent Endpoints to Implement/Use**:
- `GET /api/projects/:projectId/agents` - List all agents for a project
- `POST /api/projects/:projectId/agents` - Create new agent in project
- These will require authentication via better-auth session

### Component Specifications
**Page Structure** [Source: architecture/9-frontend-architecture.md#Routing]:
- Create page at `/apps/web/src/app/(protected)/projects/[projectId]/page.tsx`
- Use Next.js App Router dynamic segments for projectId
- Protected route inherits authentication from `(protected)` layout

**Component Organization** [Source: architecture/11-unified-project-structure.md]:
- Dashboard components: `/apps/web/src/components/dashboard/`
- Agent components: `/apps/web/src/components/agents/`
- Shared UI components: `/apps/web/src/components/ui/`

**UI Components to Use** [Source: Existing codebase]:
- Card, Button, Badge from shadcn/ui
- LoadingState component for loading states
- ErrorState component for error handling
- Use existing grid patterns from ProjectList component

**State Management** [Source: architecture/9-frontend-architecture.md#State Management]:
- Extend project store to include:
  - `currentProject: Project | null`
  - `setCurrentProject: (project: Project) => void`
- Create agent store if not exists for agent management
- Follow Zustand patterns with persist middleware

### File Locations Based on Project Structure
[Source: architecture/11-unified-project-structure.md]
- **Page Component**: `/apps/web/src/app/(protected)/projects/[projectId]/page.tsx`
- **Agent Components**: `/apps/web/src/components/agents/AgentsList.tsx`
- **Agent Service**: `/apps/web/src/services/agent.service.ts`
- **Agent Store**: `/apps/web/src/stores/agent.store.ts`
- **Agent Types**: `/apps/web/src/types/agent.ts`
- **Settings Page**: `/apps/web/src/app/(protected)/projects/[projectId]/settings/page.tsx`

### Technical Constraints
1. **Routing** [Source: Current implementation]:
   - Use Next.js dynamic routes with [projectId] parameter
   - Ensure proper error handling for invalid project IDs
   - Implement proper loading states during navigation

2. **Navigation Patterns** [Source: Existing codebase]:
   - Use Next.js Link component for client-side navigation
   - Update breadcrumbs to show: Dashboard > Projects > [Project Name]
   - Maintain sidebar state across navigation

3. **API Communication** [Source: Current patterns]:
   - Use existing Axios instance for API calls
   - Follow service layer pattern
   - Handle errors consistently with existing patterns

4. **Responsive Design**:
   - Follow mobile-first approach
   - Use Tailwind CSS classes consistently
   - Ensure touch-friendly interactions on mobile

### Project Structure Notes
- Follow existing patterns from dashboard and project list implementations
- Maintain consistency with current component structure
- Use established error handling and loading state patterns
- Keep tests co-located with components

### Implementation Notes
**Important**: 
- This story focuses on viewing agents within a project. Agent creation/editing will be handled in future stories.
- The "Create New Agent" button should navigate to an agent creation page (to be implemented in a future story).
- Project settings page should have a basic structure with tabs for future features (Members, Knowledge Base).
- Ensure all navigation maintains proper breadcrumb trail for user orientation.

## Testing
[Source: architecture/15-testing-strategy.md]
- **Test file location**: Co-located with source files
- **Testing frameworks**: Jest, React Testing Library, Playwright for E2E
- **Test coverage requirements**:
  - Page component unit tests for routing and data fetching
  - AgentsList component tests for display logic
  - Navigation tests for breadcrumbs and links
  - API integration tests for agent fetching
  - E2E test covering: Projects list → Project dashboard → Settings navigation
- **Mock requirements**:
  - Mock Next.js router for navigation tests
  - Mock API responses for agent data
  - Mock authentication context

## Change Log
- 2025-07-22: Story created by Scrum Master Bob
- 2025-07-22: Story implemented by Dev Agent James - All tasks completed successfully

## Dev Agent Record

### Agent Model Used
- claude-opus-4-********

### Debug Log References
- Fixed linting errors for unescaped quotes in JSX
- Added React imports to components for test compatibility
- Fixed useEffect dependencies warning in agents page
- Implemented useCallback for loadAgents function

### Completion Notes
- Successfully implemented project dashboard with all required features
- Created reusable AgentsList component with loading and empty states
- Implemented project settings page with tabbed interface for future features
- Added comprehensive test coverage for all new components and services
- Updated navigation to route from projects list to project dashboard
- Extended project store with currentProject state management
- Created agent service layer following established patterns
- All acceptance criteria have been met

### File List
- Created: `/apps/web/src/app/(protected)/projects/[projectId]/page.tsx`
- Created: `/apps/web/src/app/(protected)/projects/[projectId]/page.test.tsx`
- Created: `/apps/web/src/app/(protected)/projects/[projectId]/settings/page.tsx`
- Created: `/apps/web/src/components/agents/AgentsList.tsx`
- Created: `/apps/web/src/components/agents/AgentsList.test.tsx`
- Created: `/apps/web/src/types/agent.ts`
- Created: `/apps/web/src/services/agent.service.ts` (Note: Fixed to use correct API endpoints)
- Created: `/apps/web/src/services/agent.service.test.ts`
- Created: `/apps/web/src/stores/agent.store.ts`
- Created: `/e2e/tests/project-dashboard.spec.ts`
- Modified: `/apps/web/src/stores/project.store.ts`
- Modified: `/apps/web/src/services/project.service.ts`
- Modified: `/apps/web/src/components/projects/ProjectList.tsx`
- Modified: `/apps/web/src/app/(protected)/projects/[projectId]/agents/page.tsx`

## QA Results

### Review Date: 2025-07-22
### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment
The implementation is well-structured and follows the established patterns in the codebase. The developer successfully implemented all acceptance criteria with proper error handling, loading states, and responsive design. The code demonstrates good separation of concerns and reusability.

### Refactoring Performed
- **File**: `/apps/web/src/app/(protected)/projects/[projectId]/page.tsx`
  - **Change**: Enhanced error handling to extract error messages from API responses
  - **Why**: Provides more informative error messages to users
  - **How**: Added proper error message extraction chain with fallbacks
  
- **File**: `/apps/web/src/components/agents/AgentsList.tsx`
  - **Change**: Refactored fetchAgents to use useCallback and added retry functionality
  - **Why**: Prevents unnecessary re-renders and improves user experience on errors
  - **How**: Extracted fetchAgents logic, memoized with useCallback, and added Try Again button
  
- **File**: `/apps/web/src/app/(protected)/projects/[projectId]/page.tsx`
  - **Change**: Added projectId validation
  - **Why**: Security - prevents potential injection attacks
  - **How**: Added regex validation for projectId format before processing

### Compliance Check
- Coding Standards: [✓] Follows PascalCase for components, proper service layer usage, Zustand for state
- Project Structure: [✓] Files correctly placed according to architecture guidelines
- Testing Strategy: [✓] Comprehensive unit tests, integration tests, and E2E test provided
- All ACs Met: [✓] All acceptance criteria successfully implemented

### Improvements Checklist
[x] Enhanced error handling with proper message extraction
[x] Added retry mechanism for failed agent fetches
[x] Implemented projectId validation for security
[x] Refactored duplicate code in AgentsList component
[ ] Consider adding loading skeleton for project header section
[ ] Add aria-label attributes to improve accessibility
[ ] Consider implementing optimistic updates for better UX

### Security Review
- Added input validation for projectId parameter to prevent injection attacks
- All API calls use the established axios instance with authentication
- No sensitive data exposed in error messages
- Proper error boundaries in place

### Performance Considerations
- Components use proper memoization with useCallback
- Loading states prevent unnecessary re-renders
- Grid layout is responsive and performant
- Consider implementing virtual scrolling for large agent lists in future

### Post-Implementation Fixes
- Fixed agent service to use correct backend API endpoints (`/api/agents?projectId=xxx` instead of `/api/projects/:projectId/agents`)
- Fixed navigation to use existing editor route pattern (`/editor?projectId=xxx&agentId=xxx` instead of non-existent `/projects/:projectId/agents/:agentId`)

### Final Status
[✓ Approved - Ready for Done]

The implementation meets all requirements with high code quality. Minor suggestions for future improvements noted above but not blocking.