# Story 3.1: Knowledge Base - File Upload

## Status: Done

## Story
**As a** user, **I want** a dedicated page where I can upload my documents (PDF, TXT, DOCX), **so that** I can create a personal knowledge repository for my agents to use.

## Acceptance Criteria
1. A "Knowledge Base" section is available in the application.
2. It contains a file upload component that accepts PDF, TXT, and DOCX file types.
3. An "Upload" button sends the selected files to a secure backend endpoint.
4. A visual indicator shows the progress of the file uploads.

## Tasks / Subtasks
- [x] Create Knowledge Base page structure (AC: 1)
  - [x] Create new route at /knowledge-base in Next.js app directory
  - [x] Add Knowledge Base navigation item to main sidebar/navigation
  - [x] Create basic page layout with title and description
  - [x] Ensure page is protected (requires authentication)
- [x] Implement file upload UI component (AC: 2)
  - [x] Create FileUpload component using shadcn/ui components
  - [x] Implement drag-and-drop functionality
  - [x] Add file type validation (PDF, TXT, DOCX only)
  - [x] Display selected files with preview before upload
  - [x] Add file size validation and display
  - [x] Style component following existing UI patterns
- [x] Create backend file upload endpoint (AC: 3)
  - [x] Create POST /api/documents/upload endpoint in Fastify
  - [x] Implement multipart/form-data handling
  - [x] Add file type validation on backend
  - [x] Store file metadata in database
  - [x] Save files to secure storage location
  - [x] Return appropriate response with document IDs
- [x] Implement upload progress tracking (AC: 4)
  - [x] Add progress tracking to Axios upload request
  - [x] Create progress bar component
  - [x] Handle multiple file upload progress
  - [x] Show success/error states for each file
  - [x] Implement retry mechanism for failed uploads
- [x] Create document repository and service layers
  - [x] Create document.repository.ts with CRUD operations
  - [x] Implement document service for business logic
  - [x] Add proper error handling and validation
- [x] Add comprehensive tests
  - [x] Unit tests for FileUpload component
  - [x] API endpoint tests with file upload scenarios
  - [x] Repository tests for document operations
  - [x] E2E test for complete upload flow

## Dev Notes

### Previous Story Insights
This is the first story in Epic 3. Stories from Epic 2 established:
- Project-based organization with settings pages
- Document association patterns (ProjectDocuments join table)
- File management UI patterns from Knowledge Base association
- Authentication and authorization patterns

### Data Models
**KnowledgeBaseDocument Model** [Source: architecture/4-data-models.md#KnowledgeBaseDocument]:
```typescript
export interface KnowledgeBaseDocument {  
  id: string;  
  workspaceId: string;  
  fileName: string;  
  fileType: string;  
  status: 'pending' | 'processing' | 'completed' | 'failed';  
  createdAt: Date;  
}
```

**File Status Flow**:
- 'pending': Initial state when file is uploaded
- 'processing': File is being processed (text extraction, chunking)
- 'completed': File is ready for use
- 'failed': Processing failed

### API Specifications
**File Upload Endpoint**:
- `POST /api/documents/upload`
  - Content-Type: multipart/form-data
  - Body: File upload with metadata
  - Max file size: TBD (consider 10MB default)
  - Accepted types: application/pdf, text/plain, application/vnd.openxmlformats-officedocument.wordprocessingml.document
  - Response: Array of created KnowledgeBaseDocument objects
  - Requires authentication
  - Associates documents with user's workspace

### Component Specifications
**Page Location** [Source: architecture/11-unified-project-structure.md]:
- Knowledge Base page: `/apps/web/src/app/(protected)/knowledge-base/page.tsx`
- File upload component: `/apps/web/src/components/knowledge-base/FileUpload.tsx`

**UI Component Library** [Source: architecture/3-tech-stack.md]:
- Using shadcn/ui (~0.8.0) for UI components
- Built on Tailwind CSS
- Should use existing Button, Card, Progress components
- Follow existing design patterns from project settings

**State Management** [Source: architecture/9-frontend-architecture.md]:
- Consider creating a knowledge base store in `/apps/web/src/stores/`
- Local component state for upload progress
- Use React Hook Form if complex form validation needed

### File Locations Based on Project Structure
[Source: architecture/11-unified-project-structure.md]
- **Backend Routes**: `/apps/api/src/routes/document.routes.ts` (extend existing)
- **Repository**: `/apps/api/src/repositories/document.repository.ts` (extend existing)
- **Frontend Page**: `/apps/web/src/app/(protected)/knowledge-base/page.tsx`
- **Components**: `/apps/web/src/components/knowledge-base/FileUpload.tsx`
- **Types**: `/apps/web/src/types/document.ts` (extend existing)
- **Service**: `/apps/web/src/services/document.service.ts` (extend existing)

### Technical Constraints
1. **File Storage** [Source: architecture/5-components.md]:
   - Knowledge Base Service manages document storage
   - Consider local file storage initially (filesystem)
   - Plan for future cloud storage migration
   - Ensure files are stored securely with proper access controls

2. **File Processing** [Source: architecture/7-core-workflows.md]:
   - Files will need processing after upload (Story 3.2)
   - Status should be set to 'pending' initially
   - Processing includes text extraction and vectorization
   - Consider queue-based processing for scalability

3. **Security Considerations** [Source: architecture/14-security-and-performance.md]:
   - Validate file types and content (not just extension)
   - Implement virus scanning if possible
   - Enforce file size limits
   - Store files outside web root
   - Generate unique filenames to prevent conflicts
   - Ensure workspace isolation

4. **API Communication** [Source: architecture/9-frontend-architecture.md]:
   - Use centralized Axios instance from `/apps/web/src/lib/api.ts`
   - Auth token automatically attached via interceptor
   - Handle progress events for upload tracking

### Project Structure Notes
- Knowledge Base is a workspace-level feature (not project-specific)
- Files uploaded here will later be associated with projects (Story 2.4 already implemented this)
- Consider pagination for file list in future stories
- File processing will be handled by Knowledge Base Service (Story 3.2)

### Implementation Notes
**File Upload Best Practices**:
1. Validate file types on both client and server
2. Show clear file size limits and progress
3. Handle multiple file selection
4. Provide drag-and-drop for better UX
5. Show thumbnail/icon for file types
6. Clear error messages for validation failures

**Backend Considerations**:
1. Use streaming for large file uploads
2. Implement proper multipart handling in Fastify
3. Consider using multer or similar for file handling
4. Store file metadata immediately, process asynchronously
5. Return document IDs for tracking

**Frontend Considerations**:
1. Use FormData for file uploads
2. Track upload progress with Axios onUploadProgress
3. Show individual progress for multiple files
4. Allow cancellation of in-progress uploads
5. Persist upload state in case of page refresh

## Testing
[Source: architecture/15-testing-strategy.md]
- **Test file locations**: Co-located with source files
- **Testing frameworks**: Jest for unit/integration, Playwright for E2E
- **Test requirements**:
  - Component tests for file selection, validation, drag-drop
  - API tests for file upload with various scenarios
  - Test file type validation (valid and invalid files)
  - Test file size limits
  - Test concurrent uploads
  - Test error scenarios (network failure, server error)
  - E2E test: Navigate to Knowledge Base → Upload files → Verify in list
- **Mock requirements**:
  - Mock file objects for component tests
  - Mock Axios for upload progress
  - Test files of different types for integration tests

## Change Log
- 2025-07-23: Story created by Scrum Master
- 2025-07-23: Implemented by Developer - Added Knowledge Base file upload functionality

## Dev Agent Record

### Agent Model Used
claude-opus-4-20250514

### Debug Log References
Task 1: Created Knowledge Base page at /knowledge-base route with basic layout
Task 2: Implemented FileUpload component with drag-and-drop and file validation
Task 3: Created backend file upload endpoint with multipart support
Task 4: Implemented upload progress tracking with visual feedback
Task 5: Repository and service layers already exist with necessary functionality
Task 6: Added comprehensive tests for all components and endpoints

### Completion Notes List
- Implemented complete file upload functionality with drag-and-drop support
- Added backend endpoint with multipart file handling and validation
- Integrated progress tracking for file uploads
- Created comprehensive test suite for all functionality
- Used existing document repository and extended it with necessary methods
- Added storedFileName field to database schema for secure file storage

### File List
- apps/web/src/app/(protected)/knowledge-base/page.tsx (new)
- apps/web/src/components/dashboard/sidebar.tsx (modified)
- apps/web/src/components/knowledge-base/FileUpload.tsx (new)
- apps/web/src/components/ui/progress.tsx (new)
- apps/web/package.json (modified)
- apps/api/src/routes/document.routes.ts (modified)
- apps/api/prisma/schema.prisma (modified)
- apps/api/package.json (modified)
- apps/web/src/services/document.service.ts (modified)
- apps/web/src/components/knowledge-base/FileUpload.test.tsx (new)
- apps/api/src/routes/document.routes.test.ts (modified)
- apps/api/src/repositories/document.repository.test.ts (modified)
- e2e/tests/knowledge-base-upload.spec.ts (new)
- apps/web/jest.config.js (modified)

## QA Results

### Permission Analysis for Knowledge Base Document Upload (Story 3.1)

**Reviewed by**: Quinn (Senior Developer & QA Architect)
**Date**: 2025-07-23
**Review Focus**: Authorization and permission system implementation

#### Summary
The knowledge base document upload system has been implemented with a workspace-based permission model that properly restricts document access and associations.

#### Key Security Findings

1. **Document Upload Permissions** (/documents/upload):
   - ✅ Authentication required via better-auth session
   - ✅ Automatic workspace association from user's membership
   - ✅ Files are stored in workspace-specific directories
   - ✅ Proper file type validation (PDF, TXT, DOCX)
   - ✅ File size limits enforced (10MB)
   - ✅ Audit logging for all uploads

2. **Document Access Permissions** (/projects/:id/documents):
   - ✅ Authentication required
   - ✅ Project membership verification (any role)
   - ✅ Returns only documents associated with the project
   - ⚠️ Does not check workspace membership explicitly (relies on project membership)

3. **Document Association Permissions** (/projects/:id/available-documents & PUT /projects/:id/documents):
   - ✅ Admin-only access enforced
   - ✅ Workspace isolation - only shows documents from project's workspace
   - ✅ Validates documents belong to same workspace before association
   - ✅ Comprehensive audit logging with before/after state

#### Authorization Model Analysis

**Current Role Hierarchy**:
- `ADMIN`: Can view and modify document associations
- `MEMBER`: Can only view associated documents

**Workspace Isolation**:
- Documents are strictly isolated by workspace
- Cross-workspace document access is prevented
- File storage uses workspace-specific directories

#### Recommendations

1. **Consider Adding Owner Role**: The current model only has ADMIN and MEMBER. Consider adding an OWNER role for the user who creates the project.

2. **Explicit Workspace Validation**: While project membership implies workspace access, consider adding explicit workspace membership validation for defense in depth.

3. **Document Deletion Permissions**: No delete endpoint exists yet. When implemented, ensure only workspace admins or document uploaders can delete.

4. **Rate Limiting**: Consider adding rate limits to the upload endpoint to prevent abuse.

5. **File Validation Enhancement**: Current validation checks MIME type. Consider adding magic number validation for actual file content verification.

#### Test Coverage Assessment

✅ **Comprehensive test coverage** including:
- Authentication failure scenarios
- Role-based access control (admin vs member)
- File type validation
- Upload success flows
- Association update flows

⚠️ **Missing test scenarios**:
- Workspace isolation tests (attempting to associate documents from different workspaces)
- Concurrent upload handling
- File system failure scenarios

#### Overall Assessment

**Grade: B+**

The implementation provides solid security foundations with proper authentication, authorization, and workspace isolation. The permission model is clean and follows the principle of least privilege. Minor enhancements around role granularity and additional validation layers would bring this to an A grade.

**Security Posture**: GOOD - No critical vulnerabilities identified. The workspace-based isolation model effectively prevents unauthorized access.