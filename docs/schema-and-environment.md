# Schema and Environment Configuration

## Database Schema

The application uses PostgreSQL with Prisma ORM. The schema includes Better Auth's required models plus custom user fields.

### User Model
```prisma
model User {
  id            String    @id @default(cuid())
  name          String?   // User's display name
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String?
  phone         String?   // Custom field for onboarding
  country       String?   // Custom field for onboarding (ISO 2-letter code)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  sessions      Session[]
  accounts      Account[]
}
```

### Better Auth Models
- **Session**: Manages user sessions with tokens, expiry, IP, and user agent
- **Account**: Handles authentication providers and tokens
- **Verification**: Stores OTP codes and verification tokens

## Environment Variables

### API Configuration (.env)
```bash
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/sflow"

# Server
PORT=3001                    # API server port
HOST=0.0.0.0                # API server host

# Authentication
AUTH_URL=http://localhost:3001  # Better Auth base URL
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001  # Comma-separated CORS origins

# Email (Resend)
RESEND_API_KEY=your_resend_api_key         # Get from https://resend.com
RESEND_FROM_EMAIL=<EMAIL>  # Verified sender email
```

### Frontend Configuration (.env.local)
```bash
# API URL
NEXT_PUBLIC_API_URL=http://localhost:3001  # Must match API server URL
```

## Setup Instructions

1. **Database Setup**
   ```bash
   cd apps/api
   npx prisma migrate dev  # Run migrations
   npx prisma generate     # Generate Prisma client
   ```

2. **Environment Files**
   - Copy `.env.example` to `.env` in both `apps/api` and `apps/web`
   - Update values for your environment

3. **Production Deployment**
   - Update `ALLOWED_ORIGINS` to include your production domains
   - Use HTTPS URLs for `AUTH_URL` and `NEXT_PUBLIC_API_URL`
   - Ensure `DATABASE_URL` points to your production database

## Key Changes from Default Better Auth

1. **Custom User Fields**: Added `name`, `phone`, and `country` for onboarding
2. **Environment-based CORS**: Dynamic origin configuration instead of hardcoded values
3. **Cookie-based Sessions**: Using Better Auth's default cookie authentication

## Security Considerations

1. Never commit `.env` files to version control
2. Use strong database passwords
3. In production, always use HTTPS URLs
4. Verify your sender email with Resend before sending emails