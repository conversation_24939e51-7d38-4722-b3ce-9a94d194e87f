# **Epic 1: Foundation & Core Editor Experience**

**Epic Goal:** This epic lays the essential groundwork for the entire sflow platform. By the end of this epic, a user will be able to register, log in, see a dashboard of projects, and use a basic editor in English or Arabic.

## **Story 1.1: Project Scaffolding**

**As a** developer, **I want** the monorepo containing the initial Next.js frontend and Fastify backend applications to be set up, **so that** I have a clean, version-controlled foundation to start building features.

**Acceptance Criteria:**

1. The monorepo structure is created with distinct, independent packages for the frontend and backend applications.  
2. The Next.js application is initialized with the chosen stack (TypeScript, Tailwind CSS, shadcn/ui).  
3. The Fastify application is initialized with the chosen stack (TypeScript, Prisma).  
4. Basic dev scripts are configured to run both applications concurrently.

## **Story 1.2: User Authentication (OTP)**

**As a** user, **I want** to sign up or log in by entering my email address to receive a one-time password (OTP), **so that** I can access my account quickly and securely without a password.

**Acceptance Criteria:**

1. A user can enter their email address into a login field.  
2. The system sends an email containing a unique, single-use OTP code to that address.  
3. If the email address is new, a user account is created automatically upon the first login.  
4. Entering the OTP code successfully logs the user into the application and establishes a valid session.  
5. The OTP code expires after 5 minutes or after it has been used once.  
6. The better-auth library is configured to handle the OTP flow.  
7. A logged-in user has a clear option to log out, which invalidates their session.

## **Story 1.3: New User Onboarding**

**As a** new user, **I want** to be guided through a simple onboarding process after my first login, **so that** I can provide my basic profile information.

**Acceptance Criteria:**

1. Immediately after a user's first successful authentication via OTP, they are redirected to an onboarding screen.  
2. The screen prompts the user to enter their full name, phone number, and country.  
3. The phone number input must include a country code selector.  
4. The country input must be a searchable dropdown list.  
5. Submitting the form saves the information to the user's profile in the database.  
6. This onboarding flow is only shown once to each user.  
7. The user can optionally skip the onboarding flow.

## **Story 1.4: Core Editor Interface**

**As a** user, **I want** a basic editor interface powered by Blocknote.js, **so that** I can begin creating an agent by adding and arranging text blocks.

**Acceptance Criteria:**

1. The editor interface loads successfully.  
2. The user can add a new block of text.  
3. The user can type and edit text within a block.  
4. The user can re-order blocks using drag-and-drop.  
5. The editor uses the Blocknote.js library.

## **Story 1.5: Create and Save Agent**

**As a** user, **I want** to be able to create and save a named agent workflow **within a project**, **so that** my work persists between sessions in an organized way.

**Acceptance Criteria:**

1. A "Create New Agent" button is available **within a project's view**.  
2. The user can provide a title for their new agent.  
3. Clicking a "Save" button sends the agent's content to a backend API endpoint.  
4. The agent is successfully saved in the database and associated with the logged-in user and the **current project**.

## **Story 1.6: User Dashboard (Project List)**

**As a** user, **I want** to see a list of my **projects** on a dashboard, **so that** I can easily access and organize my work.

**Acceptance Criteria:**

1. A dashboard page displays a list of all projects the logged-in user has access to.  
2. The list is fetched from a secure backend API endpoint.  
3. Each item in the list displays the project's title.  
4. Clicking on a project in the list opens the **Project View**.

## **Story 1.7: Typed Agent Inputs**

**As a** user, **I want** to define specific types for my agent's inputs using a configuration modal, **so that** the system can generate appropriate forms and validate data correctly.

**Acceptance Criteria:**

1. Using the /input command in the editor opens a side-drawer/modal for configuration.  
2. The user can select an input type from a list, including: Text, Number, True/False, Image, Audio, PDF/Document, CSV/Text, List, Object, and Select.  
3. The user must provide a name for the input (e.g., customer\_name).  
4. When an agent is deployed as a web page, the system uses these defined types to generate the correct HTML form fields.

## **Story 1.8: Internationalization (i18n) & RTL Support**

**As a** user, **I want** to use the application in either English or Arabic, **so that** I can work in my preferred language.

**Acceptance Criteria:**

1. A language switcher is available in the UI.  
2. All UI text is sourced from locale files to support translation.  
3. The UI layout correctly adapts to a Right-to-Left (RTL) orientation when Arabic is selected.  
4. The initial implementation includes complete translations for English and Arabic.
