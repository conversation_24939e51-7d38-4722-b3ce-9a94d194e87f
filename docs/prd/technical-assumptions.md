# **Technical Assumptions**

* **Frontend:**  
  * **Framework:** Next.js  
  * **Language:** React / TypeScript  
  * **Block Editor Library:** Blocknote.js  
  * **UI Components:** shadcn/ui  
  * **Styling:** Tailwind CSS  
* **Backend:**  
  * **Framework:** Fastify  
  * **Database ORM:** Prisma  
  * **Authentication:** better-auth (configured for OTP)  
  * **Database:** PostgreSQL  
* **Repository Structure:** A monorepo will be used, but with **strict separation** (no shared code) between the frontend and backend packages to ensure independent deployment.  
* **Service Architecture:** A containerized, microservices-style architecture is assumed.  
* **Testing Requirements:** A comprehensive strategy including Unit, Integration, and End-to-End (E2E) tests is required.
