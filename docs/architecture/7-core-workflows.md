# **7\. Core Workflows**

* ### **Workflow 1: User Login (OTP)**

  * This diagram shows the process of a user signing up or logging in using a passwordless OTP code \[cite: PRD.md\].

sequenceDiagram  
    participant User  
    participant Frontend (sflow-web)  
    participant API Gateway  
    participant Auth Service  
    participant ReSend API

    User-\>\>+Frontend: Enters email and clicks "Login"  
    Frontend-\>\>+API Gateway: POST /auth/login (email)  
    API Gateway-\>\>+Auth Service: Forwards request  
    Auth Service-\>\>+ReSend API: Request to send OTP email  
    ReSend API--\>\>-Auth Service: Confirms email sent  
    Auth Service--\>\>-API Gateway: Responds with success message  
    API Gateway--\>\>-Frontend: Responds with success message  
    Frontend--\>\>-User: Displays "Check your email" message

    User-\>\>+Frontend: Enters OTP code from email  
    Frontend-\>\>+API Gateway: POST /auth/verify (token)  
    API Gateway-\>\>+Auth Service: Forwards request  
    Auth Service-\>\>Auth Service: Verifies token against database  
    Auth Service--\>\>-API Gateway: Responds with session token/cookie  
    API Gateway--\>\>-Frontend: Responds with session token/cookie  
    Frontend-\>\>Frontend: Establishes session, redirects to dashboard  
    Frontend--\>\>-User: Displays user dashboard

* ### **Workflow 2: Agent Execution with RAG**

  * This diagram illustrates how an agent uses the Knowledge Base to provide a context-aware answer from the LLM \[cite: PRD.md\].

sequenceDiagram  
    participant User  
    participant Frontend (sflow-web)  
    participant API Gateway  
    participant Agent Service  
    participant KB Service  
    participant Database (pgvector)  
    participant OpenRouter API

    User-\>\>+Frontend: Runs agent with a specific query  
    Frontend-\>\>+API Gateway: POST /agents/{id}/run (query)  
    API Gateway-\>\>+Agent Service: Forwards request  
    Note over Agent Service: Encounters a "Generation" block\<br\>with a @KnowledgeBase reference.  
    Agent Service-\>\>+KB Service: POST /search (query, document\_id)  
    KB Service-\>\>+Database (pgvector): Performs vector similarity search  
    Database (pgvector)--\>\>-KB Service: Returns relevant chunks.  
    KB Service--\>\>-Agent Service: Returns retrieved text chunks.  
    Note over Agent Service: Constructs final prompt with RAG context.  
    Agent Service-\>\>+OpenRouter API: POST /chat/completions (final\_prompt)  
    OpenRouter API--\>\>-Agent Service: Returns LLM-generated response.  
    Agent Service--\>\>-API Gateway: Responds with final agent output.  
    API Gateway--\>\>-Frontend: Responds with final agent output.  
    Frontend--\>\>-User: Displays the final answer.

* ### **Workflow 3: Executing an External Action**

  * This diagram shows how an agent executes an action in a third-party application \[cite: PRD.md\].

sequenceDiagram  
    participant User  
    participant Frontend (sflow-web)  
    participant API Gateway  
    participant Agent Service  
    participant Connector Service  
    participant Database  
    participant Pipedream API

    User-\>\>+Frontend: Runs an agent containing an "Action" block  
    Frontend-\>\>+API Gateway: POST /agents/{id}/run  
    API Gateway-\>\>+Agent Service: Forwards request  
    Note over Agent Service: Executes workflow, encounters "Action" block.  
    Agent Service-\>\>+Connector Service: POST /execute-action (action\_details, connection\_id)  
    Connector Service-\>\>+Database: Fetches encrypted credentials for the connection\_id  
    Database--\>\>-Connector Service: Returns encrypted credentials  
    Connector Service-\>\>+Pipedream API: Executes the specified action with credentials  
    Pipedream API--\>\>-Connector Service: Returns result of the action  
    Connector Service--\>\>-Agent Service: Returns action result  
    Agent Service--\>\>-API Gateway: Responds with final agent output  
    API Gateway--\>\>-Frontend: Responds with final agent output  
    Frontend--\>\>-User: Displays the final result
