# **4\. Data Models**

* ### **1\. Workspace**

  * **Purpose**: Represents a top-level team or company account. It is the primary container for users, projects, and the central Knowledge Base.  
  * **TypeScript Interface**:

export interface Workspace {  
  id: string;  
  name: string;  
  logoUrl?: string;  
  primaryColor?: string;  
  billingPlan: 'free' | 'pro' | 'enterprise\_pilot';  
  createdAt: Date;  
  updatedAt: Date;  
}

* ### **2\. User**

  * **Purpose**: Represents an individual who can log in and interact with the platform.  
  * **TypeScript Interface**:

export interface User {  
  id: string;  
  email: string;  
  name?: string;  
  phone?: string;  
  country?: string;  
  createdAt: Date;  
}

* ### **3\. Project**

  * **Purpose**: A container within a workspace to organize related agents, team members, and knowledge resources \[cite: PRD.md\].  
  * **TypeScript Interface**:

export interface Project {  
  id: string;  
  workspaceId: string;  
  name: string;  
  description?: string;  
  accessType: 'personal' | 'company\_wide';  
  createdAt: Date;  
  updatedAt: Date;  
}

* ### **4\. Agent**

  * **Purpose**: The core executable workflow created by a user. Contains the blocks, logic, and configuration.  
  * **TypeScript Interface**:

export interface Agent {  
  id: string;  
  projectId: string;  
  title: string;  
  workflowJson: Record\<string, any\>;   
  triggerType: 'api' | 'web\_app' | 'external\_event';  
  createdAt: Date;  
  updatedAt: Date;  
}

* ### **5\. AgentVersion**

  * **Purpose**: A read-only, timestamped snapshot of an agent's workflow, created automatically every time a user deploys the agent \[cite: PRD.md\].  
  * **TypeScript Interface**:

export interface AgentVersion {  
  id: string;  
  agentId: string;  
  workflowJson: Record\<string, any\>;  
  versionNote?: string;  
  createdAt: Date;  
}

* ### **6\. KnowledgeBaseDocument**

  * **Purpose**: Represents a single document (PDF, DOCX, TXT) uploaded by a user to the central workspace repository \[cite: PRD.md\].  
  * **TypeScript Interface**:

export interface KnowledgeBaseDocument {  
  id: string;  
  workspaceId: string;  
  fileName: string;  
  fileType: string;  
  status: 'pending' | 'processing' | 'completed' | 'failed';  
  createdAt: Date;  
}

* ### **7\. Connection**

  * **Purpose**: Represents a user's named, authenticated connection to a single external business application \[cite: PRD.md\].  
  * **TypeScript Interface**:

export interface Connection {  
  id: string;  
  workspaceId: string;  
  customName: string;  
  application: string; // e.g., 'slack', 'google\_sheets'  
  createdAt: Date;  
}
