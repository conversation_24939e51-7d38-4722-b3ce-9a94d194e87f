# **2\. High Level Architecture**

This section establishes the foundational architectural decisions for sflow, based on the requirements in the PRD.

## **Platform and Infrastructure Choice**

The architecture will be **cloud-agnostic**, designed to be deployed on any major cloud provider that supports **containerization**. For object storage, we will standardize on an **S3-compatible API**, allowing us to use services like AWS S3, Cloudflare R2, or others without code changes \[cite: PRD.md\]. The Next.js frontend will be hosted on Vercel to leverage its performance and seamless integration.

## **High Level Architecture Diagram**

graph TD  
    subgraph User  
        U\[Business User\]  
    end

    subgraph "Browser"  
        Frontend\[Next.js Frontend on Vercel\]  
    end

    subgraph "Cloud Provider"  
        API\[API Gateway\]  
        subgraph "Backend Services (Container Platform)"  
            Auth\[Auth Service\<br\>(Fastify, better-auth)\]  
            Agent\[Agent Service\<br\>(Fastify, Prisma)\]  
            KB\[Knowledge Base Service\<br\>(Fastify, S3 API)\]  
            Connector\[Connector Service\<br\>(Fastify, iPaaS)\]  
            Deploy\[Deployment Service\]  
        end  
        DB\[(Managed PostgreSQL DB)\]  
        S3\[(S3-Compatible Storage)\]  
    end

    subgraph "Third-Party Services"  
        iPaaS\[Pipedream Connect\]  
        LLM\[OpenRouter API\]  
        Email\[ReSend API\]  
        Hosting\[Static Hosting\<br\>(e.g., Vercel)\]  
    end

    U \--\> Frontend  
    Frontend \-- "API Calls (Axios)" \--\> API  
    API \--\> Auth & Agent & KB & Connector & Deploy  
    Auth \-- "OTP Codes" \--\> Email  
    Agent \--\> DB  
    KB \-- "Stores/Retrieves Docs" \--\> S3  
    Connector \--\> iPaaS  
    Agent \-- "Generates Answers" \--\> LLM  
    Deploy \--\> Hosting  
    Deploy \-- "Registers Routes" \--\> API

## **Architectural Patterns**

* **Microservices**: The backend will be composed of small, independent services (e.g., Authentication, Agents, Knowledge Base) that communicate over APIs, aligning with the PRD's assumption \[cite: PRD.md\]. This enhances scalability and maintainability.  
* **API Gateway**: A single, unified entry point for all frontend requests. This simplifies frontend development and centralizes concerns like authentication, rate limiting, and logging.  
* **Repository Pattern**: The backend services will use the repository pattern to abstract data access logic from the business logic. This improves testability and makes it easier to manage database interactions.  
* **Component-Based UI**: The Next.js frontend will be built using reusable React components, as facilitated by shadcn/ui \[cite: PRD.md\]. This ensures a consistent and maintainable user interface.  
* **RAG (Retrieval-Augmented Generation)**: The core AI functionality will use the RAG pattern to retrieve relevant information from the project-specific Knowledge Base and inject it into the LLM prompt for context-aware generation \[cite: PRD.md\].
