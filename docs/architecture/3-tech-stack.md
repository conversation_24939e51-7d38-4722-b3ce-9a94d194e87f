# **3\. <PERSON> Stack**

This table outlines the specific technologies and versions that will be used to build sflow. All development must adhere to this stack.

| Category                 | Technology            | Version           | Purpose                                                       | Rationale                                                                                                                   |
| :----------------------- | :-------------------- | :---------------- | :------------------------------------------------------------ | :-------------------------------------------------------------------------------------------------------------------------- |
| **Frontend Language**    | TypeScript            | \~5.4.5           | Adds static typing to JavaScript for the frontend.            | Improves code quality, maintainability, and developer experience.                                                           |
| **Frontend Framework**   | Next.js               | 15+ Latest stable | The core React framework for the user interface.              | Enables server-side rendering, static site generation, and a great developer experience \[cite: PRD.md\].                   |
| **State Management**     | Zustand               | \~4.5.2           | A small, fast, and scalable state-management solution.        | Provides a simple and un-opinionated API for managing state in React.                                                       |
| **API Client**           | Axios                 | \~1.7.2           | A promise-based HTTP client for the browser and Node.js.      | A robust and widely-used standard for making API requests from the frontend.                                                |
| **UI Component Library** | shadcn/ui             | \~0.8.0           | Provides a set of reusable, accessible UI components.         | Accelerates UI development with high-quality, customizable components built on Tailwind CSS \[cite: PRD.md\].               |
| **Backend Framework**    | Fastify               | 5+ latest stable  | A high-performance Node.js framework for the backend.         | Chosen for its speed, low overhead, and powerful plugin architecture, suitable for microservices \[cite: PRD.md\].          |
| **Database ORM**         | Prisma                | 6+ Latest Stable  | The ORM for interacting with the PostgreSQL database.         | Provides excellent type-safety and simplifies database queries, migrations, and management \[cite: PRD.md\].                |
| **Database**             | PostgreSQL using Neon | 16                | The primary relational database for the application.          | A robust, open-source SQL database known for reliability and a rich feature set \[cite: PRD.md\].                           |
| **Vector Storage**       | pgvector              | \~0.7.0           | PostgreSQL extension for vector similarity search.            | Enables efficient storage and querying of embeddings directly in our main database, simplifying the stack \[cite: PRD.md\]. |
| **Embedding Generation** | Sentence-Transformers | \~2.7.0           | A framework for state-of-the-art sentence embeddings.         | Provides high-quality, open-source models for generating embeddings locally without external API calls.                     |
| **Authentication**       | better-auth           | Latest            | Handles user authentication via OTP.                          | A specified requirement that provides a secure, passwordless login flow \[cite: PRD.md\].                                   |
| **Transactional Email**  | ReSend                | Latest            | Sends transactional emails for OTP codes and notifications.   | A modern email API for developers that simplifies sending emails.                                                           |
| **Monorepo Tool**        | Turborepo             | \~1.13.3          | Manages the monorepo and optimizes build/test pipelines.      | A high-performance build system that is ideal for managing separate frontend and backend packages.                          |
| **Testing**              | Jest & Playwright     | \~29.7.0          | Tools for unit, integration, and end-to-end testing.          | Jest is a standard for JS testing; Playwright provides robust, modern E2E testing across browsers \[cite: PRD.md\].         |
| **IaC Tool**             | Terraform             | \~1.8.0           | Infrastructure as Code for provisioning cloud resources.      | A cloud-agnostic, widely adopted standard for defining and managing infrastructure declaratively.                           |
| **Logging**              | Pino                  | \~9.0.0           | A high-performance, JSON-based logger.                        | The recommended logger for Fastify, providing fast and structured logging with minimal overhead.                            |
