# **8\. Database Schema**

This SQL script defines the tables, relationships, and indexes for the PostgreSQL database, including support for the pgvector extension.

\-- Enable the pgvector extension  
CREATE EXTENSION IF NOT EXISTS vector;

\-- Workspace Table  
CREATE TABLE "Workspace" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "name" TEXT NOT NULL,  
    "logoUrl" TEXT,  
    "primaryColor" TEXT,  
    "billingPlan" TEXT NOT NULL DEFAULT 'free',  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL  
);

\-- User Table  
CREATE TABLE "User" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "email" TEXT NOT NULL UNIQUE,  
    "name" TEXT,  
    "phone" TEXT,  
    "country" TEXT,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL  
);

\-- WorkspaceMembers Join Table  
CREATE TABLE "WorkspaceMembers" (  
    "userId" UUID NOT NULL,  
    "workspaceId" UUID NOT NULL,  
    "role" TEXT NOT NULL DEFAULT 'MEMBER',  
    PRIMARY KEY ("userId", "workspaceId"),  
    FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE,  
    FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE  
);

\-- Project Table  
CREATE TABLE "Project" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "name" TEXT NOT NULL,  
    "description" TEXT,  
    "accessType" TEXT NOT NULL DEFAULT 'personal',  
    "workspaceId" UUID NOT NULL,  
    "creatorId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL,  
    FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE,  
    FOREIGN KEY ("creatorId") REFERENCES "User"("id") ON DELETE SET NULL  
);

\-- Agent Table  
CREATE TABLE "Agent" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "title" TEXT NOT NULL,  
    "workflowJson" JSONB NOT NULL,  
    "triggerType" TEXT,  
    "projectId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL,  
    FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE  
);

\-- AgentVersion Table  
CREATE TABLE "AgentVersion" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "workflowJson" JSONB NOT NULL,  
    "versionNote" TEXT,  
    "agentId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    FOREIGN KEY ("agentId") REFERENCES "Agent"("id") ON DELETE CASCADE  
);

\-- KnowledgeBaseDocument Table  
CREATE TABLE "KnowledgeBaseDocument" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "fileName" TEXT NOT NULL,  
    "fileType" TEXT NOT NULL,  
    "status" TEXT NOT NULL DEFAULT 'pending',  
    "workspaceId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL,  
    FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE  
);

\-- ProjectDocuments Join Table  
CREATE TABLE "ProjectDocuments" (  
    "projectId" UUID NOT NULL,  
    "documentId" UUID NOT NULL,  
    PRIMARY KEY ("projectId", "documentId"),  
    FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE,  
    FOREIGN KEY ("documentId") REFERENCES "KnowledgeBaseDocument"("id") ON DELETE CASCADE  
);

\-- DocumentChunk Table  
CREATE TABLE "DocumentChunk" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "chunkText" TEXT NOT NULL,  
    "embedding" vector(384), \-- Dimension for all-MiniLM-L6-v2 model  
    "documentId" UUID NOT NULL,  
    FOREIGN KEY ("documentId") REFERENCES "KnowledgeBaseDocument"("id") ON DELETE CASCADE  
);

\-- Vector Index  
CREATE INDEX ON "DocumentChunk" USING HNSW ("embedding" vector\_l2\_ops);

\-- Connection Table  
CREATE TABLE "Connection" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "customName" TEXT NOT NULL,  
    "application" TEXT NOT NULL,  
    "encryptedCredentials" TEXT NOT NULL,  
    "userId" UUID NOT NULL,  
    "workspaceId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL,  
    FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE,  
    FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE  
);
