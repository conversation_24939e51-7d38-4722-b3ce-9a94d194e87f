# **Product Requirements Document (PRD)**

## **Goals and Background Context**

### **Goals**

* Launch the feature-rich MVP, including the Core Visual Editor, AI-Powered Scaffolding, and initial Template Library, by the end of Q4 2025\.  
* Acquire an initial user base of 1,000 active individual creators and secure 10 enterprise pilot teams within the first six months post-launch.  
* Validate the enterprise value proposition by converting at least 3 of the pilot teams to paid subscription plans.  
* Empower business users with a simple, powerful tool that allows them to build and deploy valuable AI agents in under 15 minutes, without developer dependency.

### **Background Context**

Currently, business domain experts are blocked by a "technical wall" when trying to implement their ideas for AI-powered automation, leading to slow innovation cycles. Existing tools are either too complex for non-technical users or lack the flexibility needed for custom workflows.

sflow solves this by providing a multi-modal, no-code platform designed specifically for the business user. Through a combination of a powerful visual block editor, AI-powered agent scaffolding, and a library of pre-built templates, sflow will democratize AI agent creation, allowing users to rapidly build, test, and deploy their own custom solutions.

### **Change Log**

| Date       | Version | Description                                                           | Author                |
| :--------- | :------ | :-------------------------------------------------------------------- | :-------------------- |
| 2025-07-16 | 1.2     | Added Project-based organization for agents, teams, and knowledge.    | Gemini                |
| 2025-07-16 | 1.1     | Added Settings (Teams, Branding, Billing) and Multi-Language Support. | Gemini                |
| 2025-07-16 | 1.0     | Initial PRD draft created from Project Brief.                         | John, Product Manager |

## **Requirements**

### **Functional**

1. **FR1:** The system shall provide a visual, block-based editor for users to define and assemble agent workflows.  
2. **FR2:** The editor shall support a slash command (/) to invoke and create different types of blocks, including:  
   * **Logic:** If-Else, Loop  
   * **I/O:** Input, Ask a Human  
   * **Execution:** Tool, Generation, Action  
   * **Workflow:**  
     * **Trigger:** Defines what externally starts the agent (e.g., an incoming WhatsApp message, an API call).  
     * **Comment:** A note-taking block that is invisible to the LLM during execution.  
3. **FR3:** The system shall provide an "AI-Powered Scaffolding" feature where a user can describe a goal in natural language and receive a generated draft of an agent workflow.  
4. **FR4:** The system shall offer a library of pre-built agent templates, categorized by business function (e.g., Sales, Marketing), that users can load and customize.  
5. **FR5:** The system shall allow users to upload documents (PDF, DOCX, TXT) into a secure, central Knowledge Base for the workspace.  
6. **FR6:** Users shall be able to reference variables and data sources using a namespaced @mention syntax (e.g., @KnowledgeBaseName:FileName.pdf, @Input:VariableName). The system will provide a UI-assisted menu to prevent ambiguity.  
7. **FR7:** The system shall provide a guided Connector Wizard (invoked via the /action command) for connecting to an initial, curated set of **10 essential business applications**. A single connection can be named and reused for multiple actions.  
8. **FR8:** Agents must be configurable to be triggered by events from connected external applications.  
9. **FR9:** Users shall be able to deploy a completed agent as a secure API endpoint.  
10. **FR10:** Users shall be able to deploy a completed agent as a simple, interactive web page.  
11. **FR11:** The system shall automatically save a new, viewable version of an agent every time a user performs a **"Deploy" action**.  
12. **FR12:** The system shall support project-level governance, allowing projects to be designated as "Personal" or "Company-Wide" with Role-Based Access Control (RBAC).  
13. **FR13:** The system shall allow workspace administrators to invite new users to their team via email and assign roles (e.g., Admin, Member).  
14. **FR14:** The system shall allow workspace administrators to customize the workspace with a company logo and primary color scheme.  
15. **FR15:** The system shall provide a dashboard for workspace administrators to view key usage metrics and their current billing plan.  
16. **FR16:** The system shall support multiple languages, initially English (LTR) and Arabic (RTL).  
17. **FR17:** The system shall allow users to create and manage Projects as containers for agents, team members, and associated knowledge.  
18. **FR18:** Each project shall have its own settings, including the ability to assign specific team members and select specific Knowledge Base documents for use by all agents within that project.

### **Non-Functional**

1. **NFR1:** The user interface shall be intuitive enough for a new user to build and deploy their first functional agent in under 15 minutes.  
2. **NFR2:** The editor interface shall remain responsive, with interaction latency under 200ms, even when displaying agent workflows with up to 50 blocks.  
3. **NFR3:** The architecture must enforce strict data isolation between tenants, ensuring a user's agents and Knowledge Base data are inaccessible to any other tenant.  
4. **NFR4:** The system must be built using the specified technology stack: Next.js frontend and a Fastify/Prisma backend with PostgreSQL.  
5. **NFR5:** The backend infrastructure must be architected to handle the concurrent execution of at least 100 agents during the MVP phase.

## **User Interface Design Goals**

### **Overall UX Vision**

The user experience will be simple, fluid, and intuitive, designed to feel more like "teaching an assistant" than "programming a machine." The interface must be discoverable, progressively revealing complexity through context-aware menus rather than requiring users to memorize commands.

### **Key Interaction Paradigms**

* **Block-Based Editor:** The core creation experience will be a Notion-like, block-based editor.  
* **Slash (/) Commands:** All actions and new blocks will be created via a slash command menu.  
* **@ Mentions:** All variables, inputs, and knowledge base files will be referenced via an @mention menu.  
* **Side-Drawer for Configuration:** Any block or action requiring detailed configuration will consistently use a side-drawer or modal.

### **Core Screens and Views**

* **Dashboard:** A central hub to view, manage, and organize **projects**.  
* **Project View:** A dedicated dashboard for a single project, showing its agents, assigned members, and linked knowledge bases.  
* **Agent Editor:** The primary workspace for building and testing agents, accessed from within a project.  
* **Knowledge Base View:** A screen for uploading and managing documents in the central workspace repository.  
* **Deployed Agent View:** The simple web page output for agents deployed as a UI.  
* **Workspace Settings View:** A dedicated area for administrators containing global settings for Team Management (invites and roles), Branding (logo/color customization), and Billing (usage metrics and subscription plan).  
* **Project Settings View:** An interface within a project to manage project-specific members and associated Knowledge Base documents.

### **Accessibility**

The application will be designed to meet **WCAG 2.1 AA** standards.

### **Target Device and Platforms**

The application will be a **Web Responsive** platform, designed for modern desktop browsers with a mobile-first approach.

## **Technical Assumptions**

* **Frontend:**  
  * **Framework:** Next.js  
  * **Language:** React / TypeScript  
  * **Block Editor Library:** Blocknote.js  
  * **UI Components:** shadcn/ui  
  * **Styling:** Tailwind CSS  
* **Backend:**  
  * **Framework:** Fastify  
  * **Database ORM:** Prisma  
  * **Authentication:** better-auth (configured for OTP)  
  * **Database:** PostgreSQL  
* **Repository Structure:** A monorepo will be used, but with **strict separation** (no shared code) between the frontend and backend packages to ensure independent deployment.  
* **Service Architecture:** A containerized, microservices-style architecture is assumed.  
* **Testing Requirements:** A comprehensive strategy including Unit, Integration, and End-to-End (E2E) tests is required.

## **Epic 1: Foundation & Core Editor Experience**

**Epic Goal:** This epic lays the essential groundwork for the entire sflow platform. By the end of this epic, a user will be able to register, log in, see a dashboard of projects, and use a basic editor in English or Arabic.

### **Story 1.1: Project Scaffolding**

**As a** developer, **I want** the monorepo containing the initial Next.js frontend and Fastify backend applications to be set up, **so that** I have a clean, version-controlled foundation to start building features.

**Acceptance Criteria:**

1. The monorepo structure is created with distinct, independent packages for the frontend and backend applications.  
2. The Next.js application is initialized with the chosen stack (TypeScript, Tailwind CSS, shadcn/ui).  
3. The Fastify application is initialized with the chosen stack (TypeScript, Prisma).  
4. Basic dev scripts are configured to run both applications concurrently.

### **Story 1.2: User Authentication (OTP)**

**As a** user, **I want** to sign up or log in by entering my email address to receive a one-time password (OTP), **so that** I can access my account quickly and securely without a password.

**Acceptance Criteria:**

1. A user can enter their email address into a login field.  
2. The system sends an email containing a unique, single-use OTP code to that address.  
3. If the email address is new, a user account is created automatically upon the first login.  
4. Entering the OTP code successfully logs the user into the application and establishes a valid session.  
5. The OTP code expires after 5 minutes or after it has been used once.  
6. The better-auth library is configured to handle the OTP flow.  
7. A logged-in user has a clear option to log out, which invalidates their session.

### **Story 1.3: New User Onboarding**

**As a** new user, **I want** to be guided through a simple onboarding process after my first login, **so that** I can provide my basic profile information.

**Acceptance Criteria:**

1. Immediately after a user's first successful authentication via OTP, they are redirected to an onboarding screen.  
2. The screen prompts the user to enter their full name, phone number, and country.  
3. The phone number input must include a country code selector.  
4. The country input must be a searchable dropdown list.  
5. Submitting the form saves the information to the user's profile in the database.  
6. This onboarding flow is only shown once to each user.  
7. The user can optionally skip the onboarding flow.

### **Story 1.4: Core Editor Interface**

**As a** user, **I want** a basic editor interface powered by Blocknote.js, **so that** I can begin creating an agent by adding and arranging text blocks.

**Acceptance Criteria:**

1. The editor interface loads successfully.  
2. The user can add a new block of text.  
3. The user can type and edit text within a block.  
4. The user can re-order blocks using drag-and-drop.  
5. The editor uses the Blocknote.js library.

### **Story 1.5: Create and Save Agent**

**As a** user, **I want** to be able to create and save a named agent workflow **within a project**, **so that** my work persists between sessions in an organized way.

**Acceptance Criteria:**

1. A "Create New Agent" button is available **within a project's view**.  
2. The user can provide a title for their new agent.  
3. Clicking a "Save" button sends the agent's content to a backend API endpoint.  
4. The agent is successfully saved in the database and associated with the logged-in user and the **current project**.

### **Story 1.6: User Dashboard (Project List)**

**As a** user, **I want** to see a list of my **projects** on a dashboard, **so that** I can easily access and organize my work.

**Acceptance Criteria:**

1. A dashboard page displays a list of all projects the logged-in user has access to.  
2. The list is fetched from a secure backend API endpoint.  
3. Each item in the list displays the project's title.  
4. Clicking on a project in the list opens the **Project View**.

### **Story 1.7: Typed Agent Inputs**

**As a** user, **I want** to define specific types for my agent's inputs using a configuration modal, **so that** the system can generate appropriate forms and validate data correctly.

**Acceptance Criteria:**

1. Using the /input command in the editor opens a side-drawer/modal for configuration.  
2. The user can select an input type from a list, including: Text, Number, True/False, Image, Audio, PDF/Document, CSV/Text, List, Object, and Select.  
3. The user must provide a name for the input (e.g., customer\_name).  
4. When an agent is deployed as a web page, the system uses these defined types to generate the correct HTML form fields.

### **Story 1.8: Internationalization (i18n) & RTL Support**

**As a** user, **I want** to use the application in either English or Arabic, **so that** I can work in my preferred language.

**Acceptance Criteria:**

1. A language switcher is available in the UI.  
2. All UI text is sourced from locale files to support translation.  
3. The UI layout correctly adapts to a Right-to-Left (RTL) orientation when Arabic is selected.  
4. The initial implementation includes complete translations for English and Arabic.

## **Epic 2: Project-Based Organization**

**Epic Goal:** To enable users to organize their work effectively by grouping related agents, team members, and knowledge resources into distinct, manageable projects.

### **Story 2.1: Create and Manage Projects**

**As a** user, **I want** to create, name, and delete projects from my main dashboard, **so that** I can establish separate workspaces for different initiatives.

**Acceptance Criteria:**

1. The main user dashboard displays a "Create New Project" button.  
2. Users can provide a name and an optional description for a new project.  
3. The dashboard displays a list or grid of all projects the user has access to.  
4. Each project listed has an option to delete it (with confirmation).

### **Story 2.2: Project Dashboard View**

**As a** user, **I want** to click on a project and see a dedicated view listing all the agents, team members, and resources associated with it, **so that** I have a clear overview of the project's contents.

**Acceptance Criteria:**

1. Clicking a project on the main dashboard navigates to a dedicated Project Dashboard.  
2. This dashboard contains a list of all agents belonging to the project.  
3. A "Create New Agent" button exists within the Project Dashboard.  
4. The Project Dashboard also provides access to project-specific settings.

### **Story 2.3: Project-Level Member Management**

**As a** project administrator, **I want** to assign specific team members from my workspace to my project, **so that** I can control who has access to the agents within that project.

**Acceptance Criteria:**

1. A "Project Settings" area includes a "Members" tab.  
2. The "Members" tab allows the project admin to add or remove users from a list of all available workspace members.  
3. Only members assigned to a project can view or edit its agents.

### **Story 2.4: Project-Level Knowledge Base Association**

**As a** project administrator, **I want** to select which documents from the central Knowledge Base are available to this project, **so that** I can scope the context for all agents within the project and ensure data relevance.

**Acceptance Criteria:**

1. "Project Settings" includes a "Knowledge Base" tab.  
2. This tab displays a list of all documents available in the workspace's central Knowledge Base.  
3. The user can check/uncheck documents to include/exclude them from the project's scope.  
4. When using the @mention feature inside an agent, only documents associated with the current project are suggested and available for use.

## **Epic 3: Knowledge Base & Core AI**

**Epic Goal:** This epic brings intelligence to the agents. By the end of this epic, a user will be able to upload documents and reference that information within an agent, using the context of the agent's project.

### **Story 3.1: Knowledge Base \- File Upload**

**As a** user, **I want** a dedicated page where I can upload my documents (PDF, TXT, DOCX), **so that** I can create a personal knowledge repository for my agents to use.

**Acceptance Criteria:**

1. A "Knowledge Base" section is available in the application.  
2. It contains a file upload component that accepts PDF, TXT, and DOCX file types.  
3. An "Upload" button sends the selected files to a secure backend endpoint.  
4. A visual indicator shows the progress of the file uploads.

### **Story 3.2: Backend \- Document Processing and Vectorization**

**As a** developer, **I want** the backend to securely process an uploaded document by extracting its text, chunking it, creating vector embeddings, and storing them, **so that** the document's contents are ready for retrieval.

**Acceptance Criteria:**

1. A secure backend endpoint accepts file uploads and associates them with the current user.  
2. The system correctly parses text content from PDF, DOCX, and TXT files.  
3. The extracted text is split into logical, indexed chunks.  
4. Vector embeddings are generated for each text chunk.  
5. The text chunks and their corresponding embeddings are stored in the PostgreSQL database and linked to the specific document and user.

### **Story 3.3: Knowledge Base \- File Management**

**As a** user, **I want** to see a list of all the documents I've uploaded and have the ability to delete them, **so that** I can manage my knowledge repository.

**Acceptance Criteria:**

1. The "Knowledge Base" page displays a list of all documents uploaded by the user.  
2. Each document in the list has a "Delete" option.  
3. Upon confirmation, the document and all its associated data are permanently removed.

### **Story 3.4: Editor \- Referencing the Project Knowledge Base**

**As a** user, **I want** to easily reference documents made available to my project, **so that** I can instruct an agent to use specific information.

**Acceptance Criteria:**

1. Typing @ in any text-input area of a block opens a context menu.  
2. The menu lists "Knowledge Base" as an option.  
3. Selecting "Knowledge Base" displays a searchable list of documents **that have been associated with the agent's current project**.  
4. Selecting a file inserts a formatted reference (e.g., @KnowledgeBase:Report-Q3.pdf) into the editor.

### **Story 3.5: Core AI \- The Generation Block with RAG**

**As a** user, **I want** to use a configurable Generation block that understands my Knowledge Base references, **so that** my agent can generate answers based on my private data using my preferred AI model.

**Acceptance Criteria:**

1. Invoking the /generation command adds a "Generation" block and opens a side-drawer/modal for configuration.  
2. The configuration modal allows the user to select an LLM provider and a specific model.  
3. The configuration modal includes a slider to set the 'temperature' parameter.  
4. When an agent is run, the backend parses any @KnowledgeBase references in the prompt.  
5. The system performs a vector search on the specified document **(validating it is part of the project's knowledge)** to retrieve relevant text chunks.  
6. The retrieved chunks are added as context to the final prompt sent to the selected LLM.  
7. The LLM's response is displayed as the output of the Generation block.

## **Epic 4: External Connectivity & Automation**

**Epic Goal:** This epic transforms agents from internal data processors into active participants in external workflows. By the end of this epic, a user will be able to connect sflow to 10 essential business applications, reuse those connections for multiple steps, and have workflows started automatically by external events.

### **Story 4.1: Connector Backend Integration**

**As a** developer, **I want** to integrate a third-party integration platform (iPaaS) into the backend, **so that** sflow can leverage its pre-built library of application connectors and triggers.

**Acceptance Criteria:**

1. An iPaaS provider is selected and its SDK is integrated into the backend.  
2. Secure credentials for the iPaaS are managed via a secrets manager.  
3. The backend can list the initial 10 curated application connectors.  
4. The architecture for storing user-specific connection tokens is implemented.

### **Story 4.2: Connection Management & The Connector Wizard**

**As a** user, **I want** to create multiple, named connections to external apps and reuse them, **so that** I can manage different accounts and not have to re-authenticate for every action.

**Acceptance Criteria:**

1. When creating a new connection, the user must be prompted to provide a custom name (e.g., "My Work Slack").  
2. The system allows a user to save multiple connections for the same application, each with a unique name.  
3. The /action side-drawer first displays a list of the user's existing, named connections, plus a "Create New Connection" option.  
4. Upon successful authorization, the new, named connection is stored and added to the user's list.

### **Story 4.3: Configuring and Executing Actions**

**As a** user, **I want** to select a connection and then choose a specific action to perform with it, **so that** my agent can interact with other systems in a flexible way.

**Acceptance Criteria:**

1. Upon selecting a named connection, the user is presented with a list of available actions for that application.  
2. The user can select an action and configure its inputs within the side-drawer/modal.  
3. The configured action is saved as part of the Action block.  
4. When the agent is run, the backend executes the action using the correct connection token.

### **Story 4.4: Configuring a Trigger**

**As a** user, **I want** to use my existing connections to set up triggers, **so that** my automated workflows are easy to configure.

**Acceptance Criteria:**

1. A /trigger command adds a Trigger block to the start of a workflow.  
2. Configuring the block prompts the user to select an existing named connection or create a new one.  
3. After selecting a connection, the wizard displays a list of available trigger events for that application.  
4. Successfully configuring a trigger registers a listener via the iPaaS.

### **Story 4.5: Backend \- Triggered Agent Execution**

**As a** developer, **I want** the backend to listen for incoming trigger events from the iPaaS and start the correct agent run, **so that** event-driven automation is reliable and secure.

**Acceptance Criteria:**

1. A secure API endpoint is created to receive incoming webhook events.  
2. The incoming event's signature is validated.  
3. The system correctly identifies the agent associated with the incoming trigger.  
4. A new run of the agent is initiated, with the event payload passed as input.

## **Epic 5: Agent Deployment & Governance**

**Epic Goal:** This epic makes agents useful in the real world and provides essential control for teams. By the end of this epic, users will be able to deploy their agents as either an API or a web page, have confidence in their work via version control, and operate within a clear governance structure.

### **Story 5.1: Deploy Agent as an API**

**As a** user, **I want** a "Deploy" button that generates a secure API endpoint for my agent, **so that** other applications can call it programmatically.

**Acceptance Criteria:**

1. A "Deploy" button is present in the editor interface.  
2. The deployment process generates a unique, secure URL for the agent's API endpoint.  
3. The system generates a unique API key for authentication.  
4. Upon successful deployment, the system generates and displays API documentation, including the endpoint URL, a description of the required inputs and their types, and code examples for calling the API.

### **Story 5.2: Deploy Agent as a Web Page**

**As a** user, **I want** to deploy my agent as a simple, clean web page, **so that** non-technical users can interact with it through a user-friendly form.

**Acceptance Criteria:**

1. The deployment process offers an option to deploy as a "Web Page".  
2. A unique, public URL is generated for the web page.  
3. The web page automatically generates a simple form based on the typed Input blocks defined in the agent.  
4. A user can fill in the form fields and submit them to run the agent.  
5. The final output of the agent run is displayed on the page.

### **Story 5.3: Version History on Deploy**

**As a** user, **I want** the system to automatically save a snapshot of my agent every time I deploy it, **so that** I have a complete version history of my work.

**Acceptance Criteria:**

1. Successfully deploying an agent creates a new, immutable, timestamped version record.  
2. The user can optionally add a name or note to each version upon deployment.  
3. A "History" tab in the editor displays a list of all previously deployed versions.  
4. The user can view the read-only configuration of any past version.

### **Story 5.4: Revert to a Previous Version**

**As a** user, **I want** to be able to revert my agent's configuration to a previous version from the history, **so that** I can quickly roll back any unintended changes.

**Acceptance Criteria:**

1. Each version in the "History" tab has a "Revert to this version" button.  
2. Clicking this button loads the selected version's configuration into the editor as the new current draft.  
3. The user must explicitly click "Save" or "Deploy" for the reverted draft to become the new active configuration.

### **Story 5.5: Agent Governance & Permissions**

**As an** administrator, **I want** to manage different agent tiers and control who can access them, **so that** I can ensure quality and security within my organization.

**Acceptance Criteria:**

1. An administration dashboard exists for managing users and agents.  
2. Agents can be designated as either "Personal" or "Company-Wide".  
3. For a "Company-Wide" agent, an administrator can assign access permissions to specific users or groups.  
4. The administrator has a global setting to disable the creation and/or execution of all "Personal" agents.

## **Epic 6: Workspace Settings & Administration**

**Epic Goal:** This epic provides administrators with the global tools to manage their team, customize the platform's appearance, and monitor the account's usage and billing status.

### **Story 6.1: Team Member Management**

**As an** administrator, **I want** to invite new members to my team via email and manage their roles, **so that** I can collaborate with colleagues and control their permissions.

**Acceptance Criteria:**

1. A "Team" settings page displays a list of current members and their roles (Admin, Member).  
2. An "Invite Member" feature allows an admin to send an email invitation to a new user.  
3. The email contains a unique link to join the team.  
4. Upon accepting, the new user is added to the team list.  
5. Administrators can change a member's role or remove them from the team.

### **Story 6.2: Custom Branding**

**As an** administrator, **I want** to upload a custom logo and set a primary color, **so that** the sflow UI and deployed web pages align with my company's brand.

**Acceptance Criteria:**

1. A "Branding" settings page provides fields to upload a logo image file and select a primary color.  
2. The uploaded logo replaces the default sflow logo in the main application header.  
3. The primary color is applied to key UI elements like buttons and links.  
4. Deployed agents (as Web Pages) also reflect the custom logo and color scheme.

### **Story 6.3: Billing & Usage Dashboard**

**As an** administrator, **I want** to view my team's current usage metrics and subscription plan, **so that** I can monitor costs and activity.

**Acceptance Criteria:**

1. A "Billing" settings page is available to administrators.  
2. The page clearly displays the team's current subscription plan (e.g., "Enterprise Pilot").  
3. The page displays key usage metrics, such as "Agent Runs this month," "Number of Active Agents," and "Knowledge Base Size."  
4. Usage data is presented clearly with visualizations.

