# Authentication Architecture

This document outlines the authentication approach using Better Auth in the sflow application.

## Overview

Better Auth handles all authentication, session management, and security. We follow their basic usage patterns without adding custom authentication logic.

## Key Principles

1. **Let Better Auth handle everything** - Sessions, cookies, security are all managed by Better Auth
2. **No custom auth logic** - Don't implement custom session checks or token management
3. **Use cookies for authentication** - Better Auth uses httpOnly cookies by default

## Implementation Patterns

### 1. API Endpoints (Server-side)

For protected API endpoints, use Better Auth's session API:

```typescript
// In your Fastify route handler
fastify.post('/api/v1/protected-route', async (request, reply) => {
  // Get session using Better Auth
  const session = await auth.api.getSession({ headers: request.headers })
  
  if (!session?.user) {
    return reply.status(401).send({ error: 'Unauthorized' })
  }
  
  // Use session.user for user data
  const userId = session.user.id
  // ... rest of your logic
})
```

### 2. Client-side API Calls

Configure axios to send cookies with all requests:

```typescript
// lib/api.ts
import axios from 'axios'

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  withCredentials: true, // CRITICAL: This ensures cookies are sent
})

export default api
```

### 3. Protected Pages (App Directory)

For protected pages in Next.js App Router, create a simple layout that checks authentication:

```typescript
// app/(protected)/layout.tsx
export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Authentication is handled by middleware
  // No need for session checks here
  return <>{children}</>
}
```

The actual protection happens in middleware.ts:

```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  
  // Skip middleware for public routes
  if (
    pathname.startsWith('/api') ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/login') ||
    pathname.startsWith('/favicon')
  ) {
    return NextResponse.next()
  }

  // Check for Better Auth session cookie
  const sessionCookie = request.cookies.get('better-auth.session_token')
  
  if (!sessionCookie?.value) {
    // No session, redirect to login
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}
```

### 4. Creating New Protected Pages

To add a new protected page:

1. Create your page under a protected route group (e.g., `app/(protected)/dashboard/page.tsx`)
2. The middleware automatically protects it
3. No additional auth code needed in the component

Example:
```typescript
// app/(protected)/settings/page.tsx
export default function SettingsPage() {
  // No auth checks needed here - middleware handles it
  return (
    <div>
      <h1>Settings</h1>
      {/* Your settings UI */}
    </div>
  )
}
```

### 5. Accessing User Data in Components

Use Better Auth's hooks:

```typescript
// In a client component
'use client'
import { useSession } from '@/lib/auth-client'

export function UserProfile() {
  const { data: session } = useSession()
  
  if (!session?.user) {
    return null // This shouldn't happen on protected pages
  }
  
  return <div>Welcome, {session.user.email}</div>
}
```

## Common Pitfalls to Avoid

1. **Don't implement custom session checks** - Better Auth handles this
2. **Don't store tokens manually** - Better Auth uses httpOnly cookies
3. **Don't add Authorization headers** - Cookies are sent automatically
4. **Don't create custom auth middleware** - Use the pattern above

## CORS Configuration

Ensure your API allows credentials:

```typescript
// In your Fastify server
fastify.register(cors, {
  origin: ['http://localhost:3000'], // Your frontend URL
  credentials: true, // CRITICAL: Allow cookies
})
```

## Summary

The authentication flow is:
1. User logs in via Better Auth
2. Better Auth sets httpOnly session cookie
3. All API requests include this cookie automatically (withCredentials: true)
4. API endpoints verify session with `auth.api.getSession()`
5. Middleware protects routes by checking for session cookie

This approach is secure, simple, and follows Better Auth's recommended patterns.