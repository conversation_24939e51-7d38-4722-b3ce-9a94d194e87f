
## **1\. Introduction**

This document outlines the complete fullstack architecture for sflow, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack. This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

## **Starter Template or Existing Project**

The project will use **Turborepo** as a high-performance build system for managing the JavaScript/TypeScript monorepo. This provides a solid, scalable foundation that aligns with the choice of Next.js for the frontend.

### **Change Log**

| Date | Version | Description | Author |
| :---- | :---- | :---- | :---- |
| 2025-07-16 | 1.0 | Initial architecture document draft. | Winston, Architect |
| \<br\> |  |  |  |

## **2\. High Level Architecture**

This section establishes the foundational architectural decisions for sflow, based on the requirements in the PRD.

### **Platform and Infrastructure Choice**

The architecture will be **cloud-agnostic**, designed to be deployed on any major cloud provider that supports **containerization**. For object storage, we will standardize on an **S3-compatible API**, allowing us to use services like AWS S3, Cloudflare R2, or others without code changes \[cite: PRD.md\]. The Next.js frontend will be hosted on Vercel to leverage its performance and seamless integration.

### **High Level Architecture Diagram**

graph TD  
    subgraph User  
        U\[Business User\]  
    end

    subgraph "Browser"  
        Frontend\[Next.js Frontend on Vercel\]  
    end

    subgraph "Cloud Provider"  
        API\[API Gateway\]  
        subgraph "Backend Services (Container Platform)"  
            Auth\[Auth Service\<br\>(Fastify, better-auth)\]  
            Agent\[Agent Service\<br\>(Fastify, Prisma)\]  
            KB\[Knowledge Base Service\<br\>(Fastify, S3 API)\]  
            Connector\[Connector Service\<br\>(Fastify, iPaaS)\]  
            Deploy\[Deployment Service\]  
        end  
        DB\[(Managed PostgreSQL DB)\]  
        S3\[(S3-Compatible Storage)\]  
    end

    subgraph "Third-Party Services"  
        iPaaS\[Pipedream Connect\]  
        LLM\[OpenRouter API\]  
        Email\[ReSend API\]  
        Hosting\[Static Hosting\<br\>(e.g., Vercel)\]  
    end

    U \--\> Frontend  
    Frontend \-- "API Calls (Axios)" \--\> API  
    API \--\> Auth & Agent & KB & Connector & Deploy  
    Auth \-- "OTP Codes" \--\> Email  
    Agent \--\> DB  
    KB \-- "Stores/Retrieves Docs" \--\> S3  
    Connector \--\> iPaaS  
    Agent \-- "Generates Answers" \--\> LLM  
    Deploy \--\> Hosting  
    Deploy \-- "Registers Routes" \--\> API

### **Architectural Patterns**

* **Microservices**: The backend will be composed of small, independent services (e.g., Authentication, Agents, Knowledge Base) that communicate over APIs, aligning with the PRD's assumption \[cite: PRD.md\]. This enhances scalability and maintainability.  
* **API Gateway**: A single, unified entry point for all frontend requests. This simplifies frontend development and centralizes concerns like authentication, rate limiting, and logging.  
* **Repository Pattern**: The backend services will use the repository pattern to abstract data access logic from the business logic. This improves testability and makes it easier to manage database interactions.  
* **Component-Based UI**: The Next.js frontend will be built using reusable React components, as facilitated by shadcn/ui \[cite: PRD.md\]. This ensures a consistent and maintainable user interface.  
* **RAG (Retrieval-Augmented Generation)**: The core AI functionality will use the RAG pattern to retrieve relevant information from the project-specific Knowledge Base and inject it into the LLM prompt for context-aware generation \[cite: PRD.md\].

## **3\. Tech Stack**

This table outlines the specific technologies and versions that will be used to build sflow. All development must adhere to this stack.

| Category                 | Technology            | Version           | Purpose                                                       | Rationale                                                                                                                   |
| :----------------------- | :-------------------- | :---------------- | :------------------------------------------------------------ | :-------------------------------------------------------------------------------------------------------------------------- |
| **Frontend Language**    | TypeScript            | \~5.4.5           | Adds static typing to JavaScript for the frontend.            | Improves code quality, maintainability, and developer experience.                                                           |
| **Frontend Framework**   | Next.js               | 15+ Latest stable | The core React framework for the user interface.              | Enables server-side rendering, static site generation, and a great developer experience \[cite: PRD.md\].                   |
| **State Management**     | Zustand               | \~4.5.2           | A small, fast, and scalable state-management solution.        | Provides a simple and un-opinionated API for managing state in React.                                                       |
| **API Client**           | Axios                 | \~1.7.2           | A promise-based HTTP client for the browser and Node.js.      | A robust and widely-used standard for making API requests from the frontend.                                                |
| **UI Component Library** | shadcn/ui             | \~0.8.0           | Provides a set of reusable, accessible UI components.         | Accelerates UI development with high-quality, customizable components built on Tailwind CSS \[cite: PRD.md\].               |
| **Backend Framework**    | Fastify               | 5+ latest stable  | A high-performance Node.js framework for the backend.         | Chosen for its speed, low overhead, and powerful plugin architecture, suitable for microservices \[cite: PRD.md\].          |
| **Database ORM**         | Prisma                | 6+ Latest Stable  | The ORM for interacting with the PostgreSQL database.         | Provides excellent type-safety and simplifies database queries, migrations, and management \[cite: PRD.md\].                |
| **Database**             | PostgreSQL using Neon | 16                | The primary relational database for the application.          | A robust, open-source SQL database known for reliability and a rich feature set \[cite: PRD.md\].                           |
| **Vector Storage**       | pgvector              | \~0.7.0           | PostgreSQL extension for vector similarity search.            | Enables efficient storage and querying of embeddings directly in our main database, simplifying the stack \[cite: PRD.md\]. |
| **Embedding Generation** | Sentence-Transformers | \~2.7.0           | A framework for state-of-the-art sentence embeddings.         | Provides high-quality, open-source models for generating embeddings locally without external API calls.                     |
| **Authentication**       | better-auth           | Latest            | Handles user authentication via OTP.                          | A specified requirement that provides a secure, passwordless login flow \[cite: PRD.md\].                                   |
| **Transactional Email**  | ReSend                | Latest            | Sends transactional emails for OTP codes and notifications.   | A modern email API for developers that simplifies sending emails.                                                           |
| **Monorepo Tool**        | Turborepo             | \~1.13.3          | Manages the monorepo and optimizes build/test pipelines.      | A high-performance build system that is ideal for managing separate frontend and backend packages.                          |
| **Testing**              | Jest & Playwright     | \~29.7.0          | Tools for unit, integration, and end-to-end testing.          | Jest is a standard for JS testing; Playwright provides robust, modern E2E testing across browsers \[cite: PRD.md\].         |
| **IaC Tool**             | Terraform             | \~1.8.0           | Infrastructure as Code for provisioning cloud resources.      | A cloud-agnostic, widely adopted standard for defining and managing infrastructure declaratively.                           |
| **Logging**              | Pino                  | \~9.0.0           | A high-performance, JSON-based logger.                        | The recommended logger for Fastify, providing fast and structured logging with minimal overhead.                            |

## **4\. Data Models**

* ### **1\. Workspace**

  * **Purpose**: Represents a top-level team or company account. It is the primary container for users, projects, and the central Knowledge Base.  
  * **TypeScript Interface**:

export interface Workspace {  
  id: string;  
  name: string;  
  logoUrl?: string;  
  primaryColor?: string;  
  billingPlan: 'free' | 'pro' | 'enterprise\_pilot';  
  createdAt: Date;  
  updatedAt: Date;  
}

* ### **2\. User**

  * **Purpose**: Represents an individual who can log in and interact with the platform.  
  * **TypeScript Interface**:

export interface User {  
  id: string;  
  email: string;  
  name?: string;  
  phone?: string;  
  country?: string;  
  createdAt: Date;  
}

* ### **3\. Project**

  * **Purpose**: A container within a workspace to organize related agents, team members, and knowledge resources \[cite: PRD.md\].  
  * **TypeScript Interface**:

export interface Project {  
  id: string;  
  workspaceId: string;  
  name: string;  
  description?: string;  
  accessType: 'personal' | 'company\_wide';  
  createdAt: Date;  
  updatedAt: Date;  
}

* ### **4\. Agent**

  * **Purpose**: The core executable workflow created by a user. Contains the blocks, logic, and configuration.  
  * **TypeScript Interface**:

export interface Agent {  
  id: string;  
  projectId: string;  
  title: string;  
  workflowJson: Record\<string, any\>;   
  triggerType: 'api' | 'web\_app' | 'external\_event';  
  createdAt: Date;  
  updatedAt: Date;  
}

* ### **5\. AgentVersion**

  * **Purpose**: A read-only, timestamped snapshot of an agent's workflow, created automatically every time a user deploys the agent \[cite: PRD.md\].  
  * **TypeScript Interface**:

export interface AgentVersion {  
  id: string;  
  agentId: string;  
  workflowJson: Record\<string, any\>;  
  versionNote?: string;  
  createdAt: Date;  
}

* ### **6\. KnowledgeBaseDocument**

  * **Purpose**: Represents a single document (PDF, DOCX, TXT) uploaded by a user to the central workspace repository \[cite: PRD.md\].  
  * **TypeScript Interface**:

export interface KnowledgeBaseDocument {  
  id: string;  
  workspaceId: string;  
  fileName: string;  
  fileType: string;  
  status: 'pending' | 'processing' | 'completed' | 'failed';  
  createdAt: Date;  
}

* ### **7\. Connection**

  * **Purpose**: Represents a user's named, authenticated connection to a single external business application \[cite: PRD.md\].  
  * **TypeScript Interface**:

export interface Connection {  
  id: string;  
  workspaceId: string;  
  customName: string;  
  application: string; // e.g., 'slack', 'google\_sheets'  
  createdAt: Date;  
}

## **5\. Components**

* **Frontend Application (sflow-web)**: Provides the entire user interface.  
* **API Gateway**: The single, secure entry point for all requests from the frontend.  
* **Auth Service**: Manages all aspects of user identity and authentication.  
* **Agent Service**: The core engine that handles CRUD operations for projects and agents, versioning, and execution.  
* **Knowledge Base Service**: Manages document uploads, processing, embedding, and retrieval for RAG.  
* **Connector Service**: Manages integrations with the Pipedream iPaaS platform.  
* **Deployment Service**: Manages the deployment of agents as either secure API endpoints or simple web pages \[cite: PRD.md\].

## **6\. External APIs**

* ### **OpenRouter API (LLM)**

  * **Purpose**: To provide access to a wide variety of large language models, powering the "Generation" block \[cite: PRD.md\].  
  * **Documentation**: https://openrouter.ai/docs  
  * **Authentication**: **API Key** sent in the Authorization: Bearer YOUR\_API\_KEY header.

* ### **Pipedream Connect API (iPaaS)**

  * **Purpose**: To provide the library of pre-built connectors to external business applications, enabling agent actions and triggers \[cite: PRD.md\].  
  * **Documentation**: https://pipedream.com/docs/  
  * **Authentication**: An **API Key** for the Pipedream account will be used by our backend.

* ### **ReSend API**

  * **Purpose**: To handle the sending of transactional emails, specifically the OTP codes \[cite: PRD.md\].  
  * **Documentation**: https://resend.com/docs/api-reference/introduction  
  * **Authentication**: **API Key** sent via Authorization: Bearer YOUR\_API\_KEY.

## **7\. Core Workflows**

* ### **Workflow 1: User Login (OTP)**

  * This diagram shows the process of a user signing up or logging in using a passwordless OTP code \[cite: PRD.md\].

sequenceDiagram  
    participant User  
    participant Frontend (sflow-web)  
    participant API Gateway  
    participant Auth Service  
    participant ReSend API

    User-\>\>+Frontend: Enters email and clicks "Login"  
    Frontend-\>\>+API Gateway: POST /auth/login (email)  
    API Gateway-\>\>+Auth Service: Forwards request  
    Auth Service-\>\>+ReSend API: Request to send OTP email  
    ReSend API--\>\>-Auth Service: Confirms email sent  
    Auth Service--\>\>-API Gateway: Responds with success message  
    API Gateway--\>\>-Frontend: Responds with success message  
    Frontend--\>\>-User: Displays "Check your email" message

    User-\>\>+Frontend: Enters OTP code from email  
    Frontend-\>\>+API Gateway: POST /auth/verify (token)  
    API Gateway-\>\>+Auth Service: Forwards request  
    Auth Service-\>\>Auth Service: Verifies token against database  
    Auth Service--\>\>-API Gateway: Responds with session token/cookie  
    API Gateway--\>\>-Frontend: Responds with session token/cookie  
    Frontend-\>\>Frontend: Establishes session, redirects to dashboard  
    Frontend--\>\>-User: Displays user dashboard

* ### **Workflow 2: Agent Execution with RAG**

  * This diagram illustrates how an agent uses the Knowledge Base to provide a context-aware answer from the LLM \[cite: PRD.md\].

sequenceDiagram  
    participant User  
    participant Frontend (sflow-web)  
    participant API Gateway  
    participant Agent Service  
    participant KB Service  
    participant Database (pgvector)  
    participant OpenRouter API

    User-\>\>+Frontend: Runs agent with a specific query  
    Frontend-\>\>+API Gateway: POST /agents/{id}/run (query)  
    API Gateway-\>\>+Agent Service: Forwards request  
    Note over Agent Service: Encounters a "Generation" block\<br\>with a @KnowledgeBase reference.  
    Agent Service-\>\>+KB Service: POST /search (query, document\_id)  
    KB Service-\>\>+Database (pgvector): Performs vector similarity search  
    Database (pgvector)--\>\>-KB Service: Returns relevant chunks.  
    KB Service--\>\>-Agent Service: Returns retrieved text chunks.  
    Note over Agent Service: Constructs final prompt with RAG context.  
    Agent Service-\>\>+OpenRouter API: POST /chat/completions (final\_prompt)  
    OpenRouter API--\>\>-Agent Service: Returns LLM-generated response.  
    Agent Service--\>\>-API Gateway: Responds with final agent output.  
    API Gateway--\>\>-Frontend: Responds with final agent output.  
    Frontend--\>\>-User: Displays the final answer.

* ### **Workflow 3: Executing an External Action**

  * This diagram shows how an agent executes an action in a third-party application \[cite: PRD.md\].

sequenceDiagram  
    participant User  
    participant Frontend (sflow-web)  
    participant API Gateway  
    participant Agent Service  
    participant Connector Service  
    participant Database  
    participant Pipedream API

    User-\>\>+Frontend: Runs an agent containing an "Action" block  
    Frontend-\>\>+API Gateway: POST /agents/{id}/run  
    API Gateway-\>\>+Agent Service: Forwards request  
    Note over Agent Service: Executes workflow, encounters "Action" block.  
    Agent Service-\>\>+Connector Service: POST /execute-action (action\_details, connection\_id)  
    Connector Service-\>\>+Database: Fetches encrypted credentials for the connection\_id  
    Database--\>\>-Connector Service: Returns encrypted credentials  
    Connector Service-\>\>+Pipedream API: Executes the specified action with credentials  
    Pipedream API--\>\>-Connector Service: Returns result of the action  
    Connector Service--\>\>-Agent Service: Returns action result  
    Agent Service--\>\>-API Gateway: Responds with final agent output  
    API Gateway--\>\>-Frontend: Responds with final agent output  
    Frontend--\>\>-User: Displays the final result

## **8\. Database Schema**

This SQL script defines the tables, relationships, and indexes for the PostgreSQL database, including support for the pgvector extension.

\-- Enable the pgvector extension  
CREATE EXTENSION IF NOT EXISTS vector;

\-- Workspace Table  
CREATE TABLE "Workspace" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "name" TEXT NOT NULL,  
    "logoUrl" TEXT,  
    "primaryColor" TEXT,  
    "billingPlan" TEXT NOT NULL DEFAULT 'free',  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL  
);

\-- User Table  
CREATE TABLE "User" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "email" TEXT NOT NULL UNIQUE,  
    "name" TEXT,  
    "phone" TEXT,  
    "country" TEXT,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL  
);

\-- WorkspaceMembers Join Table  
CREATE TABLE "WorkspaceMembers" (  
    "userId" UUID NOT NULL,  
    "workspaceId" UUID NOT NULL,  
    "role" TEXT NOT NULL DEFAULT 'MEMBER',  
    PRIMARY KEY ("userId", "workspaceId"),  
    FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE,  
    FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE  
);

\-- Project Table  
CREATE TABLE "Project" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "name" TEXT NOT NULL,  
    "description" TEXT,  
    "accessType" TEXT NOT NULL DEFAULT 'personal',  
    "workspaceId" UUID NOT NULL,  
    "creatorId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL,  
    FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE,  
    FOREIGN KEY ("creatorId") REFERENCES "User"("id") ON DELETE SET NULL  
);

\-- Agent Table  
CREATE TABLE "Agent" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "title" TEXT NOT NULL,  
    "workflowJson" JSONB NOT NULL,  
    "triggerType" TEXT,  
    "projectId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL,  
    FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE  
);

\-- AgentVersion Table  
CREATE TABLE "AgentVersion" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "workflowJson" JSONB NOT NULL,  
    "versionNote" TEXT,  
    "agentId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    FOREIGN KEY ("agentId") REFERENCES "Agent"("id") ON DELETE CASCADE  
);

\-- KnowledgeBaseDocument Table  
CREATE TABLE "KnowledgeBaseDocument" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "fileName" TEXT NOT NULL,  
    "fileType" TEXT NOT NULL,  
    "status" TEXT NOT NULL DEFAULT 'pending',  
    "workspaceId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL,  
    FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE  
);

\-- ProjectDocuments Join Table  
CREATE TABLE "ProjectDocuments" (  
    "projectId" UUID NOT NULL,  
    "documentId" UUID NOT NULL,  
    PRIMARY KEY ("projectId", "documentId"),  
    FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE,  
    FOREIGN KEY ("documentId") REFERENCES "KnowledgeBaseDocument"("id") ON DELETE CASCADE  
);

\-- DocumentChunk Table  
CREATE TABLE "DocumentChunk" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "chunkText" TEXT NOT NULL,  
    "embedding" vector(384), \-- Dimension for all-MiniLM-L6-v2 model  
    "documentId" UUID NOT NULL,  
    FOREIGN KEY ("documentId") REFERENCES "KnowledgeBaseDocument"("id") ON DELETE CASCADE  
);

\-- Vector Index  
CREATE INDEX ON "DocumentChunk" USING HNSW ("embedding" vector\_l2\_ops);

\-- Connection Table  
CREATE TABLE "Connection" (  
    "id" UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
    "customName" TEXT NOT NULL,  
    "application" TEXT NOT NULL,  
    "encryptedCredentials" TEXT NOT NULL,  
    "userId" UUID NOT NULL,  
    "workspaceId" UUID NOT NULL,  
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
    "updatedAt" TIMESTAMP(3) NOT NULL,  
    FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE,  
    FOREIGN KEY ("workspaceId") REFERENCES "Workspace"("id") ON DELETE CASCADE  
);

## **9\. Frontend Architecture**

* **Component Organization**: Components reside in src/components/, structured by UI, layout, and features.  
* **State Management**: Uses **Zustand** with separate stores for distinct domains (auth, projects, UI) located in src/stores/.  
* **Routing**: Leverages the Next.js App Router (src/app/) for file-system-based routing, with a layout for protected routes.  
* **API Communication**: A centralized **Axios** instance (src/lib/api.ts) with an interceptor for attaching auth tokens is used for all backend communication.

## **10\. Backend Architecture**

* **Service Architecture**: Each microservice is a self-contained Fastify application with routes organized by feature.  
* **Data Access Layer**: A strict **Repository Pattern** is enforced, abstracting all Prisma logic into src/repositories/ to separate it from business logic.  
* **Authentication**: A Fastify "hook" acts as a guard on protected routes, verifying session tokens and attaching the user to the request.

## **11\. Unified Project Structure**

A **Turborepo** monorepo will be used to manage the frontend, backend, and shared packages.

sflow-monorepo/  
├── apps/  
│   ├── web/  
│   └── api/  
├── packages/  
│   ├── config/  
│   └── types/  
└── docs/

## **12\. Development Workflow**

* **Prerequisites**: Node.js, pnpm, Docker.  
* **Setup**: pnpm install, configure .env, docker-compose up \-d.  
* **Commands**: pnpm dev (runs all), pnpm test.

## **13\. Deployment Architecture**

* **Frontend**: Deployed to **Vercel**, leveraging its global CDN and Next.js integration.  
* **Backend**: Each service is built into a **Docker image** and deployed to a cloud-agnostic container platform.  
* **CI/CD**: **GitHub Actions** will be used to automate testing, building, and deployment to staging and production environments.

## **14\. Security and Performance**

* **Security**: Enforced via Content Security Policy (CSP), input validation with Zod, rate limiting, strict CORS, and HttpOnly cookies for session tokens.  
* **Performance**: Optimized using Next.js features (code-splitting, image optimization), Vercel's edge network, and backend best practices (indexing, caching with Redis if needed).

## **15\. Testing Strategy**

* **Pyramid**: A strategy focused on a large base of **Unit Tests** (Jest), a smaller layer of **Integration Tests**, and a focused set of **E2E Tests** (Playwright).  
* **Organization**: Tests are co-located with the code they test in both the frontend and backend. E2E tests live in a root e2e/ directory.

## **16\. Coding Standards**

* **Critical Rules**: Enforce use of shared types, the service layer for API calls, the repository pattern for DB access, and Zustand for global state.  
* **Naming Conventions**: PascalCase for components and DB tables, useCamelCase for hooks, kebab-case for API routes.

## **17\. Error Handling Strategy**

* **Unified Approach**: A centralized error handler in the backend formats all errors into a standard JSON structure. A frontend Axios interceptor catches these errors and displays user-friendly notifications.

## **18\. Monitoring and Observability**

* **Stack**: **Vercel Analytics** for frontend vitals, **Datadog** for backend log aggregation and APM, and **Sentry** for real-time error tracking across the full stack.  
* **Key Metrics**: Core Web Vitals, JS error rates, API throughput, error rates, and latency will be tracked.
