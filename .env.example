# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/sflow"

# API Configuration
PORT=3001
HOST=0.0.0.0
AUTH_URL=http://localhost:3001
AUTH_SECRET=your_auth_secret_here

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001
FRONTEND_URL=http://localhost:3000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Email (Resend)
RESEND_API_KEY=your_resend_api_key
RESEND_FROM_EMAIL=<EMAIL>

# Development
NODE_ENV=development