{"name": "sflow-monorepo", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "test": "turbo test", "clean": "turbo clean"}, "devDependencies": {"jest-mock-extended": "^4.0.0", "turbo": "^1.13.4"}, "packageManager": "pnpm@8.0.0", "workspaces": ["apps/*", "packages/*"], "pnpm": {"overrides": {"glob": "^10.3.10", "rimraf": "^5.0.5", "inflight": "npm:@isaacs/inflight@^1.0.1"}}, "dependencies": {"zod": "^4.0.5"}}