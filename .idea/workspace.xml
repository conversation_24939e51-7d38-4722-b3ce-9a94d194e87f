<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="42470347-d41f-4584-989b-4fc7ca6231e9" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/apps/api/src/services/agent.service.test.ts" beforeDir="false" afterPath="$PROJECT_DIR$/apps/api/src/services/agent.service.test.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/api/src/services/agent.service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/apps/api/src/services/agent.service.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/web/src/components/editor/generation-block-config.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/apps/web/src/components/editor/generation-block-config.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/web/src/components/editor/generation-block-display.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/apps/web/src/components/editor/generation-block-display.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/apps/web/src/components/editor/generation-block.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/apps/web/src/components/editor/generation-block.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/stories/3.5.story.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/stories/3.5.story.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="30MQc1YJQJqCbKqJv7n3B2AGhh3" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "/projects/sflow-new",
    "nodejs_package_manager_path": "pnpm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RdControllerToolWindowsLayoutState" isNewUi="true">
    <layout>
      <window_info id="Bookmarks" side_tool="true" />
      <window_info id="Merge Requests" />
      <window_info id="Backup and Sync History" />
      <window_info id="Commit_Guest" show_stripe_button="false" />
      <window_info id="Pull Requests" />
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.33035713" />
      <window_info id="Commit" order="1" weight="0.25" />
      <window_info active="true" id="Structure" order="2" side_tool="true" visible="true" weight="0.33035713" />
      <window_info anchor="bottom" id="Database Changes" />
      <window_info anchor="bottom" id="TypeScript" />
      <window_info anchor="bottom" id="TODO" />
      <window_info anchor="bottom" id="File Transfer" />
      <window_info anchor="bottom" id="Version Control" order="0" />
      <window_info anchor="bottom" id="Problems" order="1" />
      <window_info anchor="bottom" id="Problems View" order="2" />
      <window_info active="true" anchor="bottom" id="Terminal" order="3" visible="true" weight="0.3305" />
      <window_info anchor="bottom" id="Services" order="4" />
      <window_info anchor="right" id="Endpoints" />
      <window_info anchor="right" id="Coverage" side_tool="true" />
      <window_info anchor="right" content_ui="combo" id="Notifications" order="0" weight="0.25" />
      <window_info anchor="right" id="AIAssistant" order="1" weight="0.25" />
      <window_info anchor="right" id="Database" order="2" weight="0.25" />
      <window_info anchor="right" id="Gradle" order="3" weight="0.25" />
      <window_info anchor="right" id="Maven" order="4" weight="0.25" />
    </layout>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.26927.40" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="42470347-d41f-4584-989b-4fc7ca6231e9" name="Changes" comment="" />
      <created>1753435037269</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753435037269</updated>
      <workItem from="1753435038322" duration="245000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>