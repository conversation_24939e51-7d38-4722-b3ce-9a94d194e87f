export type AccessType = 'PERSONAL' | 'COMPANY_WIDE';

export interface Project {
  id: string;
  name: string;
  description?: string;
  accessType: AccessType;
  userId: string; // TODO: Replace with workspaceId when workspaces are implemented
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateProjectInput {
  name: string;
  description?: string;
  accessType?: AccessType; // Optional, defaults to 'PERSONAL' on backend
}

export interface ProjectState {
  projects: Project[];
  isLoading: boolean;
  error: string | null;
  selectedProject: Project | null;
}