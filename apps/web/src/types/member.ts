export interface ProjectMember {
  userId: string;
  projectId: string;
  role: 'ADMIN' | 'MEMBER';
  invitedAt: string;
  joinedAt?: string;
  user: {
    id: string;
    email: string;
    name?: string;
    emailVerified: boolean;
    image?: string;
    phone?: string;
    country?: string;
    createdAt: string;
    updatedAt: string;
  };
}

export interface AddMemberRequest {
  userId: string;
  role?: 'ADMIN' | 'MEMBER';
}

export interface UpdateMemberRoleRequest {
  role: 'ADMIN' | 'MEMBER';
}