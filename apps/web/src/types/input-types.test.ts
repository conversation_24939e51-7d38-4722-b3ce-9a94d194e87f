import {
  InputDefinition,
  TextInputDefinition,
  NumberInputDefinition,
  SelectInputDefinition,
  ListInputDefinition,
  ObjectInputDefinition,
  isTextInput,
  isNumberInput,
  isBooleanInput,
  isImageInput,
  isAudioInput,
  isPDFInput,
  isCSVInput,
  isSelectInput,
  isListInput,
  isObjectInput,
  InputValue,
  FormValues,
} from './input-types';

describe('Input Type Definitions', () => {
  describe('Type Guards', () => {
    it('correctly identifies text input', () => {
      const textInput: TextInputDefinition = {
        id: '1',
        name: 'username',
        type: 'text',
        minLength: 3,
        maxLength: 20,
      };

      expect(isTextInput(textInput)).toBe(true);
      expect(isNumberInput(textInput)).toBe(false);
    });

    it('correctly identifies number input', () => {
      const numberInput: NumberInputDefinition = {
        id: '2',
        name: 'age',
        type: 'number',
        min: 0,
        max: 150,
      };

      expect(isNumberInput(numberInput)).toBe(true);
      expect(isTextInput(numberInput)).toBe(false);
    });

    it('correctly identifies boolean input', () => {
      const booleanInput: InputDefinition = {
        id: '3',
        name: 'agree',
        type: 'boolean',
      };

      expect(isBooleanInput(booleanInput)).toBe(true);
      expect(isTextInput(booleanInput)).toBe(false);
    });

    it('correctly identifies file inputs', () => {
      const imageInput: InputDefinition = { id: '4', name: 'photo', type: 'image' };
      const audioInput: InputDefinition = { id: '5', name: 'voice', type: 'audio' };
      const pdfInput: InputDefinition = { id: '6', name: 'document', type: 'pdf' };
      const csvInput: InputDefinition = { id: '7', name: 'data', type: 'csv' };

      expect(isImageInput(imageInput)).toBe(true);
      expect(isAudioInput(audioInput)).toBe(true);
      expect(isPDFInput(pdfInput)).toBe(true);
      expect(isCSVInput(csvInput)).toBe(true);
    });

    it('correctly identifies select input', () => {
      const selectInput: SelectInputDefinition = {
        id: '8',
        name: 'country',
        type: 'select',
        options: [
          { value: 'us', label: 'United States' },
          { value: 'uk', label: 'United Kingdom' },
        ],
      };

      expect(isSelectInput(selectInput)).toBe(true);
      expect(isTextInput(selectInput)).toBe(false);
    });

    it('correctly identifies list input', () => {
      const listInput: ListInputDefinition = {
        id: '9',
        name: 'tags',
        type: 'list',
        itemType: 'text',
      };

      expect(isListInput(listInput)).toBe(true);
      expect(isObjectInput(listInput)).toBe(false);
    });

    it('correctly identifies object input', () => {
      const objectInput: ObjectInputDefinition = {
        id: '10',
        name: 'address',
        type: 'object',
        properties: {
          street: { id: '11', name: 'street', type: 'text' },
          city: { id: '12', name: 'city', type: 'text' },
        },
      };

      expect(isObjectInput(objectInput)).toBe(true);
      expect(isListInput(objectInput)).toBe(false);
    });
  });

  describe('Complex Type Definitions', () => {
    it('supports nested list definitions', () => {
      const nestedListInput: ListInputDefinition = {
        id: '13',
        name: 'matrix',
        type: 'list',
        itemType: 'list',
        itemDefinition: {
          id: '14',
          name: 'row',
          type: 'list',
          itemType: 'number',
        },
      };

      expect(nestedListInput.itemType).toBe('list');
      expect(nestedListInput.itemDefinition?.type).toBe('list');
    });

    it('supports nested object definitions', () => {
      const nestedObjectInput: ObjectInputDefinition = {
        id: '15',
        name: 'user',
        type: 'object',
        properties: {
          name: { id: '16', name: 'name', type: 'text' },
          address: {
            id: '17',
            name: 'address',
            type: 'object',
            properties: {
              street: { id: '18', name: 'street', type: 'text' },
              zip: { id: '19', name: 'zip', type: 'text' },
            },
          },
        },
      };

      expect(nestedObjectInput.properties.address.type).toBe('object');
      expect((nestedObjectInput.properties.address as ObjectInputDefinition).properties.street.type).toBe('text');
    });
  });

  describe('Type Inference', () => {
    it('infers correct input value types', () => {
      // These are compile-time type checks
      type TextValue = InputValue<TextInputDefinition>;
      type NumberValue = InputValue<NumberInputDefinition>;
      type BooleanValue = InputValue<{ id: string; name: string; type: 'boolean' }>;
      
      // TypeScript will ensure these are the correct types
      const textValue: TextValue = 'hello';
      const numberValue: NumberValue = 42;
      const booleanValue: BooleanValue = true;

      expect(typeof textValue).toBe('string');
      expect(typeof numberValue).toBe('number');
      expect(typeof booleanValue).toBe('boolean');
    });

    it('infers correct form value types', () => {
      const inputs = [
        { id: '1', name: 'username', type: 'text' },
        { id: '2', name: 'age', type: 'number' },
        { id: '3', name: 'subscribe', type: 'boolean' },
      ] as const;

      type TestFormValues = FormValues<typeof inputs>;

      // This is a compile-time type check
      const formValues: TestFormValues = {
        username: 'john',
        age: 25,
        subscribe: true,
      };

      expect(formValues.username).toBe('john');
      expect(formValues.age).toBe(25);
      expect(formValues.subscribe).toBe(true);
    });
  });

  describe('Input Configuration', () => {
    it('supports all required properties', () => {
      const textInput: TextInputDefinition = {
        id: '20',
        name: 'email',
        type: 'text',
        required: true,
        description: 'User email address',
        pattern: '^[^@]+@[^@]+$',
        placeholder: 'Enter your email',
      };

      expect(textInput.required).toBe(true);
      expect(textInput.description).toBe('User email address');
      expect(textInput.pattern).toBe('^[^@]+@[^@]+$');
    });

    it('supports select input with options', () => {
      const selectInput: SelectInputDefinition = {
        id: '21',
        name: 'role',
        type: 'select',
        options: [
          { value: 'admin', label: 'Administrator' },
          { value: 'user', label: 'User' },
          { value: 'guest', label: 'Guest', disabled: true },
        ],
        multiple: false,
        defaultValue: 'user',
      };

      expect(selectInput.options).toHaveLength(3);
      expect(selectInput.options[2].disabled).toBe(true);
      expect(selectInput.defaultValue).toBe('user');
    });

    it('supports file inputs with constraints', () => {
      const imageInput: InputDefinition = {
        id: '22',
        name: 'avatar',
        type: 'image',
        maxSize: 5 * 1024 * 1024, // 5MB
        acceptedFormats: ['jpg', 'png', 'webp'],
      } as any;

      expect((imageInput as any).maxSize).toBe(5 * 1024 * 1024);
      expect((imageInput as any).acceptedFormats).toEqual(['jpg', 'png', 'webp']);
    });
  });
});