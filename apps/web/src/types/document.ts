export interface KnowledgeBaseDocument {
  id: string;
  workspaceId: string;
  fileName: string;
  fileType: string;
  fileSize?: number | null;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  uploadedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentWithAssociation {
  document: KnowledgeBaseDocument;
  isAssociated: boolean;
}

export interface UpdateProjectDocumentsRequest {
  documentIds: string[];
}

export interface PaginatedResponse<T> {
  documents: T[];
  total: number;
  limit: number;
  offset: number;
}

export interface DocumentsResponse extends PaginatedResponse<KnowledgeBaseDocument> {}

export interface AvailableDocumentsResponse extends PaginatedResponse<DocumentWithAssociation> {}