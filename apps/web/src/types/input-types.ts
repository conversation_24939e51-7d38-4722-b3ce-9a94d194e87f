// Base input type union
export type InputType = 
  | 'text' 
  | 'number' 
  | 'boolean' 
  | 'image' 
  | 'audio' 
  | 'pdf' 
  | 'csv' 
  | 'list' 
  | 'object' 
  | 'select';

// Base interface for all input definitions
export interface BaseInputDefinition {
  id: string;
  name: string;
  type: InputType;
  required?: boolean;
  description?: string;
}

// Text input configuration
export interface TextInputDefinition extends BaseInputDefinition {
  type: 'text';
  minLength?: number;
  maxLength?: number;
  pattern?: string; // Regex pattern
  placeholder?: string;
}

// Number input configuration
export interface NumberInputDefinition extends BaseInputDefinition {
  type: 'number';
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
}

// Boolean input configuration
export interface BooleanInputDefinition extends BaseInputDefinition {
  type: 'boolean';
  defaultValue?: boolean;
}

// File upload configurations
export interface ImageInputDefinition extends BaseInputDefinition {
  type: 'image';
  maxSize?: number; // in bytes
  acceptedFormats?: string[]; // e.g., ['jpg', 'png', 'gif']
}

export interface AudioInputDefinition extends BaseInputDefinition {
  type: 'audio';
  maxSize?: number; // in bytes
  acceptedFormats?: string[]; // e.g., ['mp3', 'wav']
}

export interface PDFInputDefinition extends BaseInputDefinition {
  type: 'pdf';
  maxSize?: number; // in bytes
}

export interface CSVInputDefinition extends BaseInputDefinition {
  type: 'csv';
  maxSize?: number; // in bytes
}

// Select input configuration
export interface SelectInputDefinition extends BaseInputDefinition {
  type: 'select';
  options: SelectOption[];
  multiple?: boolean;
  defaultValue?: string | string[];
}

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

// List input configuration
export interface ListInputDefinition extends BaseInputDefinition {
  type: 'list';
  itemType: InputType;
  minItems?: number;
  maxItems?: number;
  // For complex list items, we can have nested definitions
  itemDefinition?: InputDefinition;
}

// Object input configuration
export interface ObjectInputDefinition extends BaseInputDefinition {
  type: 'object';
  properties: Record<string, InputDefinition>;
}

// Discriminated union of all input definitions
export type InputDefinition = 
  | TextInputDefinition
  | NumberInputDefinition
  | BooleanInputDefinition
  | ImageInputDefinition
  | AudioInputDefinition
  | PDFInputDefinition
  | CSVInputDefinition
  | SelectInputDefinition
  | ListInputDefinition
  | ObjectInputDefinition;

// Helper type guards
export function isTextInput(input: InputDefinition): input is TextInputDefinition {
  return input.type === 'text';
}

export function isNumberInput(input: InputDefinition): input is NumberInputDefinition {
  return input.type === 'number';
}

export function isBooleanInput(input: InputDefinition): input is BooleanInputDefinition {
  return input.type === 'boolean';
}

export function isImageInput(input: InputDefinition): input is ImageInputDefinition {
  return input.type === 'image';
}

export function isAudioInput(input: InputDefinition): input is AudioInputDefinition {
  return input.type === 'audio';
}

export function isPDFInput(input: InputDefinition): input is PDFInputDefinition {
  return input.type === 'pdf';
}

export function isCSVInput(input: InputDefinition): input is CSVInputDefinition {
  return input.type === 'csv';
}

export function isSelectInput(input: InputDefinition): input is SelectInputDefinition {
  return input.type === 'select';
}

export function isListInput(input: InputDefinition): input is ListInputDefinition {
  return input.type === 'list';
}

export function isObjectInput(input: InputDefinition): input is ObjectInputDefinition {
  return input.type === 'object';
}

// Input configuration for forms
export interface InputFormConfig {
  inputs: InputDefinition[];
  submitLabel?: string;
  cancelLabel?: string;
  layout?: 'vertical' | 'horizontal';
}

// Validation result
export interface InputValidationResult {
  valid: boolean;
  errors?: Record<string, string[]>;
}

// Input value types
export type InputValue<T extends InputDefinition> = 
  T extends TextInputDefinition ? string :
  T extends NumberInputDefinition ? number :
  T extends BooleanInputDefinition ? boolean :
  T extends ImageInputDefinition ? File :
  T extends AudioInputDefinition ? File :
  T extends PDFInputDefinition ? File :
  T extends CSVInputDefinition ? File :
  T extends SelectInputDefinition ? (T['multiple'] extends true ? string[] : string) :
  T extends ListInputDefinition ? unknown[] :
  T extends ObjectInputDefinition ? Record<string, unknown> :
  never;

// Form values for a set of inputs
export type FormValues<T extends readonly InputDefinition[]> = {
  [K in T[number] as K['name']]: InputValue<K>;
};