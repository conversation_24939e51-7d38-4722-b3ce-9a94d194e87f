import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { BaseInputDefinition } from '@/types/input-types';

type SaveStatus = 'idle' | 'saving' | 'saved' | 'error';

interface EditorState {
  content: any[];
  lastSavedContent: any[];
  isDirty: boolean;
  lastModified: Date | null;
  agentTitle: string;
  saveStatus: SaveStatus;
  saveError: string | null;
  inputDefinitions: BaseInputDefinition[];
  currentAgentId: string | null;
  setContent: (content: any[]) => void;
  setAgentTitle: (title: string) => void;
  setSaveStatus: (status: SaveStatus, error?: string) => void;
  saveContent: () => void;
  resetContent: () => void;
  loadAgent: (agentId: string, title: string, content: any[], inputDefinitions: BaseInputDefinition[]) => void;
  // Input definition methods
  addInputDefinition: (input: BaseInputDefinition) => void;
  updateInputDefinition: (id: string, input: BaseInputDefinition) => void;
  removeInputDefinition: (id: string) => void;
  getInputDefinition: (id: string) => BaseInputDefinition | undefined;
  clearInputDefinitions: () => void;
}

const defaultContent = [{
  type: "paragraph",
  content: "",
}];

export const useEditorStore = create<EditorState>()(
  persist(
    (set, get) => ({
      content: defaultContent,
      lastSavedContent: defaultContent,
      isDirty: false,
      lastModified: null,
      agentTitle: '',
      saveStatus: 'idle',
      saveError: null,
      inputDefinitions: [],
      currentAgentId: null,
      setContent: (content) => {
        const currentState = get();
        const isDirty = JSON.stringify(content) !== JSON.stringify(currentState.lastSavedContent) || 
                       currentState.agentTitle !== '' || 
                       currentState.inputDefinitions.length > 0;
        set({ 
          content, 
          isDirty,
          lastModified: new Date()
        });
      },
      setAgentTitle: (title) => {
        const currentState = get();
        const isDirty = JSON.stringify(currentState.content) !== JSON.stringify(currentState.lastSavedContent) || 
                       title !== '' || 
                       currentState.inputDefinitions.length > 0;
        set({ 
          agentTitle: title,
          isDirty
        });
      },
      setSaveStatus: (status, error) => {
        set({ 
          saveStatus: status,
          saveError: error || null
        });
      },
      saveContent: () => {
        const currentState = get();
        set({ 
          lastSavedContent: currentState.content,
          isDirty: false,
          saveStatus: 'saved'
        });
      },
      resetContent: () => {
        set({ 
          content: defaultContent,
          lastSavedContent: defaultContent,
          isDirty: false,
          lastModified: null,
          agentTitle: '',
          saveStatus: 'idle',
          saveError: null,
          inputDefinitions: [],
          currentAgentId: null
        });
      },
      loadAgent: (agentId, title, content, inputDefinitions) => {
        set({
          currentAgentId: agentId,
          agentTitle: title,
          content: content || defaultContent,
          lastSavedContent: content || defaultContent,
          inputDefinitions: inputDefinitions || [],
          isDirty: false,
          saveStatus: 'idle',
          saveError: null,
          lastModified: null
        });
      },
      // Input definition methods
      addInputDefinition: (input) => {
        const currentState = get();
        // Check for duplicate names
        const exists = currentState.inputDefinitions.some(def => def.name === input.name);
        if (exists) {
          console.warn(`Input with name "${input.name}" already exists`);
          return;
        }
        set({ 
          inputDefinitions: [...currentState.inputDefinitions, input],
          isDirty: true
        });
      },
      updateInputDefinition: (id, input) => {
        const currentState = get();
        const updatedDefinitions = currentState.inputDefinitions.map(def => 
          def.id === id ? input : def
        );
        set({ 
          inputDefinitions: updatedDefinitions,
          isDirty: true
        });
      },
      removeInputDefinition: (id) => {
        const currentState = get();
        const filteredDefinitions = currentState.inputDefinitions.filter(def => def.id !== id);
        set({ 
          inputDefinitions: filteredDefinitions,
          isDirty: true
        });
      },
      getInputDefinition: (id) => {
        const currentState = get();
        return currentState.inputDefinitions.find(def => def.id === id);
      },
      clearInputDefinitions: () => {
        set({ 
          inputDefinitions: [],
          isDirty: true
        });
      },
    }),
    {
      name: 'editor-storage',
      partialize: (state) => ({ 
        // Don't persist anything - each agent should be loaded fresh
      }),
    }
  )
);