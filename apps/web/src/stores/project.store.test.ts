import { renderHook, act } from '@testing-library/react';
import { useProjectStore } from './project.store';
import { projectService } from '@/services/project.service';
import { Project } from '@/types/project';

// Mock the project service
jest.mock('@/services/project.service', () => ({
  projectService: {
    getProjects: jest.fn(),
    createProject: jest.fn(),
  },
}));

describe('useProjectStore', () => {
  beforeEach(() => {
    // Clear store state and mocks before each test
    useProjectStore.setState({
      projects: [],
      isLoading: false,
      error: null,
      selectedProject: null,
    });
    jest.clearAllMocks();
  });

  describe('fetchProjects', () => {
    it('should fetch projects successfully', async () => {
      const mockProjects = [
        { id: '1', name: 'Project 1', createdAt: new Date(), updatedAt: new Date(), userId: 'user1' },
        { id: '2', name: 'Project 2', createdAt: new Date(), updatedAt: new Date(), userId: 'user1' },
      ];
      (projectService.getProjects as jest.Mock).mockResolvedValue(mockProjects);

      const { result } = renderHook(() => useProjectStore());

      await act(async () => {
        await result.current.fetchProjects();
      });

      expect(result.current.projects).toEqual(mockProjects);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.selectedProject).toEqual(mockProjects[0]); // Auto-selects first project
    });

    it('should handle fetch error', async () => {
      const errorMessage = 'Network error';
      (projectService.getProjects as jest.Mock).mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useProjectStore());

      await act(async () => {
        await result.current.fetchProjects();
      });

      expect(result.current.projects).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });

    it('should not auto-select if a project is already selected', async () => {
      const existingProject = { id: '0', name: 'Existing', createdAt: new Date(), updatedAt: new Date(), userId: 'user1' };
      const mockProjects = [
        { id: '1', name: 'Project 1', createdAt: new Date(), updatedAt: new Date(), userId: 'user1' },
      ];
      
      useProjectStore.setState({ selectedProject: existingProject });
      (projectService.getProjects as jest.Mock).mockResolvedValue(mockProjects);

      const { result } = renderHook(() => useProjectStore());

      await act(async () => {
        await result.current.fetchProjects();
      });

      expect(result.current.selectedProject).toEqual(existingProject);
    });
  });

  describe('createProject', () => {
    it('should create project successfully', async () => {
      const newProject = { 
        id: '3', 
        name: 'New Project', 
        description: 'Test description',
        createdAt: new Date(), 
        updatedAt: new Date(), 
        userId: 'user1' 
      };
      (projectService.createProject as jest.Mock).mockResolvedValue(newProject);

      const { result } = renderHook(() => useProjectStore());

      let createdProject;
      await act(async () => {
        createdProject = await result.current.createProject({ 
          name: 'New Project', 
          description: 'Test description' 
        });
      });

      expect(createdProject).toEqual(newProject);
      expect(result.current.projects).toContain(newProject);
      expect(result.current.selectedProject).toEqual(newProject);
      expect(result.current.error).toBeNull();
    });

    it('should handle create error', async () => {
      const errorMessage = 'Failed to create';
      (projectService.createProject as jest.Mock).mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useProjectStore());

      try {
        await act(async () => {
          await result.current.createProject({ name: 'New Project' });
        });
      } catch (error) {
        // Expected error
      }

      expect(result.current.error).toBe(errorMessage);
    });
  });

  describe('selectProject', () => {
    it('should select a project', () => {
      const project = { 
        id: '1', 
        name: 'Project 1', 
        createdAt: new Date(), 
        updatedAt: new Date(), 
        userId: 'user1' 
      };

      const { result } = renderHook(() => useProjectStore());

      act(() => {
        result.current.selectProject(project);
      });

      expect(result.current.selectedProject).toEqual(project);
    });
  });
});