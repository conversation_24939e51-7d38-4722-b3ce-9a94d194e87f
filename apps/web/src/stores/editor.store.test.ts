import { act, renderHook } from '@testing-library/react';
import { useEditorStore } from './editor.store';
import { BaseInputDefinition } from '@/types/input-types';

describe('useEditorStore', () => {
  beforeEach(() => {
    // Clear the store before each test
    const { result } = renderHook(() => useEditorStore());
    act(() => {
      result.current.resetContent();
    });
  });

  it('initializes with default content', () => {
    const { result } = renderHook(() => useEditorStore());

    expect(result.current.content).toEqual([{ type: 'paragraph', content: '' }]);
    expect(result.current.lastSavedContent).toEqual([{ type: 'paragraph', content: '' }]);
    expect(result.current.isDirty).toBe(false);
    expect(result.current.lastModified).toBe(null);
    expect(result.current.agentTitle).toBe('');
    expect(result.current.saveStatus).toBe('idle');
    expect(result.current.saveError).toBe(null);
    expect(result.current.inputDefinitions).toEqual([]);
  });

  it('sets content and updates isDirty status', () => {
    const { result } = renderHook(() => useEditorStore());
    const newContent = [{ type: 'paragraph', content: 'Test' }];

    act(() => {
      result.current.setContent(newContent);
    });

    expect(result.current.content).toEqual(newContent);
    expect(result.current.isDirty).toBe(true);
    expect(result.current.lastModified).toBeInstanceOf(Date);
  });

  it('saves content and resets isDirty', () => {
    const { result } = renderHook(() => useEditorStore());
    const newContent = [{ type: 'paragraph', content: 'Test' }];

    act(() => {
      result.current.setContent(newContent);
    });

    expect(result.current.isDirty).toBe(true);

    act(() => {
      result.current.saveContent();
    });

    expect(result.current.lastSavedContent).toEqual(newContent);
    expect(result.current.isDirty).toBe(false);
    expect(result.current.saveStatus).toBe('saved');
  });

  it('detects when content matches saved content', () => {
    const { result } = renderHook(() => useEditorStore());
    const content = [{ type: 'paragraph', content: 'Test' }];

    // Set and save content
    act(() => {
      result.current.setContent(content);
      result.current.saveContent();
    });

    expect(result.current.isDirty).toBe(false);

    // Change content
    act(() => {
      result.current.setContent([{ type: 'paragraph', content: 'Changed' }]);
    });

    expect(result.current.isDirty).toBe(true);

    // Set back to saved content but with agent title
    act(() => {
      result.current.setAgentTitle('Test Agent');
      result.current.setContent(content);
    });

    expect(result.current.isDirty).toBe(true); // Still dirty because of title
  });

  it('resets to default content', () => {
    const { result } = renderHook(() => useEditorStore());
    
    // Add some content and agent metadata
    act(() => {
      result.current.setContent([{ type: 'paragraph', content: 'Test' }]);
      result.current.setAgentTitle('Test Agent');
      result.current.setSaveStatus('error', 'Test error');
      result.current.saveContent();
    });

    // Reset
    act(() => {
      result.current.resetContent();
    });

    expect(result.current.content).toEqual([{ type: 'paragraph', content: '' }]);
    expect(result.current.lastSavedContent).toEqual([{ type: 'paragraph', content: '' }]);
    expect(result.current.isDirty).toBe(false);
    expect(result.current.lastModified).toBe(null);
    expect(result.current.agentTitle).toBe('');
    expect(result.current.saveStatus).toBe('idle');
    expect(result.current.saveError).toBe(null);
  });

  it('persists content, lastSavedContent, and inputDefinitions', () => {
    const { result } = renderHook(() => useEditorStore());
    
    // Add some input definitions to test persistence
    act(() => {
      result.current.addInputDefinition({
        id: '1',
        name: 'test_input',
        type: 'text',
      });
    });

    // The store should include inputDefinitions in persisted state
    expect(result.current.inputDefinitions).toHaveLength(1);
    
    // Verify the shape of the state includes inputDefinitions
    const state = result.current;
    expect(state).toHaveProperty('inputDefinitions');
    expect(state.inputDefinitions).toEqual(expect.any(Array));
  });

  describe('Agent Metadata', () => {
    it('updates agent title', () => {
      const { result } = renderHook(() => useEditorStore());

      act(() => {
        result.current.setAgentTitle('My Test Agent');
      });

      expect(result.current.agentTitle).toBe('My Test Agent');
      expect(result.current.isDirty).toBe(true);
    });

    it('updates save status and error', () => {
      const { result } = renderHook(() => useEditorStore());

      act(() => {
        result.current.setSaveStatus('saving');
      });

      expect(result.current.saveStatus).toBe('saving');
      expect(result.current.saveError).toBe(null);

      act(() => {
        result.current.setSaveStatus('error', 'Failed to save');
      });

      expect(result.current.saveStatus).toBe('error');
      expect(result.current.saveError).toBe('Failed to save');
    });

    it('marks as dirty when title is set even with unchanged content', () => {
      const { result } = renderHook(() => useEditorStore());

      act(() => {
        result.current.saveContent(); // Sync saved and current content
      });

      expect(result.current.isDirty).toBe(false);

      act(() => {
        result.current.setAgentTitle('New Title');
      });

      expect(result.current.isDirty).toBe(true);
    });
  });

  describe('Input Definitions', () => {
    it('adds an input definition', () => {
      const { result } = renderHook(() => useEditorStore());
      const inputDef: BaseInputDefinition = {
        id: '1',
        name: 'test_input',
        type: 'text',
        required: true,
      };

      act(() => {
        result.current.addInputDefinition(inputDef);
      });

      expect(result.current.inputDefinitions).toHaveLength(1);
      expect(result.current.inputDefinitions[0]).toEqual(inputDef);
      expect(result.current.isDirty).toBe(true);
    });

    it('prevents duplicate input names', () => {
      const { result } = renderHook(() => useEditorStore());
      const inputDef1: BaseInputDefinition = {
        id: '1',
        name: 'test_input',
        type: 'text',
      };
      const inputDef2: BaseInputDefinition = {
        id: '2',
        name: 'test_input', // Same name
        type: 'number',
      };

      // Mock console.warn
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

      act(() => {
        result.current.addInputDefinition(inputDef1);
        result.current.addInputDefinition(inputDef2);
      });

      expect(result.current.inputDefinitions).toHaveLength(1);
      expect(result.current.inputDefinitions[0]).toEqual(inputDef1);
      expect(consoleWarnSpy).toHaveBeenCalledWith('Input with name "test_input" already exists');

      consoleWarnSpy.mockRestore();
    });

    it('updates an input definition', () => {
      const { result } = renderHook(() => useEditorStore());
      const inputDef: BaseInputDefinition = {
        id: '1',
        name: 'test_input',
        type: 'text',
      };

      act(() => {
        result.current.addInputDefinition(inputDef);
      });

      const updatedDef: BaseInputDefinition = {
        id: '1',
        name: 'updated_input',
        type: 'number',
        required: true,
      };

      act(() => {
        result.current.updateInputDefinition('1', updatedDef);
      });

      expect(result.current.inputDefinitions).toHaveLength(1);
      expect(result.current.inputDefinitions[0]).toEqual(updatedDef);
      expect(result.current.isDirty).toBe(true);
    });

    it('removes an input definition', () => {
      const { result } = renderHook(() => useEditorStore());
      const inputDef1: BaseInputDefinition = {
        id: '1',
        name: 'input1',
        type: 'text',
      };
      const inputDef2: BaseInputDefinition = {
        id: '2',
        name: 'input2',
        type: 'number',
      };

      act(() => {
        result.current.addInputDefinition(inputDef1);
        result.current.addInputDefinition(inputDef2);
      });

      expect(result.current.inputDefinitions).toHaveLength(2);

      act(() => {
        result.current.removeInputDefinition('1');
      });

      expect(result.current.inputDefinitions).toHaveLength(1);
      expect(result.current.inputDefinitions[0]).toEqual(inputDef2);
      expect(result.current.isDirty).toBe(true);
    });

    it('gets an input definition by ID', () => {
      const { result } = renderHook(() => useEditorStore());
      const inputDef: BaseInputDefinition = {
        id: '1',
        name: 'test_input',
        type: 'text',
      };

      act(() => {
        result.current.addInputDefinition(inputDef);
      });

      const retrieved = result.current.getInputDefinition('1');
      expect(retrieved).toEqual(inputDef);

      const notFound = result.current.getInputDefinition('999');
      expect(notFound).toBeUndefined();
    });

    it('clears all input definitions', () => {
      const { result } = renderHook(() => useEditorStore());
      
      act(() => {
        result.current.addInputDefinition({
          id: '1',
          name: 'input1',
          type: 'text',
        });
        result.current.addInputDefinition({
          id: '2',
          name: 'input2',
          type: 'number',
        });
      });

      expect(result.current.inputDefinitions).toHaveLength(2);

      act(() => {
        result.current.clearInputDefinitions();
      });

      expect(result.current.inputDefinitions).toHaveLength(0);
      expect(result.current.isDirty).toBe(true);
    });

    it('marks as dirty when input definitions exist even with unchanged content', () => {
      const { result } = renderHook(() => useEditorStore());

      act(() => {
        result.current.saveContent();
      });

      expect(result.current.isDirty).toBe(false);

      act(() => {
        result.current.addInputDefinition({
          id: '1',
          name: 'test_input',
          type: 'text',
        });
      });

      expect(result.current.isDirty).toBe(true);

      // Even setting the same content should keep it dirty due to input definitions
      act(() => {
        result.current.setContent([{ type: 'paragraph', content: '' }]);
      });

      expect(result.current.isDirty).toBe(true);
    });

    it('resets input definitions on resetContent', () => {
      const { result } = renderHook(() => useEditorStore());

      act(() => {
        result.current.addInputDefinition({
          id: '1',
          name: 'test_input',
          type: 'text',
        });
      });

      expect(result.current.inputDefinitions).toHaveLength(1);

      act(() => {
        result.current.resetContent();
      });

      expect(result.current.inputDefinitions).toHaveLength(0);
    });
  });
});