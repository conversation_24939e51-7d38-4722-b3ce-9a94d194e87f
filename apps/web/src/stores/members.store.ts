import { create } from 'zustand';
import type { ProjectMember } from '@/types/member';

interface MembersStore {
  members: Map<string, ProjectMember[]>;
  loading: boolean;
  error: string | null;
  
  setMembers: (projectId: string, members: ProjectMember[]) => void;
  addMember: (projectId: string, member: ProjectMember) => void;
  updateMember: (projectId: string, userId: string, updates: Partial<ProjectMember>) => void;
  removeMember: (projectId: string, userId: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearMembers: (projectId?: string) => void;
}

export const useMembersStore = create<MembersStore>((set, get) => ({
  members: new Map(),
  loading: false,
  error: null,

  setMembers: (projectId, members) => {
    set((state) => {
      const newMembers = new Map(state.members);
      newMembers.set(projectId, members);
      return { members: newMembers };
    });
  },

  addMember: (projectId, member) => {
    set((state) => {
      const newMembers = new Map(state.members);
      const projectMembers = newMembers.get(projectId) || [];
      newMembers.set(projectId, [...projectMembers, member]);
      return { members: newMembers };
    });
  },

  updateMember: (projectId, userId, updates) => {
    set((state) => {
      const newMembers = new Map(state.members);
      const projectMembers = newMembers.get(projectId) || [];
      const updatedMembers = projectMembers.map((member) =>
        member.userId === userId ? { ...member, ...updates } : member
      );
      newMembers.set(projectId, updatedMembers);
      return { members: newMembers };
    });
  },

  removeMember: (projectId, userId) => {
    set((state) => {
      const newMembers = new Map(state.members);
      const projectMembers = newMembers.get(projectId) || [];
      const filteredMembers = projectMembers.filter((member) => member.userId !== userId);
      newMembers.set(projectId, filteredMembers);
      return { members: newMembers };
    });
  },

  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),

  clearMembers: (projectId) => {
    if (projectId) {
      set((state) => {
        const newMembers = new Map(state.members);
        newMembers.delete(projectId);
        return { members: newMembers };
      });
    } else {
      set({ members: new Map() });
    }
  },
}));