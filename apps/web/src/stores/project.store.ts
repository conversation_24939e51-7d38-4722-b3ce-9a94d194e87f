import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { projectService } from '@/services/project.service';
import { Project, CreateProjectInput } from '@/types/project';

interface ProjectState {
  projects: Project[];
  isLoading: boolean;
  error: string | null;
  fetchProjects: () => Promise<void>;
  createProject: (data: CreateProjectInput) => Promise<Project>;
  deleteProject: (id: string) => Promise<void>;
  selectProject: (project: Project) => void;
  selectedProject: Project | null;
  currentProject: Project | null;
  setCurrentProject: (project: Project | null) => void;
  clearError: () => void;
}

export const useProjectStore = create<ProjectState>()(
  persist(
    (set, get) => ({
      projects: [],
      isLoading: false,
      error: null,
      selectedProject: null,
      currentProject: null,

      fetchProjects: async () => {
        set({ isLoading: true, error: null });
        try {
          const projects = await projectService.getProjects();
          set({ projects, isLoading: false });
          
          // Auto-select first project if none selected
          const state = get();
          if (!state.selectedProject && projects.length > 0) {
            set({ selectedProject: projects[0] });
          }
        } catch (error: any) {
          const errorMessage = error?.response?.data?.message || error?.message || 'Failed to fetch projects';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
        }
      },

      createProject: async (data) => {
        set({ error: null });
        try {
          const project = await projectService.createProject(data);
          set((state) => ({ 
            projects: [project, ...state.projects],
            selectedProject: project 
          }));
          return project;
        } catch (error: any) {
          let errorMessage = 'Failed to create project';
          
          if (error?.response?.status === 409) {
            errorMessage = 'A project with this name already exists';
          } else if (error?.response?.status === 400) {
            errorMessage = 'Invalid project data. Please check your inputs';
          } else if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          }
          
          set({ error: errorMessage });
          throw error;
        }
      },

      deleteProject: async (id: string) => {
        set({ error: null });
        try {
          await projectService.deleteProject(id);
          set((state) => {
            const projects = state.projects.filter(p => p.id !== id);
            const selectedProject = state.selectedProject?.id === id ? null : state.selectedProject;
            return { projects, selectedProject };
          });
        } catch (error: any) {
          let errorMessage = 'Failed to delete project';
          
          if (error?.response?.status === 404) {
            errorMessage = 'Project not found';
          } else if (error?.response?.status === 403) {
            errorMessage = 'You do not have permission to delete this project';
          } else if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          }
          
          set({ error: errorMessage });
          throw error;
        }
      },

      selectProject: (project) => {
        set({ selectedProject: project });
      },

      setCurrentProject: (project) => {
        set({ currentProject: project });
      },
      
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'project-store',
      partialize: (state) => ({ 
        selectedProject: state.selectedProject,
        currentProject: state.currentProject
      }),
    }
  )
);