import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { agentService } from '@/services/agent.service';
import { Agent, CreateAgentInput } from '@/types/agent';

interface AgentState {
  agents: Agent[];
  currentAgent: Agent | null;
  isLoading: boolean;
  error: string | null;
  fetchAgentsByProjectId: (projectId: string) => Promise<void>;
  createAgent: (projectId: string, data: Omit<CreateAgentInput, 'projectId'>) => Promise<Agent>;
  updateAgent: (id: string, data: Partial<CreateAgentInput>) => Promise<Agent>;
  deleteAgent: (id: string) => Promise<void>;
  setCurrentAgent: (agent: Agent | null) => void;
  clearError: () => void;
}

export const useAgentStore = create<AgentState>()(
  persist(
    (set, get) => ({
      agents: [],
      currentAgent: null,
      isLoading: false,
      error: null,

      fetchAgentsByProjectId: async (projectId) => {
        set({ isLoading: true, error: null });
        try {
          const agents = await agentService.getAgentsByProjectId(projectId);
          set({ agents, isLoading: false });
        } catch (error: any) {
          const errorMessage = error?.response?.data?.message || error?.message || 'Failed to fetch agents';
          set({ 
            error: errorMessage, 
            isLoading: false 
          });
        }
      },

      createAgent: async (projectId, data) => {
        set({ error: null });
        try {
          const agent = await agentService.createAgent(projectId, data);
          set((state) => ({ 
            agents: [agent, ...state.agents],
            currentAgent: agent 
          }));
          return agent;
        } catch (error: any) {
          const errorMessage = error?.response?.data?.message || error?.message || 'Failed to create agent';
          set({ error: errorMessage });
          throw error;
        }
      },

      updateAgent: async (id, data) => {
        set({ error: null });
        try {
          const agent = await agentService.updateAgent(id, data);
          set((state) => ({
            agents: state.agents.map(a => a.id === id ? agent : a),
            currentAgent: state.currentAgent?.id === id ? agent : state.currentAgent
          }));
          return agent;
        } catch (error: any) {
          const errorMessage = error?.response?.data?.message || error?.message || 'Failed to update agent';
          set({ error: errorMessage });
          throw error;
        }
      },

      deleteAgent: async (id) => {
        set({ error: null });
        try {
          await agentService.deleteAgent(id);
          set((state) => {
            const agents = state.agents.filter(a => a.id !== id);
            const currentAgent = state.currentAgent?.id === id ? null : state.currentAgent;
            return { agents, currentAgent };
          });
        } catch (error: any) {
          const errorMessage = error?.response?.data?.message || error?.message || 'Failed to delete agent';
          set({ error: errorMessage });
          throw error;
        }
      },

      setCurrentAgent: (agent) => {
        set({ currentAgent: agent });
      },
      
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'agent-store',
      partialize: (state) => ({ 
        currentAgent: state.currentAgent 
      }),
    }
  )
);