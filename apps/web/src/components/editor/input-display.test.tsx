import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { InputDisplay } from './input-display';
import type { BaseInputDefinition } from '@/types/input-types';

// Mock UI components
jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className, onClick, variant }: any) => (
    <div 
      data-testid="badge" 
      className={className} 
      onClick={onClick}
      data-variant={variant}
    >
      {children}
    </div>
  ),
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant, size, className }: any) => (
    <button 
      onClick={onClick} 
      data-variant={variant} 
      data-size={size}
      className={className}
    >
      {children}
    </button>
  ),
}));

jest.mock('lucide-react', () => ({
  X: () => <span>X</span>,
}));

describe('InputDisplay', () => {
  const mockInputs: BaseInputDefinition[] = [
    { id: '1', name: 'customer_name', type: 'text', required: true },
    { id: '2', name: 'age', type: 'number', required: false },
    { id: '3', name: 'is_active', type: 'boolean', required: false },
  ];

  it('renders nothing when inputs array is empty', () => {
    const { container } = render(<InputDisplay inputs={[]} />);
    expect(container.firstChild).toBeNull();
  });

  it('renders all configured inputs', () => {
    render(<InputDisplay inputs={mockInputs} />);
    
    expect(screen.getByText('Configured Inputs')).toBeInTheDocument();
    expect(screen.getByText('@customer_name')).toBeInTheDocument();
    expect(screen.getByText('@age')).toBeInTheDocument();
    expect(screen.getByText('@is_active')).toBeInTheDocument();
  });

  it('shows input types', () => {
    render(<InputDisplay inputs={mockInputs} />);
    
    expect(screen.getByText('text')).toBeInTheDocument();
    expect(screen.getByText('number')).toBeInTheDocument();
    expect(screen.getByText('boolean')).toBeInTheDocument();
  });

  it('shows required indicator for required inputs', () => {
    render(<InputDisplay inputs={mockInputs} />);
    
    // Check that the required input has an asterisk
    const badges = screen.getAllByTestId('badge');
    const customerNameBadge = badges.find(badge => 
      badge.textContent?.includes('@customer_name')
    );
    expect(customerNameBadge?.textContent).toContain('*');
  });

  it('calls onEdit when input badge is clicked', () => {
    const mockOnEdit = jest.fn();
    render(<InputDisplay inputs={mockInputs} onEdit={mockOnEdit} />);
    
    const badges = screen.getAllByTestId('badge');
    fireEvent.click(badges[0]);
    
    expect(mockOnEdit).toHaveBeenCalledWith(mockInputs[0]);
  });

  it('calls onRemove when X button is clicked', () => {
    const mockOnRemove = jest.fn();
    render(<InputDisplay inputs={mockInputs} onRemove={mockOnRemove} />);
    
    const removeButtons = screen.getAllByText('X');
    fireEvent.click(removeButtons[0].parentElement!);
    
    expect(mockOnRemove).toHaveBeenCalledWith('1');
  });

  it('does not show remove buttons when onRemove is not provided', () => {
    render(<InputDisplay inputs={mockInputs} />);
    
    expect(screen.queryByText('X')).not.toBeInTheDocument();
  });

  it('shows instruction text for using @ mentions', () => {
    render(<InputDisplay inputs={mockInputs} />);
    
    expect(screen.getByText(/Use.*@.*in the editor to reference inputs/)).toBeInTheDocument();
  });
});