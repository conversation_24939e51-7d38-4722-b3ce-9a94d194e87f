'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Rocket, Copy, CheckCircle, Loader2 } from 'lucide-react';
import { deploymentService } from '@/services/deployment.service';
import { toast } from 'sonner';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

interface DeployButtonProps {
  agentId: string | null;
  disabled?: boolean;
}

interface DeploymentInfo {
  id: string;
  agentId: string;
  deploymentUrl: string;
  apiKey: string;
  status: string;
  createdAt: string;
}

interface ApiDocumentation {
  title: string;
  description: string;
  version: string;
  endpoint: {
    url: string;
    method: string;
    headers: Record<string, string>;
  };
  inputs: Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
  }>;
  examples: {
    curl: string;
    javascript: string;
    python: string;
  };
}

export function DeployButton({ agentId, disabled }: DeployButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDeploying, setIsDeploying] = useState(false);
  const [deployment, setDeployment] = useState<DeploymentInfo | null>(null);
  const [apiDocs, setApiDocs] = useState<ApiDocumentation | null>(null);
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);

  const handleDeploy = async () => {
    if (!agentId) return;

    setIsDeploying(true);
    try {
      const result = await deploymentService.deployAgent(agentId);
      setDeployment(result);
      
      // Fetch API documentation
      const docs = await deploymentService.getApiDocumentation(result.id);
      setApiDocs(docs);
      
      toast.success('Deployment successful', {
        description: 'Your agent has been deployed and is ready to use.',
      });
    } catch (error) {
      toast.error('Deployment failed', {
        description: error instanceof Error ? error.message : 'Failed to deploy agent',
      });
    } finally {
      setIsDeploying(false);
    }
  };

  const copyToClipboard = async (text: string, field: string) => {
    try {
      // Try using the modern clipboard API first
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
          document.execCommand('copy');
          textArea.remove();
        } catch (err) {
          textArea.remove();
          throw new Error('Failed to copy');
        }
      }
      
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
      toast.success('Copied to clipboard', {
        description: `${field} has been copied to your clipboard.`,
      });
    } catch (error) {
      console.error('Copy failed:', error);
      toast.error('Failed to copy', {
        description: 'Please copy the text manually by selecting it.',
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled || !agentId}
          className="gap-2"
        >
          <Rocket className="h-4 w-4" />
          Deploy
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Deploy Agent as API</DialogTitle>
          <DialogDescription>
            Deploy your agent to make it accessible via a secure API endpoint.
          </DialogDescription>
        </DialogHeader>

        {!deployment ? (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Deploying your agent will generate:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>A unique API endpoint URL</li>
              <li>A secure API key for authentication</li>
              <li>Complete API documentation with examples</li>
            </ul>
            <Button
              onClick={handleDeploy}
              disabled={isDeploying}
              className="w-full"
            >
              {isDeploying ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deploying...
                </>
              ) : (
                <>
                  <Rocket className="mr-2 h-4 w-4" />
                  Deploy Agent
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>Deployment Successful</AlertTitle>
              <AlertDescription>
                Your agent is now available as an API. Save the API key below - you won't be able to see it again.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div>
                <Label htmlFor="api-url">API Endpoint</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    id="api-url"
                    value={deployment.deploymentUrl + '/execute'}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(deployment.deploymentUrl + '/execute', 'API URL')}
                  >
                    {copiedField === 'API URL' ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="api-key">API Key</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    id="api-key"
                    value={deployment.apiKey}
                    readOnly
                    type={showApiKey ? "text" : "password"}
                    className="font-mono text-sm cursor-pointer select-all"
                    onClick={() => setShowApiKey(!showApiKey)}
                    onFocus={(e) => e.target.select()}
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(deployment.apiKey, 'API Key')}
                    title="Copy API Key"
                  >
                    {copiedField === 'API Key' ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Click the field to {showApiKey ? 'hide' : 'show'} • Store this key securely. You won't be able to see it again.
                </p>
              </div>

              <div className="flex items-center gap-2">
                <Badge variant={deployment.status === 'ACTIVE' ? 'default' : 'secondary'}>
                  {deployment.status}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Deployed at {new Date(deployment.createdAt).toLocaleString()}
                </span>
              </div>
            </div>

            {apiDocs && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">API Documentation</h3>
                
                {apiDocs.inputs.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Input Parameters</h4>
                    <div className="rounded-lg border p-4 space-y-2">
                      {apiDocs.inputs.map((input) => (
                        <div key={input.name} className="text-sm">
                          <span className="font-mono font-medium">{input.name}</span>
                          <span className="text-muted-foreground"> ({input.type})</span>
                          {input.required && <Badge variant="outline" className="ml-2 text-xs">Required</Badge>}
                          {input.description && (
                            <p className="text-xs text-muted-foreground mt-1">{input.description}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Tabs defaultValue="curl" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="curl">cURL</TabsTrigger>
                    <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                    <TabsTrigger value="python">Python</TabsTrigger>
                  </TabsList>
                  <TabsContent value="curl" className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label>Example Request</Label>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(apiDocs.examples.curl, 'cURL example')}
                      >
                        {copiedField === 'cURL example' ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                      <code className="text-sm">{apiDocs.examples.curl}</code>
                    </pre>
                  </TabsContent>
                  <TabsContent value="javascript" className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label>Example Request</Label>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(apiDocs.examples.javascript, 'JavaScript example')}
                      >
                        {copiedField === 'JavaScript example' ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                      <code className="text-sm">{apiDocs.examples.javascript}</code>
                    </pre>
                  </TabsContent>
                  <TabsContent value="python" className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label>Example Request</Label>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(apiDocs.examples.python, 'Python example')}
                      >
                        {copiedField === 'Python example' ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <pre className="bg-muted p-4 rounded-lg overflow-x-auto">
                      <code className="text-sm">{apiDocs.examples.python}</code>
                    </pre>
                  </TabsContent>
                </Tabs>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}