/* Override BlockNote default styles */
.bn-container .bn-editor {
  border: none !important;
  box-shadow: none !important;
  padding: 0;
  background-color: transparent !important;
}

.bn-container .bn-editor:focus-within {
  box-shadow: none !important;
}

/* Override Mantine CSS variables for light mode */
html:not(.dark) .bn-container {
  --mantine-color-body: white !important;
  --mantine-color-text: rgb(17 24 39) !important;
  --mantine-color-bright: rgb(17 24 39) !important;
  --mantine-color-placeholder: rgb(156 163 175) !important;
  --mantine-color-anchor: rgb(59 130 246) !important;
  --mantine-color-default: white !important;
  --mantine-color-default-hover: rgb(249 250 251) !important;
  --mantine-color-default-color: rgb(17 24 39) !important;
  --mantine-color-default-border: rgb(229 231 235) !important;
}

/* Override Mantine CSS variables for dark mode */
.dark .bn-container {
  --mantine-color-body: rgb(31 41 55) !important;
  --mantine-color-text: rgb(243 244 246) !important;
  --mantine-color-bright: rgb(243 244 246) !important;
  --mantine-color-placeholder: rgb(107 114 128) !important;
  --mantine-color-anchor: rgb(147 197 253) !important;
  --mantine-color-default: rgb(31 41 55) !important;
  --mantine-color-default-hover: rgb(55 65 81) !important;
  --mantine-color-default-color: rgb(243 244 246) !important;
  --mantine-color-default-border: rgb(55 65 81) !important;
}

/* Force light mode styles when not in dark mode */
html:not(.dark) .bn-container .bn-editor {
  background-color: transparent !important;
  color: rgb(17 24 39) !important;
}

html:not(.dark) .bn-container .bn-editor * {
  color: inherit !important;
}

/* Ensure menus are light in light mode */
html:not(.dark) .bn-container .bn-slash-menu,
html:not(.dark) .bn-container .bn-formatting-toolbar,
html:not(.dark) .bn-container .bn-tooltip,
html:not(.dark) .bn-container .bn-block-menu,
html:not(.dark) .bn-container .bn-side-menu,
html:not(.dark) .bn-suggestion-menu {
  background-color: white !important;
  border-color: rgb(229 231 235) !important;
  color: rgb(17 24 39) !important;
}

html:not(.dark) .bn-container .bn-slash-menu-item,
html:not(.dark) .bn-container .bn-formatting-toolbar button,
html:not(.dark) .bn-container .bn-block-menu button,
html:not(.dark) .bn-container .bn-side-menu button,
html:not(.dark) .bn-suggestion-menu-item {
  color: rgb(17 24 39) !important;
}

html:not(.dark) .bn-container .bn-slash-menu-item:hover,
html:not(.dark) .bn-container .bn-formatting-toolbar button:hover,
html:not(.dark) .bn-container .bn-block-menu button:hover,
html:not(.dark) .bn-container .bn-side-menu button:hover,
html:not(.dark) .bn-suggestion-menu-item:hover {
  background-color: rgb(243 244 246) !important;
}

/* Reset Mantine theme overrides */
html:not(.dark) .bn-container {
  --mantine-color-scheme: light !important;
}

html:not(.dark) .bn-container [data-mantine-color-scheme="dark"] {
  --mantine-color-scheme: light !important;
}

/* Force theme based on data-theme attribute */
[data-theme="light"] .bn-container .bn-editor,
[data-theme="light"] .bn-container .bn-editor * {
  background-color: transparent !important;
  color: rgb(17 24 39) !important;
}

[data-theme="dark"] .bn-container .bn-editor,
[data-theme="dark"] .bn-container .bn-editor * {
  background-color: transparent !important;
  color: rgb(243 244 246) !important;
}

/* Style the block drag handles */
.bn-container .bn-block-outer .bn-drag-handle {
  opacity: 0;
  transition: opacity 0.2s;
}

.bn-container .bn-block-outer:hover .bn-drag-handle {
  opacity: 0.5;
}

.bn-container .bn-block-outer .bn-drag-handle:hover {
  opacity: 1;
}

/* Style the slash menu */
.bn-container .bn-slash-menu {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  border: 1px solid rgb(229 231 235);
}

/* Style tooltips */
.bn-container .bn-tooltip {
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* Style the formatting toolbar */
.bn-container .bn-formatting-toolbar {
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  border: 1px solid rgb(229 231 235);
}

/* Remove focus outlines and use subtle highlights instead */
.bn-container .bn-editor [contenteditable]:focus {
  outline: none;
}

/* Style placeholders */
.bn-container .bn-inline-content:has(.bn-placeholder):before {
  color: rgb(156 163 175);
}

/* Ensure proper text styles */
.bn-container .bn-editor {
  font-family: inherit;
  line-height: 1.75;
  color: rgb(17 24 39);
}

/* Dark mode styles */
.dark .bn-container .bn-editor {
  color: rgb(243 244 246);
  background-color: transparent;
}

.dark .bn-container .bn-editor * {
  color: rgb(243 244 246);
}

.dark .bn-container .bn-slash-menu {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
  color: rgb(243 244 246);
}

.dark .bn-container .bn-slash-menu-item {
  color: rgb(243 244 246);
}

.dark .bn-container .bn-slash-menu-item:hover {
  background-color: rgb(55 65 81);
}

.dark .bn-container .bn-formatting-toolbar {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
  color: rgb(243 244 246);
}

.dark .bn-container .bn-formatting-toolbar button {
  color: rgb(243 244 246);
}

.dark .bn-container .bn-formatting-toolbar button:hover {
  background-color: rgb(55 65 81);
}

.dark .bn-container .bn-formatting-toolbar button[data-active="true"] {
  background-color: rgb(75 85 99);
}

.dark .bn-container .bn-tooltip {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
  color: rgb(243 244 246);
}

.dark .bn-container .bn-inline-content:has(.bn-placeholder):before {
  color: rgb(107 114 128);
}

/* Dark mode for drag handles */
.dark .bn-container .bn-drag-handle {
  color: rgb(156 163 175);
}

.dark .bn-container .bn-drag-handle:hover {
  color: rgb(243 244 246);
  background-color: rgb(55 65 81);
}

/* Dark mode for block menu */
.dark .bn-container .bn-block-menu {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
}

.dark .bn-container .bn-block-menu button {
  color: rgb(243 244 246);
}

.dark .bn-container .bn-block-menu button:hover {
  background-color: rgb(55 65 81);
}

/* Dark mode for links */
.dark .bn-container a {
  color: rgb(147 197 253);
}

/* Dark mode for code blocks */
.dark .bn-container pre {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
}

.dark .bn-container code {
  background-color: rgb(31 41 55);
  color: rgb(248 113 113);
}

/* Dark mode for blockquotes */
.dark .bn-container blockquote {
  border-left-color: rgb(75 85 99);
  color: rgb(156 163 175);
}

/* Dark mode for tables */
.dark .bn-container table {
  border-color: rgb(55 65 81);
}

.dark .bn-container th {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
}

.dark .bn-container td {
  border-color: rgb(55 65 81);
}

/* Dark mode for selection */
.dark .bn-container .ProseMirror-selectednode {
  background-color: rgba(147, 197, 253, 0.2);
}

/* Dark mode for lists */
.dark .bn-container ul li::marker,
.dark .bn-container ol li::marker {
  color: rgb(156 163 175);
}

.bn-container .bn-editor h1 {
  font-size: 2.25rem;
  font-weight: 700;
  margin-top: 0;
  margin-bottom: 1rem;
}

.bn-container .bn-editor h2 {
  font-size: 1.875rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 0.75rem;
}

.bn-container .bn-editor h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.bn-container .bn-editor p {
  margin-bottom: 0.75rem;
}

/* Remove default padding from first and last blocks */
.bn-container .bn-editor > .bn-block-group > .bn-block-outer:first-child {
  margin-top: 0;
}

.bn-container .bn-editor > .bn-block-group > .bn-block-outer:last-child {
  margin-bottom: 0;
}

/* Style for input references */
.bn-editor [data-input-ref] {
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
  display: inline-block;
  font-weight: 600;
}

.bn-editor [data-input-ref]:hover {
  background-color: #c7d2fe !important;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.dark .bn-editor [data-input-ref]:hover {
  background-color: #4c1d95 !important;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}

.bn-editor [data-input-ref]:active {
  transform: scale(0.98);
}

/* Add a tooltip on hover */
.bn-editor [data-input-ref]::after {
  content: "Click to edit";
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 12px;
  font-weight: normal;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  margin-bottom: 4px;
}

.bn-editor [data-input-ref]:hover::after {
  opacity: 1;
}

/* Dark mode for @ mentions and slash menu popup */
.dark .bn-suggestion-menu {
  background-color: rgb(31 41 55) !important;
  border-color: rgb(55 65 81) !important;
  color: rgb(243 244 246) !important;
}

.dark .bn-suggestion-menu-item {
  color: rgb(243 244 246) !important;
}

.dark .bn-suggestion-menu-item:hover {
  background-color: rgb(55 65 81) !important;
}

.dark .bn-suggestion-menu-item-selected {
  background-color: rgb(75 85 99) !important;
}

/* Dark mode fixes for any remaining light elements */
.dark .bn-container [data-color-scheme="light"] {
  color-scheme: dark !important;
}

.dark .bn-container .bn-side-menu {
  background-color: rgb(31 41 55) !important;
  border-color: rgb(55 65 81) !important;
}

.dark .bn-container .bn-side-menu button {
  color: rgb(243 244 246) !important;
}

.dark .bn-container .bn-side-menu button:hover {
  background-color: rgb(55 65 81) !important;
}

/* Generation block styles */
.generation-block {
  margin: 0.5rem 0;
}