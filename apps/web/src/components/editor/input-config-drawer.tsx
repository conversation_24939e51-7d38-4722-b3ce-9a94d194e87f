'use client';

import React, { useState } from 'react';
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { InputType, BaseInputDefinition } from '@/types/input-types';
import { 
  Type, 
  Hash, 
  ToggleLeft, 
  Image, 
  Music, 
  FileText, 
  Table, 
  List, 
  Braces, 
  ChevronDown 
} from 'lucide-react';
import { InputPreview } from './input-preview';

// Re-export for backward compatibility
export type { InputType } from '@/types/input-types';
export type InputDefinition = BaseInputDefinition;

interface InputConfigDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (inputConfig: InputDefinition) => void;
  existingInput?: InputDefinition; // For editing
}

const INPUT_TYPE_OPTIONS: { value: InputType; label: string; description: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { value: 'text', label: 'Text', description: 'Single line text input', icon: Type },
  { value: 'number', label: 'Number', description: 'Numeric input field', icon: Hash },
  { value: 'boolean', label: 'True/False', description: 'Checkbox or toggle switch', icon: ToggleLeft },
  { value: 'image', label: 'Image', description: 'Image file upload', icon: Image },
  { value: 'audio', label: 'Audio', description: 'Audio file upload', icon: Music },
  { value: 'pdf', label: 'PDF/Document', description: 'PDF or document file upload', icon: FileText },
  { value: 'csv', label: 'CSV/Text', description: 'CSV or text file upload', icon: Table },
  { value: 'list', label: 'List', description: 'List of items', icon: List },
  { value: 'object', label: 'Object', description: 'Structured data with multiple fields', icon: Braces },
  { value: 'select', label: 'Select', description: 'Dropdown with predefined options', icon: ChevronDown },
];

export function InputConfigDrawer({
  open,
  onOpenChange,
  onSave,
  existingInput,
}: InputConfigDrawerProps) {
  const [name, setName] = useState(existingInput?.name || '');
  const [type, setType] = useState<InputType>(existingInput?.type || 'text');
  const [description, setDescription] = useState(existingInput?.description || '');
  const [required, setRequired] = useState(existingInput?.required || false);
  const [errors, setErrors] = useState<{ name?: string }>({});

  const validateName = (value: string) => {
    if (!value.trim()) {
      return 'Name is required';
    }
    if (!/^[a-zA-Z0-9_]+$/.test(value)) {
      return 'Name must be alphanumeric with underscores only';
    }
    return undefined;
  };

  const handleSave = () => {
    const nameError = validateName(name);
    if (nameError) {
      setErrors({ name: nameError });
      return;
    }

    const inputConfig: InputDefinition = {
      id: existingInput?.id || Date.now().toString(),
      name: name.trim(),
      type,
      description: description.trim() || undefined,
      required,
    };

    onSave(inputConfig);
    resetForm();
    onOpenChange(false);
  };

  const resetForm = () => {
    setName('');
    setType('text');
    setDescription('');
    setRequired(false);
    setErrors({});
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      resetForm();
    }
    onOpenChange(newOpen);
  };

  const selectedTypeInfo = INPUT_TYPE_OPTIONS.find(opt => opt.value === type);

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetContent side="right" className="w-[400px] sm:w-[540px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Configure Input Field</SheetTitle>
          <SheetDescription>
            Define the type and properties for your input field. This will determine how the field appears in forms.
          </SheetDescription>
        </SheetHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">
              Input Name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => {
                setName(e.target.value);
                setErrors({});
              }}
              placeholder="customer_name"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Use lowercase with underscores (e.g., first_name, user_email)
            </p>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="type">Input Type</Label>
            <Select value={type} onValueChange={(value) => setType(value as InputType)}>
              <SelectTrigger id="type">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {INPUT_TYPE_OPTIONS.map((option) => {
                  const Icon = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-start gap-3">
                        <Icon className="h-4 w-4 mt-0.5 text-muted-foreground" />
                        <div className="flex-1">
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-muted-foreground">{option.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {selectedTypeInfo && (
              <p className="text-xs text-muted-foreground">
                {selectedTypeInfo.description}
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter a helpful description for this input..."
              rows={3}
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="required"
              checked={required}
              onChange={(e) => setRequired(e.target.checked)}
              className="h-4 w-4 rounded border-gray-300"
            />
            <Label htmlFor="required" className="cursor-pointer">
              Required field
            </Label>
          </div>

          {/* Live Preview */}
          {name.trim() && (
            <InputPreview
              name={name}
              type={type}
              required={required}
              description={description}
            />
          )}
        </div>

        <SheetFooter className="mt-6">
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            {existingInput ? 'Update' : 'Save'} Input
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}