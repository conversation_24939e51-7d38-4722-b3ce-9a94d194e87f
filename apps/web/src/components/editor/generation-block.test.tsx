import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock blocknote modules to avoid ESM issues
jest.mock('@blocknote/react', () => ({
  createReactBlockSpec: jest.fn((config, component) => ({
    ...config,
    render: component.render,
  })),
}));

jest.mock('@blocknote/core', () => ({
  BlockNoteEditor: jest.fn(),
}));

// Import after mocking
import { GenerationBlock } from './generation-block';

// Mock BlockNoteEditor
const mockEditor = {
  updateBlock: jest.fn(),
};

// Mock block data
const mockBlock = {
  id: 'test-block-id',
  type: 'generation',
  props: {
    provider: 'openrouter',
    model: 'openai/gpt-3.5-turbo',
    temperature: 0.7,
    prompt: '',
    output: '',
    isGenerating: false,
    error: '',
  },
  content: [],
};

describe('GenerationBlock', () => {
  it('renders generation block with default props', () => {
    const Render = GenerationBlock.render;
    render(
      <Render
        block={mockBlock}
        editor={mockEditor as any}
      />
    );

    expect(screen.getByText('AI Generation')).toBeInTheDocument();
    expect(screen.getByText('gpt-3.5-turbo • 0.7')).toBeInTheDocument();
    expect(screen.getByText('Configure')).toBeInTheDocument();
    expect(screen.getByText('Prompt:')).toBeInTheDocument();
    expect(screen.getByText('Click Configure to set the prompt')).toBeInTheDocument();
  });

  it('shows loading state when generating', () => {
    const generatingBlock = {
      ...mockBlock,
      props: {
        ...mockBlock.props,
        isGenerating: true,
      },
    };

    const Render = GenerationBlock.render;
    render(
      <Render
        block={generatingBlock}
        editor={mockEditor as any}
      />
    );

    expect(screen.getByText('Generating...')).toBeInTheDocument();
  });

  it('shows output when available', () => {
    const blockWithOutput = {
      ...mockBlock,
      props: {
        ...mockBlock.props,
        output: 'This is the generated response',
      },
    };

    const Render = GenerationBlock.render;
    render(
      <Render
        block={blockWithOutput}
        editor={mockEditor as any}
      />
    );

    expect(screen.getByText('Output:')).toBeInTheDocument();
    expect(screen.getByText('This is the generated response')).toBeInTheDocument();
  });

  it('shows error when present', () => {
    const blockWithError = {
      ...mockBlock,
      props: {
        ...mockBlock.props,
        error: 'Failed to generate response',
      },
    };

    const Render = GenerationBlock.render;
    render(
      <Render
        block={blockWithError}
        editor={mockEditor as any}
      />
    );

    expect(screen.getByText('Failed to generate response')).toBeInTheDocument();
  });

  it('dispatches configure event when Configure button is clicked', () => {
    const dispatchEventSpy = jest.spyOn(document, 'dispatchEvent');
    
    const Render = GenerationBlock.render;
    render(
      <Render
        block={mockBlock}
        editor={mockEditor as any}
      />
    );

    const configureButton = screen.getByText('Configure');
    fireEvent.click(configureButton);

    expect(dispatchEventSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'generation-configure',
      })
    );

    dispatchEventSpy.mockRestore();
  });
});