import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BlockEditor } from './block-editor';

// Mock BlockNote modules
jest.mock('@blocknote/react', () => ({
  useCreateBlockNote: jest.fn(() => ({
    document: [],
    _tiptapEditor: {
      destroy: jest.fn(),
    },
  })),
  createReactBlockSpec: jest.fn((config, component) => ({
    ...config,
    render: component.render,
  })),
  SuggestionMenuController: ({ triggerCharacter, getItems }: any) => {
    // Simulate the getItems call to test slash menu items
    React.useEffect(() => {
      if (getItems) {
        getItems('').then((items: any) => {
          // Store items for testing
          (window as any).__testSlashMenuItems = items;
        });
      }
    }, [getItems]);
    return <div data-testid="suggestion-menu" data-trigger={trigger<PERSON>haracter} />;
  },
  getDefaultReactSlashMenuItems: jest.fn(() => [
    { title: 'Paragraph', onItemClick: jest.fn() },
    { title: 'Heading', onItemClick: jest.fn() }
  ]),
}));

jest.mock('@blocknote/mantine', () => ({
  BlockNoteView: ({ theme, className, slashMenu, children, onChange }: any) => (
    <div data-testid="blocknote-view" data-theme={theme} className={className} data-slash-menu={slashMenu}>
      {children}
      <button onClick={() => onChange && onChange()}>Trigger Change</button>
    </div>
  ),
}));

jest.mock('@blocknote/core', () => ({
  BlockNoteEditor: jest.fn(),
  filterSuggestionItems: jest.fn((items) => items),
  BlockNoteSchema: {
    create: jest.fn(() => ({
      blockSpecs: {},
    })),
  },
  defaultBlockSpecs: {},
}));

// Mock CSS imports
jest.mock('@blocknote/mantine/style.css', () => ({}));
jest.mock('./block-editor.css', () => ({}));

// Mock InputConfigDrawer
jest.mock('./input-config-drawer', () => ({
  InputConfigDrawer: ({ open, onOpenChange, onSave }: any) => (
    open ? (
      <div data-testid="input-config-drawer">
        <button onClick={() => onSave({ name: 'test_input', type: 'text' })}>
          Save Input
        </button>
      </div>
    ) : null
  ),
}));

// Mock KnowledgeBaseSearch
jest.mock('./knowledge-base-search', () => ({
  KnowledgeBaseSearch: ({ open, onOpenChange, onSelectDocument }: any) => (
    open ? (
      <div data-testid="knowledge-base-search">
        <button 
          onClick={() => onSelectDocument({
            id: 'doc1',
            fileName: 'test-document.pdf',
            fileType: 'application/pdf',
            status: 'COMPLETED',
            workspaceId: 'workspace1',
            uploadedAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })}
        >
          Select Document
        </button>
      </div>
    ) : null
  ),
}));

// Mock editor store
jest.mock('@/stores/editor.store', () => ({
  useEditorStore: jest.fn(() => ({
    addInputDefinition: jest.fn(),
    inputDefinitions: [],
  })),
}));

describe('BlockEditor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    delete (window as any).__testSlashMenuItems;
  });

  it('renders loading state initially', () => {
    const { useCreateBlockNote } = require('@blocknote/react');
    useCreateBlockNote.mockReturnValue(null);

    render(<BlockEditor />);
    
    expect(screen.getByText('Loading editor...')).toBeInTheDocument();
  });

  it('renders editor after loading', async () => {
    const mockEditor = {
      _tiptapEditor: {
        destroy: jest.fn(),
      },
    };
    const { useCreateBlockNote } = require('@blocknote/react');
    useCreateBlockNote.mockReturnValue(mockEditor);

    render(<BlockEditor />);

    await waitFor(() => {
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });
  });

  it('calls onChange when editor content changes', async () => {
    const mockOnChange = jest.fn();
    const mockEditor = {
      _tiptapEditor: {
        destroy: jest.fn(),
      },
      document: [{ type: 'paragraph', content: 'Test' }],
    };
    const { useCreateBlockNote } = require('@blocknote/react');
    
    useCreateBlockNote.mockReturnValue(mockEditor);

    const { getByTestId } = render(<BlockEditor onChange={mockOnChange} />);

    await waitFor(() => {
      expect(getByTestId('blocknote-view')).toBeInTheDocument();
    });

    // Note: In real implementation, onChange is triggered by BlockNoteView
    // This test verifies the setup is correct
  });

  it('applies custom className', async () => {
    const mockEditor = {
      _tiptapEditor: {
        destroy: jest.fn(),
      },
    };
    const { useCreateBlockNote } = require('@blocknote/react');
    useCreateBlockNote.mockReturnValue(mockEditor);

    render(<BlockEditor className="custom-class" />);

    await waitFor(() => {
      const container = screen.getByTestId('blocknote-view').parentElement;
      expect(container).toHaveClass('custom-class');
    });
  });

  it('renders with initial content', async () => {
    const initialContent = [
      { type: 'paragraph', content: 'Initial content' },
    ];
    const mockEditor = {
      _tiptapEditor: {
        destroy: jest.fn(),
      },
    };
    const { useCreateBlockNote } = require('@blocknote/react');
    
    useCreateBlockNote.mockImplementation((config: any) => {
      expect(config.initialContent).toEqual(initialContent);
      return mockEditor;
    });

    render(<BlockEditor initialContent={initialContent} />);

    await waitFor(() => {
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });
  });

  it('cleans up editor on unmount', async () => {
    const mockDestroy = jest.fn();
    const mockEditor = {
      _tiptapEditor: {
        destroy: mockDestroy,
      },
    };
    const { useCreateBlockNote } = require('@blocknote/react');
    useCreateBlockNote.mockReturnValue(mockEditor);

    const { unmount } = render(<BlockEditor />);

    await waitFor(() => {
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    unmount();

    expect(mockDestroy).toHaveBeenCalled();
  });

  it('renders BlockNoteView with slashMenu disabled', async () => {
    const mockEditor = {
      document: [],
      _tiptapEditor: {
        destroy: jest.fn(),
      },
    };
    const { useCreateBlockNote } = require('@blocknote/react');
    useCreateBlockNote.mockReturnValue(mockEditor);

    render(<BlockEditor />);
    
    await waitFor(() => {
      const blockNoteView = screen.getByTestId('blocknote-view');
      expect(blockNoteView).toHaveAttribute('data-slash-menu', 'false');
    });
  });

  it('renders with projectId prop', async () => {
    const mockEditor = {
      document: [],
      _tiptapEditor: {
        destroy: jest.fn(),
      },
    };
    const { useCreateBlockNote } = require('@blocknote/react');
    useCreateBlockNote.mockReturnValue(mockEditor);

    render(<BlockEditor projectId="test-project-id" />);
    
    await waitFor(() => {
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });
  });

  it('renders two SuggestionMenuController components', async () => {
    const mockEditor = {
      document: [],
      _tiptapEditor: {
        destroy: jest.fn(),
      },
    };
    const { useCreateBlockNote } = require('@blocknote/react');
    useCreateBlockNote.mockReturnValue(mockEditor);

    render(<BlockEditor />);
    
    await waitFor(() => {
      const suggestionMenus = screen.getAllByTestId('suggestion-menu');
      expect(suggestionMenus).toHaveLength(2);
      // First one for "/" commands
      expect(suggestionMenus[0]).toHaveAttribute('data-trigger', '/');
      // Second one for "@" mentions  
      expect(suggestionMenus[1]).toHaveAttribute('data-trigger', '@');
    });
  });
});