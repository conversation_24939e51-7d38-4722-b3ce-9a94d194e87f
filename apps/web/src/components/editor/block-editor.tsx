'use client';

import { BlockNoteEditor, filterSuggestionItems, BlockNoteSchema, defaultBlockSpecs } from '@blocknote/core';
import { 
  useCreateBlockNote,
  SuggestionMenuController,
  getDefaultReactSlashMenuItems,
  DefaultReactSuggestionItem
} from '@blocknote/react';
import { BlockNoteView } from '@blocknote/mantine';
import '@blocknote/mantine/style.css';
import '@blocknote/core/fonts/inter.css';
import './block-editor.css';
import React, { useEffect, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { useEditorStore } from '@/stores/editor.store';
import { useTheme } from 'next-themes';
import { FileText, Sparkles } from 'lucide-react';
import { KnowledgeBaseSearch } from './knowledge-base-search';
import { KnowledgeBaseDocument } from '@/types/document';
import { GenerationBlock } from './generation-block';
import { GenerationBlockConfig } from './generation-block-config';

interface BlockEditorProps {
  initialContent?: any;
  onChange?: (content: any) => void;
  className?: string;
  onInputClick?: (inputName: string) => void;
  projectId?: string;
}

// Create custom schema with Generation block
const schema = BlockNoteSchema.create({
  blockSpecs: {
    ...defaultBlockSpecs,
    generation: GenerationBlock,
  },
});

export function BlockEditor({ initialContent, onChange, className, onInputClick, projectId }: BlockEditorProps): React.ReactElement {
  const [isLoading, setIsLoading] = useState(true);
  const [showKnowledgeBase, setShowKnowledgeBase] = useState(false);
  const [showGenerationConfig, setShowGenerationConfig] = useState(false);
  const [configBlockId, setConfigBlockId] = useState<string | null>(null);
  const { inputDefinitions } = useEditorStore();
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  
  // Handle theme mounting
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Use resolvedTheme which gives us the actual theme being used
  const currentTheme = mounted ? resolvedTheme : 'light';

  // Ensure we always have at least one block
  const defaultContent = [{
    type: "paragraph",
    content: "",
  }];

  // Use initial content or default
  const processedContent = initialContent && initialContent.length > 0 
    ? initialContent 
    : defaultContent;
  
  // Create editor
  const editor = useCreateBlockNote({
    schema,
    initialContent: processedContent,
    uploadFile: async () => {
      throw new Error("File upload not implemented");
    },
  });

  // Create custom slash menu items
  const getCustomSlashMenuItems = useCallback((editor: BlockNoteEditor): DefaultReactSuggestionItem[] => {
    const defaultItems = getDefaultReactSlashMenuItems(editor);
    
    // Add Generation block command
    const generationItem: DefaultReactSuggestionItem = {
      title: "Generation",
      onItemClick: () => {
        const currentBlock = editor.getTextCursorPosition().block;
        const blocks = editor.insertBlocks(
          [
            {
              type: "generation",
              props: {
                provider: 'openrouter',
                model: 'openai/gpt-3.5-turbo',
                temperature: 0.7,
                prompt: '',
                output: '',
                isGenerating: false,
                error: '',
              },
            },
            {
              type: "paragraph",
              content: [],
            },
          ],
          currentBlock,
          "after"
        );
        
        // Move cursor to the paragraph block after generation block
        if (blocks && blocks.length > 1) {
          try {
            editor.setTextCursorPosition(blocks[1], "start");
          } catch (e) {
            console.error("Failed to set cursor position:", e);
          }
        }
        
        // Open config modal for the newly created generation block
        if (blocks && blocks[0]) {
          setConfigBlockId(blocks[0].id);
          setShowGenerationConfig(true);
        }
      },
      aliases: ["ai", "llm", "generate"],
      group: "AI Blocks",
      icon: <Sparkles className="w-4 h-4" />,
      subtext: "Add an AI generation block",
    };
    
    return [...defaultItems, generationItem];
  }, []);

  // Create @ mention items for inputs
  const getInputMentionItems = useCallback((): DefaultReactSuggestionItem[] => {
    const items: DefaultReactSuggestionItem[] = [];
    
    // Add Knowledge Base option first
    if (projectId) {
      items.push({
        title: "Knowledge Base",
        onItemClick: () => {
          setShowKnowledgeBase(true);
        },
        subtext: "Reference project documents",
        group: "Special",
        icon: <FileText className="w-4 h-4" />,
      });
    }
    
    // Add input definitions
    const inputItems = inputDefinitions.map((input) => ({
      title: input.name,
      onItemClick: () => {
        editor.insertInlineContent([
          {
            type: "text",
            text: `@${input.name}`,
            styles: {
              backgroundColor: "#e0e7ff",
              textColor: "#4338ca",
              bold: true,
            },
          },
          " ", // Add space after mention
        ]);
      },
      subtext: `${input.type}${input.required ? ' (required)' : ''}`,
      group: "Inputs",
    }));
    
    return [...items, ...inputItems];
  }, [inputDefinitions, editor, projectId]);

  // Handle document selection from Knowledge Base
  const handleDocumentSelect = useCallback((document: KnowledgeBaseDocument) => {
    editor.insertInlineContent([
      {
        type: "text",
        text: `@KnowledgeBase:${document.fileName}`,
        styles: {
          backgroundColor: "#f3e8ff",
          textColor: "#6b21a8",
          bold: true,
        },
      },
      " ", // Add space after mention
    ]);
    setShowKnowledgeBase(false);
  }, [editor]);

  // Handle Generation block configuration save
  const handleGenerationConfigSave = useCallback((config: any) => {
    if (!configBlockId || !editor) return;

    const block = editor.getBlock(configBlockId);
    if (block && block.type === 'generation') {
      editor.updateBlock(configBlockId, {
        type: 'generation',
        props: {
          ...block.props,
          ...config,
        },
      });
    }
    setShowGenerationConfig(false);
    setConfigBlockId(null);
  }, [configBlockId, editor]);

  // Handle Generation block Configure button clicks
  useEffect(() => {
    if (!editor) return;

    const handleGenerationConfigure = (event: CustomEvent) => {
      const blockId = event.detail.blockId;
      setConfigBlockId(blockId);
      setShowGenerationConfig(true);
    };

    document.addEventListener('generation-configure', handleGenerationConfigure as EventListener);
    return () => {
      document.removeEventListener('generation-configure', handleGenerationConfigure as EventListener);
    };
  }, [editor]);

  // Handle clicks on input references
  useEffect(() => {
    if (!editor || !onInputClick) return;

    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const inputRef = target.getAttribute('data-input-ref');
      
      if (inputRef && target.closest('.bn-editor')) {
        event.preventDefault();
        event.stopPropagation();
        onInputClick(inputRef);
      }
    };

    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, [editor, onInputClick]);

  useEffect(() => {
    if (editor) {
      setIsLoading(false);
    }
  }, [editor]);

  if (isLoading || !editor || !mounted) {
    return (
      <div className={cn("w-full h-full min-h-[400px] flex items-center justify-center", className)}>
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-gray-300 dark:border-gray-600 border-t-gray-600 dark:border-t-gray-400 rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-gray-600 dark:text-gray-400 text-sm">Loading editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full h-full min-h-[400px]", className)} data-theme={currentTheme}>
      <BlockNoteView 
        editor={editor} 
        theme={currentTheme === "dark" ? "dark" : "light"}
        slashMenu={false}
        onChange={() => {
          onChange?.(editor.document);
        }}
        data-color-scheme={currentTheme === "dark" ? "dark" : "light"}
      >
        <SuggestionMenuController
          triggerCharacter={"/"}
          getItems={async (query) => 
            filterSuggestionItems(
              getCustomSlashMenuItems(editor), 
              query
            )
          }
        />
        <SuggestionMenuController
          triggerCharacter={"@"}
          getItems={async (query) => 
            filterSuggestionItems(
              getInputMentionItems(), 
              query
            )
          }
        />
      </BlockNoteView>
      {projectId && (
        <KnowledgeBaseSearch
          open={showKnowledgeBase}
          onOpenChange={setShowKnowledgeBase}
          onSelectDocument={handleDocumentSelect}
          projectId={projectId}
        />
      )}
      <GenerationBlockConfig
        open={showGenerationConfig}
        onOpenChange={setShowGenerationConfig}
        initialConfig={
          configBlockId && editor
            ? editor.getBlock(configBlockId)?.props || {}
            : {}
        }
        onSave={handleGenerationConfigSave}
      />
    </div>
  );
}