'use client';

import React from 'react';
import { BaseInputDefinition } from '@/types/input-types';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface InputDisplayProps {
  inputs: BaseInputDefinition[];
  onRemove?: (id: string) => void;
  onEdit?: (input: BaseInputDefinition) => void;
}

const INPUT_TYPE_COLORS: Record<string, string> = {
  text: 'bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50',
  number: 'bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50',
  boolean: 'bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:hover:bg-purple-900/50',
  image: 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:hover:bg-yellow-900/50',
  audio: 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:hover:bg-indigo-900/50',
  pdf: 'bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50',
  csv: 'bg-orange-100 text-orange-700 hover:bg-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:hover:bg-orange-900/50',
  list: 'bg-teal-100 text-teal-700 hover:bg-teal-200 dark:bg-teal-900/30 dark:text-teal-300 dark:hover:bg-teal-900/50',
  object: 'bg-pink-100 text-pink-700 hover:bg-pink-200 dark:bg-pink-900/30 dark:text-pink-300 dark:hover:bg-pink-900/50',
  select: 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600',
};

export function InputDisplay({ inputs, onRemove, onEdit }: InputDisplayProps) {
  if (inputs.length === 0) {
    return null;
  }

  return (
    <div className="border rounded-lg p-4 mb-4 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Configured Inputs</h3>
      <div className="flex flex-wrap gap-2">
        {inputs.map((input) => (
          <div
            key={input.id}
            className="group relative inline-flex items-center gap-1"
          >
            <Badge
              variant="secondary"
              className={`cursor-pointer transition-colors ${INPUT_TYPE_COLORS[input.type] || 'bg-gray-100 dark:bg-gray-700'}`}
              onClick={() => onEdit?.(input)}
            >
              <span className="font-mono text-xs">@{input.name}</span>
              <span className="ml-2 text-xs opacity-70">{input.type}</span>
              {input.required && (
                <span className="ml-1 text-red-500 dark:text-red-400">*</span>
              )}
            </Badge>
            {onRemove && (
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => onRemove(input.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        ))}
      </div>
      <p className="text-xs text-gray-500 dark:text-gray-400 mt-3">
        Use <code className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded">@</code> in the editor to reference inputs
      </p>
    </div>
  );
}