import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { InputConfigDrawer } from './input-config-drawer';
import type { BaseInputDefinition } from '@/types/input-types';

// Mock UI components
jest.mock('@/components/ui/sheet', () => ({
  Sheet: ({ open, children }: any) => open ? <div data-testid="sheet">{children}</div> : null,
  SheetContent: ({ children }: any) => <div data-testid="sheet-content">{children}</div>,
  SheetHeader: ({ children }: any) => <div>{children}</div>,
  SheetTitle: ({ children }: any) => <h2>{children}</h2>,
  SheetDescription: ({ children }: any) => <p>{children}</p>,
  SheetFooter: ({ children }: any) => <div>{children}</div>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant }: any) => (
    <button onClick={onClick} data-variant={variant}>{children}</button>
  ),
}));

jest.mock('@/components/ui/input', () => ({
  Input: ({ value, onChange, placeholder, className, id }: any) => (
    <input
      id={id}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
      data-testid={`input-${id}`}
    />
  ),
}));

jest.mock('@/components/ui/label', () => ({
  Label: ({ children, htmlFor }: any) => <label htmlFor={htmlFor}>{children}</label>,
}));

jest.mock('@/components/ui/select', () => ({
  Select: ({ value, onValueChange, children }: any) => {
    const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
      onValueChange(e.target.value);
    };
    return (
      <div data-testid="select-wrapper">
        <select value={value} onChange={handleChange} data-testid="select-type">
          <option value="text">Text</option>
          <option value="number">Number</option>
          <option value="boolean">True/False</option>
          <option value="image">Image</option>
          <option value="audio">Audio</option>
          <option value="pdf">PDF/Document</option>
          <option value="csv">CSV/Text</option>
          <option value="list">List</option>
          <option value="object">Object</option>
          <option value="select">Select</option>
        </select>
        {children}
      </div>
    );
  },
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => <div data-value={value}>{children}</div>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: () => null,
}));

jest.mock('@/components/ui/textarea', () => ({
  Textarea: ({ value, onChange, placeholder, id }: any) => (
    <textarea
      id={id}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      data-testid={`textarea-${id}`}
    />
  ),
}));

describe('InputConfigDrawer', () => {
  const mockOnOpenChange = jest.fn();
  const mockOnSave = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(
      <InputConfigDrawer
        open={true}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
      />
    );

    expect(screen.getByTestId('sheet')).toBeInTheDocument();
    expect(screen.getByText('Configure Input Field')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(
      <InputConfigDrawer
        open={false}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
      />
    );

    expect(screen.queryByTestId('sheet')).not.toBeInTheDocument();
  });

  it('validates name field is required', async () => {
    render(
      <InputConfigDrawer
        open={true}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
      />
    );

    const saveButton = screen.getByText('Save Input');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Name is required')).toBeInTheDocument();
    });

    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('validates name field format', async () => {
    render(
      <InputConfigDrawer
        open={true}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
      />
    );

    const nameInput = screen.getByTestId('input-name');
    fireEvent.change(nameInput, { target: { value: 'invalid-name!' } });

    const saveButton = screen.getByText('Save Input');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Name must be alphanumeric with underscores only')).toBeInTheDocument();
    });

    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('saves valid input configuration', async () => {
    render(
      <InputConfigDrawer
        open={true}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
      />
    );

    // Fill in form
    const nameInput = screen.getByTestId('input-name');
    fireEvent.change(nameInput, { target: { value: 'customer_name' } });

    const typeSelect = screen.getByTestId('select-type');
    fireEvent.change(typeSelect, { target: { value: 'text' } });

    const descriptionTextarea = screen.getByTestId('textarea-description');
    fireEvent.change(descriptionTextarea, { target: { value: 'Customer full name' } });

    const requiredCheckbox = screen.getByRole('checkbox');
    fireEvent.click(requiredCheckbox);

    const saveButton = screen.getByText('Save Input');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith({
        id: expect.any(String),
        name: 'customer_name',
        type: 'text',
        description: 'Customer full name',
        required: true,
      });
    });

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('resets form when canceled', () => {
    render(
      <InputConfigDrawer
        open={true}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
      />
    );

    // Fill in form
    const nameInput = screen.getByTestId('input-name');
    fireEvent.change(nameInput, { target: { value: 'test_input' } });

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('loads existing input for editing', () => {
    const existingInput: BaseInputDefinition = {
      id: '123',
      name: 'existing_field',
      type: 'number',
      description: 'Existing description',
      required: true,
    };

    render(
      <InputConfigDrawer
        open={true}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
        existingInput={existingInput}
      />
    );

    const nameInput = screen.getByTestId('input-name') as HTMLInputElement;
    expect(nameInput.value).toBe('existing_field');

    const typeSelect = screen.getByTestId('select-type') as HTMLSelectElement;
    expect(typeSelect.value).toBe('number');

    const descriptionTextarea = screen.getByTestId('textarea-description') as HTMLTextAreaElement;
    expect(descriptionTextarea.value).toBe('Existing description');

    const requiredCheckbox = screen.getByRole('checkbox') as HTMLInputElement;
    expect(requiredCheckbox.checked).toBe(true);

    // Check that it shows "Update" instead of "Save"
    expect(screen.getByText('Update Input')).toBeInTheDocument();
  });

  it('displays all input type options', () => {
    render(
      <InputConfigDrawer
        open={true}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
      />
    );

    const typeSelect = screen.getByTestId('select-type');
    const options = typeSelect.querySelectorAll('option');

    expect(options).toHaveLength(10);
    expect(Array.from(options).map(opt => opt.value)).toEqual([
      'text', 'number', 'boolean', 'image', 'audio', 
      'pdf', 'csv', 'list', 'object', 'select'
    ]);
  });

  it('clears errors when user types', async () => {
    render(
      <InputConfigDrawer
        open={true}
        onOpenChange={mockOnOpenChange}
        onSave={mockOnSave}
      />
    );

    // Trigger validation error
    const saveButton = screen.getByText('Save Input');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Name is required')).toBeInTheDocument();
    });

    // Type in the input
    const nameInput = screen.getByTestId('input-name');
    fireEvent.change(nameInput, { target: { value: 'a' } });

    // Error should be cleared
    expect(screen.queryByText('Name is required')).not.toBeInTheDocument();
  });
});