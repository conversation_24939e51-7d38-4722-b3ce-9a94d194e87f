'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, FileText, FileSpreadsheet, FileImage, File, AlertCircle } from 'lucide-react';
import { KnowledgeBaseDocument } from '@/types/document';
import { projectService } from '@/services/project.service';

interface KnowledgeBaseSearchProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectDocument: (document: KnowledgeBaseDocument) => void;
  projectId: string;
}

export function KnowledgeBaseSearch({
  open,
  onOpenChange,
  onSelectDocument,
  projectId,
}: KnowledgeBaseSearchProps) {
  const [documents, setDocuments] = useState<KnowledgeBaseDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState('');

  useEffect(() => {
    if (open) {
      fetchDocuments();
    }
  }, [open, projectId]);

  const fetchDocuments = async () => {
    setLoading(true);
    setError(null);
    try {
      const docs = await projectService.getProjectDocuments(projectId);
      // Filter to only show completed documents
      const completedDocs = docs.filter(doc => doc.status === 'COMPLETED');
      setDocuments(completedDocs);
    } catch (err) {
      setError('Failed to load documents. Please try again.');
      console.error('Error fetching documents:', err);
    } finally {
      setLoading(false);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <FileText className="w-4 h-4" />;
    if (fileType.includes('spreadsheet') || fileType.includes('excel')) 
      return <FileSpreadsheet className="w-4 h-4" />;
    if (fileType.includes('image')) return <FileImage className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  const formatFileSize = (bytes?: number | null) => {
    if (!bytes) return '';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const filteredDocuments = documents.filter(doc =>
    doc.fileName.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Select Document from Knowledge Base</DialogTitle>
        </DialogHeader>
        
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span className="ml-2">Loading documents...</span>
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : documents.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 mx-auto text-gray-400 mb-3" />
            <p className="text-gray-600">No documents found in this project.</p>
            <p className="text-sm text-gray-500 mt-2">
              Add documents to the Knowledge Base to reference them here.
            </p>
          </div>
        ) : (
          <Command>
            <CommandInput
              placeholder="Search documents..."
              value={search}
              onValueChange={setSearch}
            />
            <CommandList>
              <CommandEmpty>No documents found.</CommandEmpty>
              <CommandGroup heading="Available Documents">
                {filteredDocuments.map((doc) => (
                  <CommandItem
                    key={doc.id}
                    onSelect={() => {
                      onSelectDocument(doc);
                      setSearch('');
                    }}
                    className="flex items-center justify-between py-3 cursor-pointer"
                  >
                    <div className="flex items-center space-x-3 flex-1">
                      {getFileIcon(doc.fileType)}
                      <div className="flex-1">
                        <p className="font-medium">{doc.fileName}</p>
                        <p className="text-sm text-gray-500">
                          {formatFileSize(doc.fileSize)} • Uploaded {formatDate(doc.uploadedAt)}
                        </p>
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        )}
        
        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}