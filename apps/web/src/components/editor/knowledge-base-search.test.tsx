import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { KnowledgeBaseSearch } from './knowledge-base-search';
import { projectService } from '@/services/project.service';

// Mock the project service
jest.mock('@/services/project.service', () => ({
  projectService: {
    getProjectDocuments: jest.fn(),
  },
}));

// Mock the UI components
jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div>{children}</div>,
  DialogHeader: ({ children }: any) => <div>{children}</div>,
  DialogTitle: ({ children }: any) => <h2>{children}</h2>,
}));

jest.mock('@/components/ui/command', () => ({
  Command: ({ children }: any) => <div>{children}</div>,
  CommandEmpty: ({ children }: any) => <div data-testid="command-empty">{children}</div>,
  CommandGroup: ({ children, heading }: any) => (
    <div>
      <h3>{heading}</h3>
      {children}
    </div>
  ),
  CommandInput: ({ placeholder, value, onValueChange }: any) => (
    <input
      data-testid="search-input"
      placeholder={placeholder}
      value={value}
      onChange={(e) => onValueChange?.(e.target.value)}
    />
  ),
  CommandItem: ({ children, onSelect }: any) => (
    <div data-testid="command-item" onClick={() => onSelect?.()}>
      {children}
    </div>
  ),
  CommandList: ({ children }: any) => <div>{children}</div>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick }: any) => (
    <button onClick={onClick}>{children}</button>
  ),
}));

jest.mock('@/components/ui/alert', () => ({
  Alert: ({ children }: any) => <div data-testid="alert">{children}</div>,
  AlertDescription: ({ children }: any) => <div>{children}</div>,
}));

describe('KnowledgeBaseSearch', () => {
  const mockOnOpenChange = jest.fn();
  const mockOnSelectDocument = jest.fn();
  const projectId = 'test-project-id';

  const mockDocuments = [
    {
      id: 'doc1',
      fileName: 'test-document.pdf',
      fileType: 'application/pdf',
      fileSize: 1024000,
      status: 'COMPLETED' as const,
      workspaceId: 'workspace1',
      uploadedAt: '2024-01-15T10:00:00Z',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
    },
    {
      id: 'doc2',
      fileName: 'spreadsheet.xlsx',
      fileType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      fileSize: 2048000,
      status: 'COMPLETED' as const,
      workspaceId: 'workspace1',
      uploadedAt: '2024-01-14T10:00:00Z',
      createdAt: '2024-01-14T10:00:00Z',
      updatedAt: '2024-01-14T10:00:00Z',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows loading state initially', async () => {
    jest.mocked(projectService.getProjectDocuments).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    expect(screen.getByText('Loading documents...')).toBeInTheDocument();
  });

  it('fetches and displays documents when opened', async () => {
    jest.mocked(projectService.getProjectDocuments).mockResolvedValue(mockDocuments);

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(projectService.getProjectDocuments).toHaveBeenCalledWith(projectId);
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
      expect(screen.getByText('spreadsheet.xlsx')).toBeInTheDocument();
    });
  });

  it('filters out non-completed documents', async () => {
    const documentsWithPending = [
      ...mockDocuments,
      {
        id: 'doc3',
        fileName: 'pending-doc.txt',
        fileType: 'text/plain',
        status: 'PENDING' as const,
        workspaceId: 'workspace1',
        uploadedAt: '2024-01-13T10:00:00Z',
        createdAt: '2024-01-13T10:00:00Z',
        updatedAt: '2024-01-13T10:00:00Z',
      },
    ];

    jest.mocked(projectService.getProjectDocuments).mockResolvedValue(documentsWithPending);

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
      expect(screen.getByText('spreadsheet.xlsx')).toBeInTheDocument();
      expect(screen.queryByText('pending-doc.txt')).not.toBeInTheDocument();
    });
  });

  it('shows empty state when no documents are available', async () => {
    jest.mocked(projectService.getProjectDocuments).mockResolvedValue([]);

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('No documents found in this project.')).toBeInTheDocument();
      expect(screen.getByText('Add documents to the Knowledge Base to reference them here.')).toBeInTheDocument();
    });
  });

  it('shows error state when fetching fails', async () => {
    jest.mocked(projectService.getProjectDocuments).mockRejectedValue(new Error('Network error'));

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load documents. Please try again.')).toBeInTheDocument();
    });
  });

  it('filters documents based on search input', async () => {
    jest.mocked(projectService.getProjectDocuments).mockResolvedValue(mockDocuments);

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
      expect(screen.getByText('spreadsheet.xlsx')).toBeInTheDocument();
    });

    const searchInput = screen.getByTestId('search-input');
    fireEvent.change(searchInput, { target: { value: 'test' } });

    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
      expect(screen.queryByText('spreadsheet.xlsx')).not.toBeInTheDocument();
    });
  });

  it('calls onSelectDocument when a document is selected', async () => {
    jest.mocked(projectService.getProjectDocuments).mockResolvedValue(mockDocuments);

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    });

    const firstDocument = screen.getAllByTestId('command-item')[0];
    fireEvent.click(firstDocument);

    expect(mockOnSelectDocument).toHaveBeenCalledWith(mockDocuments[0]);
  });

  it('closes dialog when cancel button is clicked', async () => {
    jest.mocked(projectService.getProjectDocuments).mockResolvedValue(mockDocuments);

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    });

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('does not render when closed', () => {
    render(
      <KnowledgeBaseSearch
        open={false}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
  });

  it('refetches documents when reopened', async () => {
    jest.mocked(projectService.getProjectDocuments).mockResolvedValue(mockDocuments);

    const { rerender } = render(
      <KnowledgeBaseSearch
        open={false}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    expect(projectService.getProjectDocuments).not.toHaveBeenCalled();

    rerender(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(projectService.getProjectDocuments).toHaveBeenCalledWith(projectId);
    });
  });

  it('displays correct file icons based on file type', async () => {
    const documentsWithVariousTypes = [
      {
        id: 'doc1',
        fileName: 'document.pdf',
        fileType: 'application/pdf',
        status: 'COMPLETED' as const,
        workspaceId: 'workspace1',
        uploadedAt: '2024-01-15T10:00:00Z',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      },
      {
        id: 'doc2',
        fileName: 'image.png',
        fileType: 'image/png',
        status: 'COMPLETED' as const,
        workspaceId: 'workspace1',
        uploadedAt: '2024-01-14T10:00:00Z',
        createdAt: '2024-01-14T10:00:00Z',
        updatedAt: '2024-01-14T10:00:00Z',
      },
    ];

    jest.mocked(projectService.getProjectDocuments).mockResolvedValue(documentsWithVariousTypes);

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('document.pdf')).toBeInTheDocument();
      expect(screen.getByText('image.png')).toBeInTheDocument();
    });
  });

  it('formats file sizes correctly', async () => {
    jest.mocked(projectService.getProjectDocuments).mockResolvedValue(mockDocuments);

    render(
      <KnowledgeBaseSearch
        open={true}
        onOpenChange={mockOnOpenChange}
        onSelectDocument={mockOnSelectDocument}
        projectId={projectId}
      />
    );

    await waitFor(() => {
      // 1024000 bytes = 1000.0 KB
      expect(screen.getByText(/1000\.0 KB/)).toBeInTheDocument();
      // 2048000 bytes = 2.0 MB
      expect(screen.getByText(/2\.0 MB/)).toBeInTheDocument();
    });
  });
});