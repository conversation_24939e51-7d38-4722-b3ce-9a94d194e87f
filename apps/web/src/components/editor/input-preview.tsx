'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { InputType } from '@/types/input-types';
import { Upload, Plus, X } from 'lucide-react';

interface InputPreviewProps {
  name: string;
  type: InputType;
  required?: boolean;
  description?: string;
}

export function InputPreview({ name, type, required, description }: InputPreviewProps) {
  const renderPreview = () => {
    switch (type) {
      case 'text':
        return (
          <Input 
            placeholder={`Enter ${name}...`}
            disabled
          />
        );
      
      case 'number':
        return (
          <Input 
            type="number"
            placeholder="0"
            disabled
          />
        );
      
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <input 
              type="checkbox" 
              id="preview-checkbox"
              disabled
              className="h-4 w-4 rounded border-gray-300"
            />
            <Label htmlFor="preview-checkbox" className="cursor-default">
              {name}
            </Label>
          </div>
        );
      
      case 'image':
      case 'audio':
      case 'pdf':
      case 'csv':
        const fileTypeLabels = {
          image: 'image',
          audio: 'audio file',
          pdf: 'PDF document',
          csv: 'CSV file'
        };
        return (
          <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
            <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600">
              Click to upload {fileTypeLabels[type]}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              or drag and drop
            </p>
          </div>
        );
      
      case 'select':
        return (
          <Select disabled>
            <SelectTrigger>
              <SelectValue placeholder={`Select ${name}...`} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="option1">Option 1</SelectItem>
              <SelectItem value="option2">Option 2</SelectItem>
              <SelectItem value="option3">Option 3</SelectItem>
            </SelectContent>
          </Select>
        );
      
      case 'list':
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Input placeholder="Item 1" disabled className="flex-1" />
              <Button size="sm" variant="ghost" disabled>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Input placeholder="Item 2" disabled className="flex-1" />
              <Button size="sm" variant="ghost" disabled>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <Button size="sm" variant="outline" disabled className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>
        );
      
      case 'object':
        return (
          <div className="space-y-3 p-3 border rounded-md bg-gray-50">
            <div>
              <Label className="text-xs">Field 1</Label>
              <Input placeholder="Value" disabled />
            </div>
            <div>
              <Label className="text-xs">Field 2</Label>
              <Input placeholder="Value" disabled />
            </div>
          </div>
        );
      
      default:
        return <div className="text-gray-500">Preview not available</div>;
    }
  };

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-700">Preview</h4>
      <div className="p-4 border rounded-md bg-gray-50">
        <Label className="text-sm font-medium mb-2 block">
          {name} {required && <span className="text-red-500">*</span>}
        </Label>
        {description && (
          <p className="text-xs text-gray-600 mb-3">{description}</p>
        )}
        {renderPreview()}
      </div>
    </div>
  );
}