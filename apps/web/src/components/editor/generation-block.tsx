'use client';

import React from 'react';
import { BlockNoteEditor } from '@blocknote/core';
import { createReactBlockSpec } from '@blocknote/react';
import { Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';

export interface GenerationBlockProps {
  provider?: string;
  model?: string;
  temperature?: number;
  prompt?: string;
  output?: string;
  isGenerating?: boolean;
  error?: string;
}

export const GenerationBlock = createReactBlockSpec(
  {
    type: 'generation' as const,
    propSchema: {
      provider: {
        default: 'openrouter',
      },
      model: {
        default: 'openai/gpt-3.5-turbo',
      },
      temperature: {
        default: 0.7,
      },
      prompt: {
        default: '',
      },
      output: {
        default: '',
      },
      isGenerating: {
        default: false,
      },
      error: {
        default: '',
      },
    },
    content: 'none',
    containsInlineContent: false
  },
  {
    render: (props) => {
      const { block, editor, contentRef } = props;
      const { provider, model, temperature, output, isGenerating, error, prompt } = block.props;

      return (
        <div className="generation-block">
          <div className="px-3 py-2 border rounded-lg bg-muted/30">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="w-4 h-4 text-primary flex-shrink-0" />
              <span className="text-sm font-medium">AI Generation</span>
              <span className="text-xs text-muted-foreground flex-1">
                {model?.split('/').pop()} • {temperature}
              </span>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 px-2 text-xs"
                onClick={() => {
                  // Dispatch event to open configuration modal
                  const event = new CustomEvent('generation-configure', {
                    detail: { blockId: block.id }
                  });
                  document.dispatchEvent(event);
                }}
              >
                Configure
              </Button>
            </div>

            {isGenerating && (
              <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                <div className="w-3 h-3 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                Generating...
              </div>
            )}

            {output && !isGenerating && (
              <div className="mt-2 p-2 bg-muted/50 rounded text-sm">
                <div className="text-xs text-muted-foreground mb-1">Output:</div>
                <div className="whitespace-pre-wrap">{output}</div>
              </div>
            )}

            {error && (
              <div className="mt-2 p-2 bg-destructive/10 text-destructive rounded text-sm">
                {error}
              </div>
            )}
          </div>
        </div>
      );
    },
  }
);