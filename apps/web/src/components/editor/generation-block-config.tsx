'use client';

import React, { useState } from 'react';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetHeader,
  SheetTit<PERSON>,
} from '@/components/ui/sheet';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { GenerationBlockProps } from './generation-block';

interface GenerationBlockConfigProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialConfig: Partial<GenerationBlockProps>;
  onSave: (config: Partial<GenerationBlockProps>) => void;
}

// OpenRouter supported models with metadata
const OPENROUTER_MODELS = [
  { 
    value: 'openai/gpt-3.5-turbo', 
    label: 'GPT-3.5 Turbo',
    description: 'Fast & versatile',
    icon: '🚀'
  },
  { 
    value: 'openai/gpt-4', 
    label: 'GPT-4',
    description: 'Advanced reasoning',
    icon: '🧠'
  },
  { 
    value: 'openai/gpt-4-turbo', 
    label: 'GPT-4 Turbo',
    description: 'Faster GPT-4',
    icon: '⚡'
  },
  { 
    value: 'anthropic/claude-2', 
    label: 'Claude 2',
    description: 'Great for writing',
    icon: '📝'
  },
  { 
    value: 'anthropic/claude-instant-1', 
    label: 'Claude Instant',
    description: 'Quick responses',
    icon: '💬'
  },
  { 
    value: 'google/gemini-pro', 
    label: 'Gemini Pro',
    description: 'Google\'s best',
    icon: '✨'
  },
  { 
    value: 'meta-llama/llama-2-70b-chat', 
    label: 'Llama 2 70B',
    description: 'Open source',
    icon: '🦙'
  },
  { 
    value: 'mistralai/mistral-7b-instruct', 
    label: 'Mistral 7B',
    description: 'Small & efficient',
    icon: '🌟'
  },
];

export function GenerationBlockConfig({
  open,
  onOpenChange,
  initialConfig,
  onSave,
}: GenerationBlockConfigProps) {
  const [model, setModel] = useState(initialConfig.model || 'openai/gpt-3.5-turbo');
  const [temperature, setTemperature] = useState(initialConfig.temperature || 0.7);

  const handleSave = () => {
    onSave({
      provider: 'openrouter', // Always use openrouter internally
      model,
      temperature,
    });
    onOpenChange(false);
  };

  const handleCancel = () => {
    // Reset to initial values
    setModel(initialConfig.model || 'openai/gpt-3.5-turbo');
    setTemperature(initialConfig.temperature || 0.7);
    onOpenChange(false);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[400px] sm:w-[480px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Generation Settings</SheetTitle>
          <SheetDescription>
            Choose AI model and creativity level
          </SheetDescription>
        </SheetHeader>
        
        <div className="mt-6 space-y-6">

          {/* Model Selection */}
          <div className="space-y-3">
            <Label>Select Model</Label>
            <div className="grid grid-cols-2 gap-3">
              {OPENROUTER_MODELS.map((m) => (
                <Card
                  key={m.value}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    model === m.value && "ring-2 ring-primary"
                  )}
                  onClick={() => setModel(m.value)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-lg">{m.icon}</span>
                          <h4 className="font-medium text-sm">{m.label}</h4>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {m.description}
                        </p>
                      </div>
                      {model === m.value && (
                        <Check className="h-4 w-4 text-primary ml-2 flex-shrink-0" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Temperature Control */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="temperature">Creativity</Label>
              <span className="text-sm text-muted-foreground">{temperature.toFixed(1)}</span>
            </div>
            <Slider
              id="temperature"
              min={0}
              max={1}
              step={0.1}
              value={[temperature]}
              onValueChange={([value]) => setTemperature(value)}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Focused</span>
              <span>Creative</span>
            </div>
          </div>

        </div>

        <SheetFooter className="mt-6">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Configuration
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}