'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON>ert<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface GenerationBlockDisplayProps {
  blockId: string;
  provider: string;
  model: string;
  temperature: number;
  prompt?: string;
  output?: string;
  isGenerating?: boolean;
  error?: string;
  onRegenerate?: () => void;
}

export function GenerationBlockDisplay({
  blockId,
  provider,
  model,
  temperature,
  prompt,
  output,
  isGenerating = false,
  error,
  onRegenerate,
}: GenerationBlockDisplayProps) {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = async () => {
    if (!output) return;
    
    try {
      await navigator.clipboard.writeText(output);
      setIsCopied(true);
      toast.success('Copied to clipboard');
      setTimeout(() => setIsCopied(false), 2000);
    } catch {
      toast.error('Failed to copy');
    }
  };

  const formatModel = (modelName: string) => {
    // Extract the model name after the provider prefix
    const parts = modelName.split('/');
    return parts[parts.length - 1];
  };

  return (
    <Card className="generation-block-display p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-primary" />
          <h3 className="font-semibold">Generation</h3>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            {formatModel(model)}
          </Badge>
          <Badge variant="outline" className="text-xs">
            Temp: {temperature}
          </Badge>
        </div>
      </div>


      {/* Loading State */}
      {isGenerating && (
        <div className="flex items-center gap-3 p-4 bg-primary/5 rounded-md">
          <Loader2 className="w-4 h-4 animate-spin text-primary" />
          <span className="text-sm">Generating response...</span>
        </div>
      )}

      {/* Error State */}
      {error && !isGenerating && (
        <div className="flex items-start gap-3 p-4 bg-destructive/10 rounded-md">
          <AlertCircle className="w-4 h-4 text-destructive mt-0.5" />
          <div className="flex-1">
            <p className="text-sm font-medium text-destructive">Generation Failed</p>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
          </div>
        </div>
      )}

      {/* Output Display */}
      {output && !isGenerating && !error && (
        <div className="relative">
          <div className="p-4 bg-muted/30 rounded-md">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium">Response:</p>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleCopy}
                className="h-7 px-2"
              >
                {isCopied ? (
                  <>
                    <Check className="w-3 h-3 mr-1" />
                    Copied
                  </>
                ) : (
                  <>
                    <Copy className="w-3 h-3 mr-1" />
                    Copy
                  </>
                )}
              </Button>
            </div>
            <div className="text-sm whitespace-pre-wrap">{output}</div>
          </div>
        </div>
      )}

      {/* Regenerate Button */}
      {(output || error) && !isGenerating && onRegenerate && (
        <div className="mt-3 flex justify-end">
          <Button
            size="sm"
            variant="outline"
            onClick={onRegenerate}
          >
            <Sparkles className="w-3 h-3 mr-1" />
            Regenerate
          </Button>
        </div>
      )}
    </Card>
  );
}