import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { GenerationBlockConfig } from './generation-block-config';

describe('GenerationBlockConfig', () => {
  const mockOnSave = jest.fn();
  const mockOnOpenChange = jest.fn();

  const defaultProps = {
    open: true,
    onOpenChange: mockOnOpenChange,
    initialConfig: {
      provider: 'openrouter',
      model: 'openai/gpt-3.5-turbo',
      temperature: 0.7,
    },
    onSave: mockOnSave,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders configuration modal with initial values', () => {
    render(<GenerationBlockConfig {...defaultProps} />);

    expect(screen.getByText('Configure Generation Block')).toBeInTheDocument();
    expect(screen.getByText('Select an AI model and configure generation parameters for this block.')).toBeInTheDocument();
    // Check that temperature value is displayed (there are multiple instances)
    expect(screen.getAllByText('0.7')).toHaveLength(2); // One in slider label, one in preview
  });

  it('calls onSave with configuration when Save is clicked', () => {
    render(<GenerationBlockConfig {...defaultProps} />);

    const saveButton = screen.getByText('Save Configuration');
    fireEvent.click(saveButton);

    expect(mockOnSave).toHaveBeenCalledWith({
      provider: 'openrouter',
      model: 'openai/gpt-3.5-turbo',
      temperature: 0.7,
      prompt: '',
    });
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('resets values and closes modal when Cancel is clicked', () => {
    render(<GenerationBlockConfig {...defaultProps} />);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('does not render when open is false', () => {
    render(<GenerationBlockConfig {...defaultProps} open={false} />);

    expect(screen.queryByText('Configure Generation Block')).not.toBeInTheDocument();
  });
});