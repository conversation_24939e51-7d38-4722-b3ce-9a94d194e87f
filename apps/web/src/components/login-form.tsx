"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { signIn, emailOtp } from "@/lib/auth-client"
import { useAuthStore } from "@/stores/auth.store"

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const router = useRouter()
  const { setUser } = useAuthStore()
  const [email, setEmail] = useState("")
  const [otp, setOtp] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [otpSent, setOtpSent] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    try {
      await emailOtp.sendVerificationOtp({
        email,
        type: "sign-in"
      })
      setOtpSent(true)
      setSuccessMessage("Code sent to")
    } catch (err: any) {
      const errorMessage = err?.message || err?.data?.message || "Failed to send code"
      setError(errorMessage)
      console.error("Send OTP error:", err)
      
      // Show helpful message for Resend test mode
      if (errorMessage.includes("<EMAIL>")) {
        setError("Test mode: You can only send <NAME_EMAIL>")
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    try {
      console.log('Attempting sign in with:', { email, otp })
      const result = await signIn.emailOtp({
        email,
        otp
      })
      
      console.log('Sign in result:', result)
      
      // Check cookies after sign in
      console.log('Cookies after sign in:', document.cookie)
      
      // Check if we have an error
      if (result?.error) {
        console.error('Sign in error:', result.error)
        setError(result.error.message || "Sign in failed")
        return
      }
      
      // Check if user needs onboarding
      if (result?.data?.user) {
        const user = result.data.user as any
        
        // Save user to store
        setUser({
          id: user.id,
          email: user.email,
          name: user.name || null,
          phone: user.phone || null,
          country: user.country || null,
          image: user.image || null
        })
        
        // Simple check: if user hasn't completed onboarding AND doesn't have all required fields
        const needsOnboarding = !user.onboardingCompleted && (!user.name || !user.phone || !user.country)
        
        console.log('User needs onboarding:', needsOnboarding)
        console.log('Redirecting to:', needsOnboarding ? '/onboarding' : '/dashboard')
        
        if (needsOnboarding) {
          router.push("/onboarding")
        } else {
          router.push("/dashboard")
        }
      } else {
        console.log('No user data in result, redirecting to dashboard')
        router.push("/dashboard")
      }
    } catch (err) {
      setError("Invalid code")
      console.error("Verify OTP error:", err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn("flex flex-col gap-8", className)} {...props}>
      <Card className="overflow-hidden shadow-xl border-0 bg-gray-800 border-gray-700">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form className="p-8 md:p-12" onSubmit={otpSent ? handleVerifyOtp : handleSendOtp}>
            <div className="flex flex-col gap-8">
              <div className="flex flex-col items-center text-center space-y-2">
                <h1 className="text-3xl font-bold tracking-tight text-white">Welcome</h1>
                <p className="text-gray-400">
                  {otpSent ? "Enter your code" : "Sign in with email"}
                </p>
              </div>
              {error && (
                <div className="text-sm text-red-400 text-center bg-red-950/50 p-3 rounded-md border border-red-800">
                  {error}
                </div>
              )}
              {!otpSent ? (
                <div className="grid gap-3">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-300">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                    className="h-12 text-base bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:border-blue-500"
                  />
                </div>
              ) : (
                <>
                  {successMessage && (
                    <div className="text-center p-4 bg-green-950/50 rounded-lg border border-green-800">
                      <p className="text-sm text-green-400">{successMessage}</p>
                      <p className="font-semibold text-green-300 mt-1">{email}</p>
                    </div>
                  )}
                  <div className="grid gap-3">
                    <Label htmlFor="otp" className="text-sm font-medium text-gray-300">Code</Label>
                    <Input
                      id="otp"
                      type="text"
                      required
                      value={otp}
                      onChange={(e) => setOtp(e.target.value)}
                      disabled={isLoading}
                      maxLength={6}
                      className="text-center text-2xl tracking-widest h-16 font-mono bg-gray-700 border-gray-600 text-white focus:border-blue-500"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setOtpSent(false)
                      setOtp("")
                      setError("")
                      setSuccessMessage("")
                    }}
                    className="text-sm text-blue-400 hover:text-blue-300 hover:underline transition-colors"
                  >
                    ← Different email
                  </button>
                </>
              )}
              <Button 
                type="submit" 
                className="w-full h-12 text-base font-medium bg-blue-600 hover:bg-blue-700 text-white border-0" 
                disabled={isLoading}
              >
                {isLoading ? "Loading..." : otpSent ? "Verify" : "Continue"}
              </Button>
              {!otpSent && (
                <>
                  <div className="text-center text-sm text-gray-400">
                    New accounts created automatically
                  </div>
                  <div className="text-center text-xs text-yellow-500 mt-2">
                    ⚠️ Test mode: Only <EMAIL> can receive emails
                  </div>
                </>
              )}
            </div>
          </form>
          <div className="relative hidden bg-gradient-to-br from-gray-700 to-gray-800 md:block">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20" />
            <div className="relative h-full flex items-center justify-center p-12">
              <div className="text-center space-y-6">
                <div className="w-24 h-24 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto">
                  <svg className="w-12 h-12 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">
                  SFlow
                </h2>
                <p className="text-gray-300 max-w-sm">
                  Powerful project management for modern teams
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="text-balance text-center text-xs text-gray-500 [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-blue-400">
        By continuing, you agree to our <a href="#">Terms</a> and <a href="#">Privacy Policy</a>.
      </div>
    </div>
  )
}
