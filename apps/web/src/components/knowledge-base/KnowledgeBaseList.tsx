'use client';

import React, { useEffect, useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { documentService } from '@/services/document.service';
import type { DocumentWithAssociation } from '@/types/document';
import { FileText, FileIcon, Save, AlertCircle, ChevronLeft, ChevronRight, Search, Eye } from 'lucide-react';

interface KnowledgeBaseListProps {
  projectId: string;
  isAdmin: boolean;
}

export function KnowledgeBaseList({ projectId, isAdmin }: KnowledgeBaseListProps) {
  const [documents, setDocuments] = useState<DocumentWithAssociation[]>([]);
  const [selectedDocIds, setSelectedDocIds] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [savingDocIds, setSavingDocIds] = useState<Set<string>>(new Set());
  const [hasChanges, setHasChanges] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [previewDoc, setPreviewDoc] = useState<{ fileName: string; fileType: string } | null>(null);
  const pageSize = 20;

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
      setCurrentPage(1); // Reset to first page on search
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  useEffect(() => {
    fetchDocuments();
  }, [projectId, currentPage, debouncedSearch]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const offset = (currentPage - 1) * pageSize;
      const response = await documentService.getAvailableDocuments(projectId, pageSize, offset, debouncedSearch);
      setDocuments(response.documents);
      setTotal(response.total);
      
      // Initialize selected documents on first page load and no search
      if (currentPage === 1 && !debouncedSearch) {
        // Fetch all associated documents to maintain selection state (max 100 per API limit)
        const allAssociated = await documentService.getProjectDocuments(projectId, 100, 0);
        const initialSelected = new Set(allAssociated.documents.map(d => d.id));
        setSelectedDocIds(initialSelected);
      }
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      toast.error('Failed to load documents. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleDocument = (docId: string) => {
    if (!isAdmin) return;

    const newSelected = new Set(selectedDocIds);
    if (newSelected.has(docId)) {
      newSelected.delete(docId);
    } else {
      newSelected.add(docId);
    }
    setSelectedDocIds(newSelected);
    setHasChanges(true);
    
    // Optimistically update the document's visual state
    setDocuments(docs => 
      docs.map(d => ({
        ...d,
        isAssociated: d.document.id === docId ? newSelected.has(docId) : d.isAssociated
      }))
    );
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const previousDocIds = new Set(documents.filter(d => d.isAssociated).map(d => d.document.id));
      
      // Track which documents are being updated
      const changedDocIds = new Set<string>();
      documents.forEach(doc => {
        const wasAssociated = doc.isAssociated;
        const willBeAssociated = selectedDocIds.has(doc.document.id);
        if (wasAssociated !== willBeAssociated) {
          changedDocIds.add(doc.document.id);
        }
      });
      setSavingDocIds(changedDocIds);
      
      // Optimistically update UI
      setDocuments(docs => 
        docs.map(d => ({
          ...d,
          isAssociated: selectedDocIds.has(d.document.id)
        }))
      );
      
      try {
        await documentService.updateProjectDocuments(projectId, Array.from(selectedDocIds));
        setHasChanges(false);
        toast.success('Document associations updated successfully.');
      } catch (error) {
        // Revert on error
        console.error('Failed to update documents:', error);
        setDocuments(docs => 
          docs.map(d => ({
            ...d,
            isAssociated: previousDocIds.has(d.document.id)
          }))
        );
        setSelectedDocIds(previousDocIds);
        toast.error('Failed to update document associations. Please try again.');
      }
    } finally {
      setSaving(false);
      setSavingDocIds(new Set());
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <FileText className="h-4 w-4" />;
    return <FileIcon className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number | null | undefined) => {
    if (!bytes) return 'Unknown size';
    const kb = bytes / 1024;
    const mb = kb / 1024;
    if (mb >= 1) return `${mb.toFixed(1)} MB`;
    return `${kb.toFixed(0)} KB`;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge variant="success">Completed</Badge>;
      case 'PROCESSING':
        return <Badge variant="secondary">Processing</Badge>;
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  const isPreviewable = (fileType: string) => {
    // Simple preview for text-based files
    return fileType.includes('text') || 
           fileType.includes('markdown') || 
           fileType.includes('json') ||
           fileType.includes('xml') ||
           fileType.includes('csv');
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
            <Skeleton className="h-5 w-5" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-48" />
              <Skeleton className="h-3 w-32" />
            </div>
            <Skeleton className="h-6 w-20" />
          </div>
        ))}
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          No documents available in the workspace. Upload documents to the central Knowledge Base to make them available for projects.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Actions Bar */}
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search documents by name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {selectedDocIds.size} of {total} total documents selected
            {debouncedSearch && ` (filtered by "${debouncedSearch}")`}
          </p>
          {isAdmin && hasChanges && (
            <Button
              onClick={handleSave}
              disabled={saving}
              size="sm"
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-2">
        {documents.map(({ document, isAssociated }) => {
          const isSaving = savingDocIds.has(document.id);
          
          return (
            <div
              key={document.id}
              data-testid="document-list-item"
              className={`flex items-center space-x-4 p-4 border rounded-lg transition-all ${
                isSaving ? 'opacity-60' : 'hover:bg-muted/50'
              }`}
            >
              {isSaving ? (
                <Skeleton className="h-5 w-5 rounded" />
              ) : (
                <Checkbox
                  checked={selectedDocIds.has(document.id)}
                  onCheckedChange={() => handleToggleDocument(document.id)}
                  disabled={!isAdmin || document.status !== 'COMPLETED'}
                />
              )}
              
              <div className="flex items-center gap-2 flex-1">
                {isSaving ? (
                  <>
                    <Skeleton className="h-4 w-4" />
                    <div className="flex-1">
                      <Skeleton className="h-4 w-48 mb-2" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                  </>
                ) : (
                  <>
                    {getFileIcon(document.fileType)}
                    <div className="flex-1">
                      <p className="font-medium text-sm">{document.fileName}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                        <span>{formatFileSize(document.fileSize)}</span>
                        <span>•</span>
                        <span>Uploaded {new Date(document.uploadedAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </>
                )}
              </div>

              <div className="flex items-center gap-2">
                {isSaving ? (
                  <Skeleton className="h-6 w-20" />
                ) : (
                  <>
                    {isPreviewable(document.fileType) && document.status === 'COMPLETED' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setPreviewDoc({ fileName: document.fileName, fileType: document.fileType })}
                        className="h-8 w-8 p-0"
                        title="Preview document"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                    {getStatusBadge(document.status)}
                  </>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Pagination Controls */}
      {total > pageSize && (
        <div className="flex items-center justify-center gap-4 mt-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
            disabled={currentPage === 1 || loading}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <span className="text-sm text-muted-foreground">
            Page {currentPage} of {Math.ceil(total / pageSize)}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(p => Math.min(Math.ceil(total / pageSize), p + 1))}
            disabled={currentPage >= Math.ceil(total / pageSize) || loading}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}

      {!isAdmin && (
        <Alert className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You need admin permissions to modify document associations.
          </AlertDescription>
        </Alert>
      )}

      {/* Document Preview Dialog */}
      <Dialog open={!!previewDoc} onOpenChange={(open) => !open && setPreviewDoc(null)}>
        <DialogContent className="max-w-3xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>{previewDoc?.fileName}</DialogTitle>
          </DialogHeader>
          <div className="overflow-auto p-4 bg-muted rounded-md">
            <p className="text-sm text-muted-foreground text-center py-8">
              Document preview would be displayed here.
              <br />
              <span className="text-xs">
                (Preview functionality requires integration with document storage service)
              </span>
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}