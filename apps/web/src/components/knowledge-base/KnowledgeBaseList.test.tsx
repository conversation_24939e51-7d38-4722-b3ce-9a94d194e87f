import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { KnowledgeBaseList } from './KnowledgeBaseList';
import { documentService } from '@/services/document.service';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/services/document.service');
jest.mock('sonner');

describe('KnowledgeBaseList', () => {
  const mockProjectId = 'test-project-123';
  const mockDocuments = [
    {
      document: {
        id: 'doc1',
        workspaceId: 'workspace1',
        fileName: 'test-document.pdf',
        fileType: 'application/pdf',
        fileSize: 1024000,
        status: 'COMPLETED' as const,
        uploadedAt: '2024-01-01T00:00:00Z',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      isAssociated: true
    },
    {
      document: {
        id: 'doc2',
        workspaceId: 'workspace1',
        fileName: 'another-doc.md',
        fileType: 'text/markdown',
        fileSize: 512000,
        status: 'COMPLETED' as const,
        uploadedAt: '2024-01-02T00:00:00Z',
        createdAt: '2024-01-02T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z'
      },
      isAssociated: false
    },
    {
      document: {
        id: 'doc3',
        workspaceId: 'workspace1',
        fileName: 'processing.txt',
        fileType: 'text/plain',
        fileSize: 256000,
        status: 'PROCESSING' as const,
        uploadedAt: '2024-01-03T00:00:00Z',
        createdAt: '2024-01-03T00:00:00Z',
        updatedAt: '2024-01-03T00:00:00Z'
      },
      isAssociated: false
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (documentService.getAvailableDocuments as jest.Mock).mockResolvedValue({
      documents: mockDocuments,
      total: mockDocuments.length,
      limit: 20,
      offset: 0
    });
    (documentService.getProjectDocuments as jest.Mock).mockResolvedValue({
      documents: mockDocuments.filter(d => d.isAssociated).map(d => d.document),
      total: 1,
      limit: 1000,
      offset: 0
    });
    (documentService.updateProjectDocuments as jest.Mock).mockResolvedValue([]);
  });

  it('should load and display documents', async () => {
    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    // Should show loading state initially
    expect(screen.getByRole('status')).toBeInTheDocument();

    // Wait for documents to load
    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    });

    // Should display all documents
    expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    expect(screen.getByText('another-doc.md')).toBeInTheDocument();
    expect(screen.getByText('processing.txt')).toBeInTheDocument();

    // Should show document count
    expect(screen.getByText('1 of 3 total documents selected')).toBeInTheDocument();
  });

  it('should show correct file sizes', async () => {
    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getByText('1.0 MB')).toBeInTheDocument(); // 1024000 bytes
      expect(screen.getByText('500 KB')).toBeInTheDocument(); // 512000 bytes
      expect(screen.getByText('250 KB')).toBeInTheDocument(); // 256000 bytes
    });
  });

  it('should show correct status badges', async () => {
    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getAllByText('Completed')).toHaveLength(2);
      expect(screen.getByText('Processing')).toBeInTheDocument();
    });
  });

  it('should allow admin to toggle document selection', async () => {
    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getByText('another-doc.md')).toBeInTheDocument();
    });

    // Find checkbox for second document (not associated)
    const checkboxes = screen.getAllByRole('checkbox');
    const secondCheckbox = checkboxes[1];

    // Click to select
    fireEvent.click(secondCheckbox);

    // Should update count
    expect(screen.getByText('2 of 3 total documents selected')).toBeInTheDocument();

    // Should show save button
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
  });

  it('should disable checkbox for non-completed documents', async () => {
    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getByText('processing.txt')).toBeInTheDocument();
    });

    const checkboxes = screen.getAllByRole('checkbox');
    const processingCheckbox = checkboxes[2]; // Third document is processing

    expect(processingCheckbox).toBeDisabled();
  });

  it('should save document associations', async () => {
    (documentService.updateProjectDocuments as jest.Mock).mockResolvedValue([]);

    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getByText('another-doc.md')).toBeInTheDocument();
    });

    // Toggle second document
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[1]);

    // Click save
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    // Should show saving state
    expect(screen.getByText('Saving...')).toBeInTheDocument();

    await waitFor(() => {
      expect(documentService.updateProjectDocuments).toHaveBeenCalledWith(
        mockProjectId,
        ['doc1', 'doc2'] // Both documents should be selected now
      );
      expect(toast.success).toHaveBeenCalledWith('Document associations updated successfully.');
    });

    // Save button should disappear
    expect(screen.queryByText('Save Changes')).not.toBeInTheDocument();
  });

  it('should handle save errors', async () => {
    (documentService.updateProjectDocuments as jest.Mock).mockRejectedValue(new Error('Network error'));

    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getByText('another-doc.md')).toBeInTheDocument();
    });

    // Toggle and save
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[1]);
    fireEvent.click(screen.getByText('Save Changes'));

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to update document associations. Please try again.');
    });
  });

  it('should show read-only view for non-admin users', async () => {
    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={false} />);

    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    });

    // All checkboxes should be disabled
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      expect(checkbox).toBeDisabled();
    });

    // Should show permission message
    expect(screen.getByText('You need admin permissions to modify document associations.')).toBeInTheDocument();

    // Should not show save button even if there are changes
    expect(screen.queryByText('Save Changes')).not.toBeInTheDocument();
  });

  it('should show empty state when no documents available', async () => {
    (documentService.getAvailableDocuments as jest.Mock).mockResolvedValue({
      documents: [],
      total: 0,
      limit: 20,
      offset: 0
    });

    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getByText('No documents available in the workspace. Upload documents to the central Knowledge Base to make them available for projects.')).toBeInTheDocument();
    });
  });

  it('should handle load errors', async () => {
    (documentService.getAvailableDocuments as jest.Mock).mockRejectedValue(new Error('Failed to fetch'));

    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to load documents. Please try again.');
    });
  });

  it('should support pagination', async () => {
    // Mock large dataset
    const largeDataset = Array.from({ length: 25 }, (_, i) => ({
      document: {
        id: `doc${i}`,
        workspaceId: 'workspace1',
        fileName: `document-${i}.pdf`,
        fileType: 'application/pdf',
        fileSize: 1024000,
        status: 'COMPLETED' as const,
        uploadedAt: '2024-01-01T00:00:00Z',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      isAssociated: false
    }));

    (documentService.getAvailableDocuments as jest.Mock).mockResolvedValue({
      documents: largeDataset.slice(0, 20),
      total: 25,
      limit: 20,
      offset: 0
    });

    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getByText('document-0.pdf')).toBeInTheDocument();
    });

    // Should show pagination controls
    expect(screen.getByText('Page 1 of 2')).toBeInTheDocument();
    expect(screen.getByText('Previous')).toBeDisabled();
    expect(screen.getByText('Next')).toBeEnabled();

    // Mock page 2 response
    (documentService.getAvailableDocuments as jest.Mock).mockResolvedValue({
      documents: largeDataset.slice(20, 25),
      total: 25,
      limit: 20,
      offset: 20
    });

    // Click next page
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('document-20.pdf')).toBeInTheDocument();
    });

    expect(screen.getByText('Page 2 of 2')).toBeInTheDocument();
    expect(screen.getByText('Previous')).toBeEnabled();
    expect(screen.getByText('Next')).toBeDisabled();
  });

  it('should support search functionality', async () => {
    const user = userEvent.setup();
    
    render(<KnowledgeBaseList projectId={mockProjectId} isAdmin={true} />);

    await waitFor(() => {
      expect(screen.getByText('test-document.pdf')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search documents by name...');
    expect(searchInput).toBeInTheDocument();

    // Mock filtered results
    const filteredDocs = [{
      document: {
        id: 'doc1',
        workspaceId: 'workspace1',
        fileName: 'test-document.pdf',
        fileType: 'application/pdf',
        fileSize: 1024000,
        status: 'COMPLETED' as const,
        uploadedAt: '2024-01-01T00:00:00Z',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      isAssociated: true
    }];

    (documentService.getAvailableDocuments as jest.Mock).mockResolvedValue({
      documents: filteredDocs,
      total: 1,
      limit: 20,
      offset: 0
    });

    // Type in search
    await user.type(searchInput, 'test');

    // Wait for debounce and verify search was called
    await waitFor(() => {
      expect(documentService.getAvailableDocuments).toHaveBeenLastCalledWith(
        mockProjectId,
        20,
        0,
        'test'
      );
    });

    // Verify filtered message appears
    expect(screen.getByText(/filtered by "test"/)).toBeInTheDocument();
  });
});