import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { DocumentListItem } from './DocumentListItem'
import type { KnowledgeBaseDocument } from '@/types/document'

// Mock date-fns
jest.mock('date-fns', () => ({
  formatDistanceToNow: () => '2 hours ago'
}))

describe('DocumentListItem', () => {
  const mockDocument: KnowledgeBaseDocument = {
    id: 'doc-123',
    workspaceId: 'workspace-123',
    fileName: 'test-document.pdf',
    fileType: 'application/pdf',
    fileSize: 1024 * 1024, // 1MB
    status: 'COMPLETED',
    uploadedAt: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  const mockOnDelete = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders document information correctly', () => {
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)

    expect(screen.getByText('test-document.pdf')).toBeInTheDocument()
    expect(screen.getByText('1 MB')).toBeInTheDocument()
    expect(screen.getByText('2 hours ago')).toBeInTheDocument()
  })

  it('displays correct file icon for PDF files', () => {
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    const fileIcon = screen.getByTestId('file-icon').querySelector('svg')
    expect(fileIcon).toHaveClass('text-red-500')
  })

  it('displays correct file icon for DOCX files', () => {
    const docxDocument = { ...mockDocument, fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }
    render(<DocumentListItem document={docxDocument} onDelete={mockOnDelete} />)
    
    const fileIcon = screen.getByTestId('file-icon').querySelector('svg')
    expect(fileIcon).toHaveClass('text-blue-500')
  })

  it('displays correct file icon for TXT files', () => {
    const txtDocument = { ...mockDocument, fileType: 'text/plain' }
    render(<DocumentListItem document={txtDocument} onDelete={mockOnDelete} />)
    
    const fileIcon = screen.getByTestId('file-icon').querySelector('svg')
    expect(fileIcon).toHaveClass('text-gray-500')
  })

  it('displays correct status badge for PENDING status', () => {
    const pendingDocument = { ...mockDocument, status: 'PENDING' as const }
    render(<DocumentListItem document={pendingDocument} onDelete={mockOnDelete} />)
    
    expect(screen.getByText('Pending')).toBeInTheDocument()
  })

  it('displays correct status badge for PROCESSING status', () => {
    const processingDocument = { ...mockDocument, status: 'PROCESSING' as const }
    render(<DocumentListItem document={processingDocument} onDelete={mockOnDelete} />)
    
    expect(screen.getByText('Processing')).toBeInTheDocument()
  })

  it('displays correct status badge for COMPLETED status', () => {
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    expect(screen.getByText('Completed')).toBeInTheDocument()
  })

  it('displays correct status badge for FAILED status', () => {
    const failedDocument = { ...mockDocument, status: 'FAILED' as const }
    render(<DocumentListItem document={failedDocument} onDelete={mockOnDelete} />)
    
    expect(screen.getByText('Failed')).toBeInTheDocument()
  })

  it('opens delete confirmation dialog when delete button is clicked', () => {
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    const deleteButton = screen.getByRole('button', { name: /delete document/i })
    fireEvent.click(deleteButton)
    
    expect(screen.getByText('Delete Document')).toBeInTheDocument()
    expect(screen.getByText((content, element) => {
      return content.includes('Are you sure you want to delete') && 
             content.includes('test-document.pdf') &&
             content.includes('This action cannot be undone')
    })).toBeInTheDocument()
  })

  it('closes delete confirmation dialog when cancel is clicked', () => {
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    const deleteButton = screen.getByRole('button', { name: /delete document/i })
    fireEvent.click(deleteButton)
    
    const cancelButton = screen.getByText('Cancel')
    fireEvent.click(cancelButton)
    
    expect(screen.queryByText('Delete Document')).not.toBeInTheDocument()
  })

  it('calls onDelete when confirm delete is clicked', async () => {
    mockOnDelete.mockResolvedValue(undefined)
    
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    const deleteButton = screen.getByRole('button', { name: /delete document/i })
    fireEvent.click(deleteButton)
    
    const confirmButton = screen.getByRole('button', { name: 'Delete' })
    fireEvent.click(confirmButton)
    
    await waitFor(() => {
      expect(mockOnDelete).toHaveBeenCalledWith('doc-123')
    })
  })

  it('disables delete button while deleting', async () => {
    // Create a deferred promise so we can control when it resolves
    let resolveDelete: () => void
    const deletePromise = new Promise<void>(resolve => {
      resolveDelete = resolve
    })
    mockOnDelete.mockReturnValue(deletePromise)
    
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    const deleteButton = screen.getByRole('button', { name: /delete document/i })
    fireEvent.click(deleteButton)
    
    // Wait for dialog to be visible
    await waitFor(() => {
      expect(screen.getByText('Delete Document')).toBeInTheDocument()
    })
    
    const confirmButton = screen.getByRole('button', { name: 'Delete' })
    fireEvent.click(confirmButton)
    
    // Verify that onDelete was called
    expect(mockOnDelete).toHaveBeenCalledWith('doc-123')
    
    // The dialog should close after successful deletion
    // Resolve the promise to complete the deletion
    resolveDelete!()
    
    // Wait for the dialog to close
    await waitFor(() => {
      expect(screen.queryByText('Delete Document')).not.toBeInTheDocument()
    })
  })

  it('handles delete error gracefully', async () => {
    const consoleError = jest.spyOn(console, 'error').mockImplementation()
    mockOnDelete.mockRejectedValue(new Error('Delete failed'))
    
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    const deleteButton = screen.getByRole('button', { name: /delete document/i })
    fireEvent.click(deleteButton)
    
    const confirmButton = screen.getByRole('button', { name: 'Delete' })
    fireEvent.click(confirmButton)
    
    await waitFor(() => {
      expect(mockOnDelete).toHaveBeenCalled()
      expect(consoleError).toHaveBeenCalledWith('Failed to delete document:', expect.any(Error))
    })
    
    consoleError.mockRestore()
  })

  it('formats file size correctly', () => {
    const testCases = [
      { size: 0, expected: '0 Bytes' },
      { size: 512, expected: '512 Bytes' },
      { size: 1024, expected: '1 KB' },
      { size: 1024 * 1024, expected: '1 MB' },
      { size: 1.5 * 1024 * 1024, expected: '1.5 MB' },
      { size: 1024 * 1024 * 1024, expected: '1 GB' }
    ]

    testCases.forEach(({ size, expected }) => {
      const { rerender } = render(
        <DocumentListItem 
          document={{ ...mockDocument, fileSize: size }} 
          onDelete={mockOnDelete} 
        />
      )
      expect(screen.getByText(expected)).toBeInTheDocument()
      rerender(<></>)
    })
  })

  it('includes proper aria-labels for accessibility', () => {
    render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    // Check status badge aria-label
    const statusBadge = screen.getByLabelText('Document status: Processing completed successfully')
    expect(statusBadge).toBeInTheDocument()
    
    // Check file icon aria-label
    const fileIcon = screen.getByLabelText('File type: application/pdf')
    expect(fileIcon).toBeInTheDocument()
  })

  it('has correct aria-labels for different status badges', () => {
    const { rerender } = render(<DocumentListItem document={mockDocument} onDelete={mockOnDelete} />)
    
    // PENDING status
    rerender(<DocumentListItem document={{ ...mockDocument, status: 'PENDING' as const }} onDelete={mockOnDelete} />)
    expect(screen.getByLabelText('Document status: Pending processing')).toBeInTheDocument()
    
    // PROCESSING status
    rerender(<DocumentListItem document={{ ...mockDocument, status: 'PROCESSING' as const }} onDelete={mockOnDelete} />)
    expect(screen.getByLabelText('Document status: Currently processing')).toBeInTheDocument()
    
    // FAILED status
    rerender(<DocumentListItem document={{ ...mockDocument, status: 'FAILED' as const }} onDelete={mockOnDelete} />)
    expect(screen.getByLabelText('Document status: Processing failed')).toBeInTheDocument()
  })

  it('shows checkbox when in selection mode', () => {
    render(
      <DocumentListItem 
        document={mockDocument} 
        onDelete={mockOnDelete}
        selectionMode={true}
        isSelected={false}
        onSelectionChange={jest.fn()}
      />
    )
    
    expect(screen.getByLabelText('Select test-document.pdf')).toBeInTheDocument()
  })

  it('handles selection change', () => {
    const mockOnSelectionChange = jest.fn()
    
    render(
      <DocumentListItem 
        document={mockDocument} 
        onDelete={mockOnDelete}
        selectionMode={true}
        isSelected={false}
        onSelectionChange={mockOnSelectionChange}
      />
    )
    
    const checkbox = screen.getByLabelText('Select test-document.pdf')
    fireEvent.click(checkbox)
    
    expect(mockOnSelectionChange).toHaveBeenCalledWith('doc-123', true)
  })

  it('reflects selection state', () => {
    const { rerender } = render(
      <DocumentListItem 
        document={mockDocument} 
        onDelete={mockOnDelete}
        selectionMode={true}
        isSelected={false}
        onSelectionChange={jest.fn()}
      />
    )
    
    const checkbox = screen.getByLabelText('Select test-document.pdf')
    expect(checkbox).not.toBeChecked()
    
    rerender(
      <DocumentListItem 
        document={mockDocument} 
        onDelete={mockOnDelete}
        selectionMode={true}
        isSelected={true}
        onSelectionChange={jest.fn()}
      />
    )
    
    expect(checkbox).toBeChecked()
  })

  it('does not show checkbox when not in selection mode', () => {
    render(
      <DocumentListItem 
        document={mockDocument} 
        onDelete={mockOnDelete}
        selectionMode={false}
      />
    )
    
    expect(screen.queryByLabelText('Select test-document.pdf')).not.toBeInTheDocument()
  })
})