"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import { 
  Upload, 
  File, 
  X, 
  CheckCircle2, 
  AlertCircle,
  FileText,
  FileImage
} from "lucide-react"
import { documentService } from "@/services/document.service"
import { toast } from "sonner"

interface FileWithPreview {
  file: File
  preview?: string
  id: string
  progress?: number
  status?: "pending" | "uploading" | "success" | "error"
  error?: string
  // Convenience properties from File
  name: string
  size: number
  type: string
}

const ACCEPTED_FILE_TYPES = {
  "application/pdf": [".pdf"],
  "text/plain": [".txt"],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"]
}

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

interface FileUploadProps {
  onUploadSuccess?: () => void
}

export function FileUpload({ onUploadSuccess }: FileUploadProps = {}) {
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    if (rejectedFiles.length > 0) {
      toast.error(`Some files were rejected. Please ensure files are PDF, TXT, or DOCX and under 10MB.`)
    }

    console.log('Accepted files:', acceptedFiles)
    
    const newFiles = acceptedFiles.map(file => {
      console.log('Processing file:', { name: file.name, size: file.size, type: file.type })
      return {
        file: file,
        name: file.name,
        size: file.size,
        type: file.type,
        id: Math.random().toString(36).substring(7),
        status: "pending" as const,
        progress: 0
      } as FileWithPreview
    })

    setFiles(prev => [...prev, ...newFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxSize: MAX_FILE_SIZE,
    multiple: true
  })

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const getFileIcon = (file: FileWithPreview) => {
    const fileType = file.type || ""
    if (fileType === "application/pdf") {
      return <FileText className="h-5 w-5 text-red-500" />
    } else if (fileType.startsWith("text/")) {
      return <FileText className="h-5 w-5 text-blue-500" />
    } else if (fileType.includes("wordprocessingml")) {
      return <FileText className="h-5 w-5 text-purple-500" />
    } else {
      return <FileImage className="h-5 w-5 text-green-500" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (!bytes || bytes === 0 || isNaN(bytes)) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const uploadFiles = async () => {
    setIsUploading(true)
    
    const pendingFiles = files.filter(f => f.status === "pending")
    if (pendingFiles.length === 0) {
      setIsUploading(false)
      return
    }

    // Update file statuses to uploading
    setFiles(prev => prev.map(file => 
      file.status === "pending" 
        ? { ...file, status: "uploading" as const, progress: 0 }
        : file
    ))

    try {
      console.log('Starting upload for files:', pendingFiles.map(f => ({ name: f.name, size: f.size })))
      
      // Extract the actual File objects for upload
      const filesToUpload = pendingFiles.map(f => f.file)
      
      const uploadedDocs = await documentService.uploadDocuments(
        filesToUpload,
        {
          onProgress: (fileName, progress) => {
            console.log('Upload progress:', { fileName, progress })
            setFiles(prev => prev.map(file => 
              file.name === fileName && file.status === "uploading"
                ? { ...file, progress }
                : file
            ))
          }
        }
      )

      console.log('Upload completed. Received documents:', uploadedDocs)
      console.log('Pending files were:', pendingFiles.map(f => f.name))
      if (uploadedDocs.length > 0) {
        console.log('First uploaded doc structure:', uploadedDocs[0])
      }

      // Mark files as successful
      setFiles(prev => prev.map(file => {
        const uploaded = uploadedDocs.find(doc => doc.fileName === file.name)
        console.log(`Matching file "${file.name}" with uploaded docs:`, uploaded)
        if (uploaded) {
          return { ...file, status: "success" as const, progress: 100 }
        }
        if (file.status === "uploading") {
          return { ...file, status: "error" as const, error: "Upload failed" }
        }
        return file
      }))

      toast.success(`Successfully uploaded ${uploadedDocs.length} file${uploadedDocs.length > 1 ? 's' : ''}`)
      
      // Call the success callback if provided
      if (onUploadSuccess) {
        onUploadSuccess()
      }
    } catch (error: any) {
      console.error('Upload error in FileUpload component:', error)
      console.error('Error details:', {
        message: error?.message,
        stack: error?.stack,
        response: error?.response,
        name: error?.name
      })
      
      // Extract meaningful error message
      const errorMessage = error?.response?.data?.error || error?.message || "Upload failed"
      
      // Mark uploading files as failed
      setFiles(prev => prev.map(file => 
        file.status === "uploading"
          ? { ...file, status: "error" as const, error: errorMessage }
          : file
      ))
      
      toast.error(errorMessage)
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
          isDragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-primary/50"
        )}
      >
        <input {...getInputProps()} />
        <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        {isDragActive ? (
          <p className="text-lg">Drop the files here...</p>
        ) : (
          <>
            <p className="text-lg mb-2">Drag & drop files here, or click to select</p>
            <p className="text-sm text-muted-foreground">
              Supports PDF, TXT, and DOCX files (max 10MB each)
            </p>
          </>
        )}
      </div>

      {files.length > 0 && (
        <Card className="p-4">
          <h3 className="font-medium mb-3">Selected Files ({files.length})</h3>
          <div className="space-y-2">
            {files.map((file) => (
              <div key={file.id} className="flex items-center gap-3 p-2 rounded-lg bg-muted/50">
                {getFileIcon(file)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{file.name || "Unknown file"}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(file.size)}
                  </p>
                  {file.status === "uploading" && file.progress !== undefined && (
                    <Progress value={file.progress} className="h-1 mt-1" />
                  )}
                </div>
                {file.status === "pending" && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeFile(file.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
                {file.status === "success" && (
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                )}
                {file.status === "error" && (
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-destructive" />
                    {file.error && (
                      <span className="text-xs text-destructive">{file.error}</span>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
          <Button 
            className="w-full mt-4" 
            onClick={uploadFiles}
            disabled={isUploading || files.every(f => f.status !== "pending")}
          >
            {isUploading ? "Uploading..." : "Upload Files"}
          </Button>
        </Card>
      )}
    </div>
  )
}