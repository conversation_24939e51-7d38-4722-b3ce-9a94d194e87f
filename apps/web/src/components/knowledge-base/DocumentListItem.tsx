import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { FileText, FileIcon, Trash2, Clock, CheckCircle2, XCircle, Loader2, Play } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import type { KnowledgeBaseDocument } from '@/types/document'
import { formatFileSize } from '@/lib/format'

interface DocumentListItemProps {
  document: KnowledgeBaseDocument
  onDelete: (documentId: string) => Promise<void>
  onProcess?: (documentId: string) => Promise<void>
  isSelected?: boolean
  onSelectionChange?: (documentId: string, isSelected: boolean) => void
  selectionMode?: boolean
}

export function DocumentListItem({ 
  document, 
  onDelete,
  onProcess,
  isSelected = false,
  onSelectionChange,
  selectionMode = false
}: DocumentListItemProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)


  const getFileIcon = () => {
    const fileTypeConfig = {
      'application/pdf': { icon: FileText, className: 'h-5 w-5 text-red-500' },
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { icon: FileText, className: 'h-5 w-5 text-blue-500' },
      'text/plain': { icon: FileIcon, className: 'h-5 w-5 text-gray-500' }
    }
    
    const config = fileTypeConfig[document.fileType as keyof typeof fileTypeConfig]
    const Icon = config?.icon || FileIcon
    const className = config?.className || 'h-5 w-5'
    
    return <Icon className={className} />
  }

  const getStatusBadge = () => {
    switch (document.status) {
      case 'PENDING':
        return (
          <Badge variant="secondary" className="gap-1" aria-label="Document status: Pending processing">
            <Clock className="h-3 w-3" aria-hidden="true" />
            Pending
          </Badge>
        )
      case 'PROCESSING':
        return (
          <Badge variant="secondary" className="gap-1" aria-label="Document status: Currently processing">
            <Loader2 className="h-3 w-3 animate-spin" aria-hidden="true" />
            Processing
          </Badge>
        )
      case 'COMPLETED':
        return (
          <Badge variant="default" className="gap-1" aria-label="Document status: Processing completed successfully">
            <CheckCircle2 className="h-3 w-3" aria-hidden="true" />
            Completed
          </Badge>
        )
      case 'FAILED':
        return (
          <Badge variant="destructive" className="gap-1" aria-label="Document status: Processing failed">
            <XCircle className="h-3 w-3" aria-hidden="true" />
            Failed
          </Badge>
        )
      default:
        return null
    }
  }

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await onDelete(document.id)
      setDeleteDialogOpen(false)
    } catch (error) {
      console.error('Failed to delete document:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleProcess = async () => {
    if (!onProcess) return
    setIsProcessing(true)
    try {
      await onProcess(document.id)
    } catch (error) {
      console.error('Failed to process document:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex gap-4">
              {selectionMode && (
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={(checked) => 
                    onSelectionChange?.(document.id, checked as boolean)
                  }
                  aria-label={`Select ${document.fileName}`}
                />
              )}
              <div className="mt-1" data-testid="file-icon" aria-label={`File type: ${document.fileType || 'Unknown'}`}>{getFileIcon()}</div>
              <div className="space-y-1">
                <h3 className="font-medium leading-none">{document.fileName}</h3>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>{formatFileSize(document.fileSize || 0)}</span>
                  <span>•</span>
                  <span>{formatDistanceToNow(new Date(document.uploadedAt), { addSuffix: true })}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge()}
              {document.status === 'PENDING' && onProcess && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleProcess}
                  disabled={isProcessing}
                  title="Process document"
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                  <span className="sr-only">Process document</span>
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setDeleteDialogOpen(true)}
                disabled={isDeleting}
              >
                <Trash2 className="h-4 w-4" />
                <span className="sr-only">Delete document</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Document</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &ldquo;{document.fileName}&rdquo;? This action cannot be undone.
              All associated data and chunks will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}