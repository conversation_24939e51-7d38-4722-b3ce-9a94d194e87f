import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { FileUpload } from './FileUpload'
import { documentService } from '@/services/document.service'
import { toast } from 'sonner'

// Mock dependencies
jest.mock('@/services/document.service')
jest.mock('sonner')

// Mock react-dropzone
jest.mock('react-dropzone', () => ({
  useDropzone: jest.fn((options) => ({
    getRootProps: () => ({
      onClick: jest.fn(),
      onDrop: options.onDrop,
      onDragEnter: jest.fn(),
      onDragLeave: jest.fn(),
    }),
    getInputProps: () => ({
      type: 'file',
      accept: options.accept,
      multiple: options.multiple,
    }),
    isDragActive: false,
  })),
}))

describe('FileUpload', () => {
  const mockUploadDocuments = documentService.uploadDocuments as jest.MockedFunction<typeof documentService.uploadDocuments>
  const mockToastSuccess = toast.success as jest.MockedFunction<typeof toast.success>
  const mockToastError = toast.error as jest.MockedFunction<typeof toast.error>

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders upload area with correct text', () => {
    render(<FileUpload />)
    
    expect(screen.getByText('Drag & drop files here, or click to select')).toBeInTheDocument()
    expect(screen.getByText('Supports PDF, TXT, and DOCX files (max 10MB each)')).toBeInTheDocument()
  })

  it('displays selected files with correct information', async () => {
    render(<FileUpload />)
    
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    const input = document.querySelector('input[type="file"]') as HTMLInputElement
    
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    })
    
    fireEvent.change(input)
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
      expect(screen.getByText(/12 Bytes/)).toBeInTheDocument()
    })
  })

  it('removes file when X button is clicked', async () => {
    render(<FileUpload />)
    
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' })
    const input = document.querySelector('input[type="file"]') as HTMLInputElement
    
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    })
    
    fireEvent.change(input)
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const removeButtons = screen.getAllByRole('button')
    const removeButton = removeButtons.find(btn => btn.querySelector('svg'))
    fireEvent.click(removeButton!)
    
    expect(screen.queryByText('test.pdf')).not.toBeInTheDocument()
  })

  it('validates file types and shows alert for invalid files', async () => {
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {})
    render(<FileUpload />)
    
    const invalidFile = new File(['test'], 'test.exe', { type: 'application/x-msdownload' })
    const input = document.querySelector('input[type="file"]') as HTMLInputElement
    
    Object.defineProperty(input, 'files', {
      value: [invalidFile],
      writable: false,
    })
    
    fireEvent.change(input)
    
    await waitFor(() => {
      expect(alertSpy).toHaveBeenCalledWith(
        expect.stringContaining('Some files were rejected')
      )
    })
    
    alertSpy.mockRestore()
  })

  it('uploads files successfully and shows success toast', async () => {
    const mockDocuments = [
      { id: '1', fileName: 'test.pdf', status: 'pending' as const }
    ]
    mockUploadDocuments.mockResolvedValue(mockDocuments as any)
    
    render(<FileUpload />)
    
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' })
    const input = document.querySelector('input[type="file"]') as HTMLInputElement
    
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    })
    
    fireEvent.change(input)
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const uploadButton = screen.getByRole('button', { name: /upload files/i })
    fireEvent.click(uploadButton)
    
    await waitFor(() => {
      expect(mockUploadDocuments).toHaveBeenCalledWith(
        [expect.any(File)],
        expect.objectContaining({ onProgress: expect.any(Function) })
      )
      expect(mockToastSuccess).toHaveBeenCalledWith('Successfully uploaded 1 file')
    })
  })

  it('handles upload errors and shows error toast', async () => {
    mockUploadDocuments.mockRejectedValue(new Error('Upload failed'))
    
    render(<FileUpload />)
    
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' })
    const input = document.querySelector('input[type="file"]') as HTMLInputElement
    
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    })
    
    fireEvent.change(input)
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const uploadButton = screen.getByRole('button', { name: /upload files/i })
    fireEvent.click(uploadButton)
    
    await waitFor(() => {
      expect(mockToastError).toHaveBeenCalledWith('Failed to upload files. Please try again.')
    })
  })

  it('displays progress bar during upload', async () => {
    let progressCallback: ((fileName: string, progress: number) => void) | undefined
    
    mockUploadDocuments.mockImplementation(async (files, options) => {
      progressCallback = options?.onProgress
      // Simulate progress updates
      setTimeout(() => progressCallback?.('test.pdf', 50), 100)
      return []
    })
    
    render(<FileUpload />)
    
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' })
    const input = document.querySelector('input[type="file"]') as HTMLInputElement
    
    Object.defineProperty(input, 'files', {
      value: [file],
      writable: false,
    })
    
    fireEvent.change(input)
    
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeInTheDocument()
    })
    
    const uploadButton = screen.getByRole('button', { name: /upload files/i })
    fireEvent.click(uploadButton)
    
    await waitFor(() => {
      expect(screen.getByRole('progressbar')).toBeInTheDocument()
    })
  })
})