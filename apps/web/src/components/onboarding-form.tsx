'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import PhoneInput from 'react-phone-number-input'
import Select from 'react-select'
import 'react-phone-number-input/style.css'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { countries } from '@/lib/countries'
import { useAuthStore } from '@/stores/auth.store'
import api from '@/lib/api'
import { authClient } from '@/lib/auth-client'

const onboardingSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format'),
  country: z.string().length(2, 'Please select a country')
})

type OnboardingFormValues = z.infer<typeof onboardingSchema>

export function OnboardingForm() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [phoneValue, setPhoneValue] = useState<string>('')
  const [selectedCountry, setSelectedCountry] = useState<{ value: string; label: string } | null>(null)
  const { user, setUser } = useAuthStore()

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm<OnboardingFormValues>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      name: '',
      phone: '',
      country: ''
    }
  })

  // Verify session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        const session = await authClient.getSession()
        if (!session?.data?.user) {
          router.push('/login')
        }
      } catch (error) {
        console.error('Session check error:', error)
        router.push('/login')
      }
    }
    checkSession()
  }, [router])

  const onSubmit = async (data: OnboardingFormValues) => {
    setIsLoading(true)
    setError(null)

    try {
      // First verify we have a session
      const session = await authClient.getSession()
      if (!session?.data?.user) {
        setError('Session expired. Please log in again.')
        router.push('/login')
        return
      }

      // Make the API call
      const response = await api.post('/api/v1/user/profile', data)
      
      // Update user in store with the response data
      if (response.data) {
        setUser({
          id: response.data.id,
          email: response.data.email,
          name: response.data.name,
          phone: response.data.phone,
          country: response.data.country,
          image: response.data.image || null
        })
      }
      
      // Redirect to dashboard
      router.push('/dashboard')
    } catch (err: any) {
      console.error('Profile update error:', err)
      
      // Handle specific error cases
      if (err.response?.status === 401) {
        setError('Session expired. Please log in again.')
        router.push('/login')
      } else if (err.response?.data?.issues) {
        // Handle validation errors from Zod
        const firstError = err.response.data.issues[0]
        setError(firstError?.message || 'Validation error')
      } else {
        setError(err.response?.data?.error || err.message || 'Failed to update profile')
      }
    } finally {
      setIsLoading(false)
    }
  }


  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Complete your profile</h2>
        <p className="text-gray-600">
          Help us personalize your experience by providing some basic information
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <div>
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            type="text"
            placeholder="John Doe"
            {...register('name')}
            disabled={isLoading}
          />
          {errors.name && (
            <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="phone">Phone Number</Label>
          <PhoneInput
            international
            defaultCountry="US"
            value={phoneValue}
            onChange={(value) => {
              setPhoneValue(value || '')
              setValue('phone', value || '')
            }}
            disabled={isLoading}
            className="w-full"
            inputComponent={Input}
          />
          {errors.phone && (
            <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="country">Country</Label>
          <Select
            id="country"
            options={countries}
            value={selectedCountry}
            onChange={(option: { value: string; label: string } | null) => {
              setSelectedCountry(option)
              setValue('country', option?.value || '')
            }}
            placeholder="Select your country"
            isSearchable
            isDisabled={isLoading}
            classNamePrefix="react-select"
            className="w-full"
          />
          {errors.country && (
            <p className="text-sm text-red-500 mt-1">{errors.country.message}</p>
          )}
        </div>
      </div>

      <Button
        type="submit"
        disabled={isLoading}
        className="w-full"
      >
        {isLoading ? 'Saving...' : 'Complete Profile'}
      </Button>
    </form>
  )
}