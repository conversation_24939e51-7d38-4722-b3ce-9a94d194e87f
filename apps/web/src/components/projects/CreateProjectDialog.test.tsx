import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CreateProjectDialog } from './CreateProjectDialog';
import { useProjectStore } from '@/stores/project.store';

// Mock dependencies
jest.mock('@/stores/project.store');

describe('CreateProjectDialog', () => {
  const mockCreateProject = jest.fn();
  const mockOnOpenChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useProjectStore as unknown as jest.Mock).mockReturnValue({
      createProject: mockCreateProject,
    });
  });

  it('should not render when closed', () => {
    render(<CreateProjectDialog open={false} onOpenChange={mockOnOpenChange} />);
    expect(screen.queryByText('Create New Project')).not.toBeInTheDocument();
  });

  it('should render when open', () => {
    render(<CreateProjectDialog open={true} onOpenChange={mockOnOpenChange} />);
    expect(screen.getByText('Create New Project')).toBeInTheDocument();
  });

  it('should validate required name field', async () => {
    render(<CreateProjectDialog open={true} onOpenChange={mockOnOpenChange} />);
    
    const createButton = screen.getByText('Create Project');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Project name is required')).toBeInTheDocument();
    });
    expect(mockCreateProject).not.toHaveBeenCalled();
  });

  it('should validate name length', async () => {
    render(<CreateProjectDialog open={true} onOpenChange={mockOnOpenChange} />);
    
    const nameInput = screen.getByLabelText(/Project Name/);
    fireEvent.change(nameInput, { target: { value: 'a'.repeat(101) } });
    
    const createButton = screen.getByText('Create Project');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Project name is too long')).toBeInTheDocument();
    });
  });

  it('should validate description length', async () => {
    render(<CreateProjectDialog open={true} onOpenChange={mockOnOpenChange} />);
    
    const nameInput = screen.getByLabelText(/Project Name/);
    const descInput = screen.getByLabelText(/Description/);
    
    fireEvent.change(nameInput, { target: { value: 'Test Project' } });
    fireEvent.change(descInput, { target: { value: 'a'.repeat(501) } });
    
    const createButton = screen.getByText('Create Project');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Description is too long')).toBeInTheDocument();
    });
  });

  it('should create project successfully', async () => {
    mockCreateProject.mockResolvedValue({ id: '1', name: 'Test Project' });
    
    render(<CreateProjectDialog open={true} onOpenChange={mockOnOpenChange} />);
    
    const nameInput = screen.getByLabelText(/Project Name/);
    const descInput = screen.getByLabelText(/Description/);
    
    fireEvent.change(nameInput, { target: { value: 'Test Project' } });
    fireEvent.change(descInput, { target: { value: 'Test description' } });
    
    const createButton = screen.getByText('Create Project');
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(mockCreateProject).toHaveBeenCalledWith({
        name: 'Test Project',
        description: 'Test description',
      });
      expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    });
  });

  it('should show character count for description', () => {
    render(<CreateProjectDialog open={true} onOpenChange={mockOnOpenChange} />);
    
    const descInput = screen.getByLabelText(/Description/);
    fireEvent.change(descInput, { target: { value: 'Test' } });
    
    expect(screen.getByText('4/500')).toBeInTheDocument();
  });

  it('should disable buttons while creating', async () => {
    mockCreateProject.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(<CreateProjectDialog open={true} onOpenChange={mockOnOpenChange} />);
    
    const nameInput = screen.getByLabelText(/Project Name/);
    fireEvent.change(nameInput, { target: { value: 'Test Project' } });
    
    const createButton = screen.getByText('Create Project');
    const cancelButton = screen.getByText('Cancel');
    
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(createButton).toBeDisabled();
      expect(cancelButton).toBeDisabled();
      expect(screen.getByText('Creating...')).toBeInTheDocument();
    });
  });

  it('should reset form when closing', () => {
    render(<CreateProjectDialog open={true} onOpenChange={mockOnOpenChange} />);
    
    const nameInput = screen.getByLabelText(/Project Name/);
    const descInput = screen.getByLabelText(/Description/);
    
    fireEvent.change(nameInput, { target: { value: 'Test Project' } });
    fireEvent.change(descInput, { target: { value: 'Test description' } });
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });
});