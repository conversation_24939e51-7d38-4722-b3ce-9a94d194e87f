import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ProjectsView } from './ProjectsView';
import { useProjectStore } from '@/stores/project.store';

// Mock dependencies
jest.mock('@/stores/project.store');
jest.mock('./ProjectList', () => ({
  ProjectList: ({ projects, onCreateProject }: any) => (
    <div data-testid="project-list">
      {projects.length} projects
      {onCreateProject && (
        <button onClick={onCreateProject}>Create from list</button>
      )}
    </div>
  ),
}));
jest.mock('./CreateProjectDialog', () => ({
  CreateProjectDialog: ({ open, onOpenChange }: any) => 
    open ? <div data-testid="create-dialog">Create Dialog</div> : null,
}));

describe('ProjectsView', () => {
  const mockFetchProjects = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    (useProjectStore as unknown as jest.Mock).mockReturnValue({
      projects: [],
      isLoading: false,
      error: null,
      fetchProjects: mockFetchProjects,
    });
  });

  it('should render with default title', () => {
    render(<ProjectsView />);
    expect(screen.getByText('Projects')).toBeInTheDocument();
  });

  it('should render with custom title', () => {
    render(<ProjectsView title="Dashboard" />);
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });

  it('should fetch projects on mount', () => {
    render(<ProjectsView />);
    expect(mockFetchProjects).toHaveBeenCalledTimes(1);
  });

  it('should not fetch projects if already loading', () => {
    (useProjectStore as unknown as jest.Mock).mockReturnValue({
      projects: [],
      isLoading: true,
      error: null,
      fetchProjects: mockFetchProjects,
    });
    
    render(<ProjectsView />);
    expect(mockFetchProjects).not.toHaveBeenCalled();
  });

  it('should show loading state', () => {
    (useProjectStore as unknown as jest.Mock).mockReturnValue({
      projects: [],
      isLoading: true,
      error: null,
      fetchProjects: mockFetchProjects,
    });
    
    const { container } = render(<ProjectsView />);
    expect(container.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('should show error state', () => {
    (useProjectStore as unknown as jest.Mock).mockReturnValue({
      projects: [],
      isLoading: false,
      error: 'Failed to load projects',
      fetchProjects: mockFetchProjects,
    });
    
    render(<ProjectsView />);
    expect(screen.getByText('Failed to load projects')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('should handle retry', async () => {
    (useProjectStore as unknown as jest.Mock).mockReturnValue({
      projects: [],
      isLoading: false,
      error: 'Failed to load projects',
      fetchProjects: mockFetchProjects,
    });
    
    render(<ProjectsView />);
    fireEvent.click(screen.getByText('Retry'));
    
    await waitFor(() => {
      expect(mockFetchProjects).toHaveBeenCalledTimes(1);
    });
  });

  it('should open create dialog when clicking new project button', () => {
    (useProjectStore as unknown as jest.Mock).mockReturnValue({
      projects: [{ id: '1', name: 'Test' }],
      isLoading: false,
      error: null,
      fetchProjects: mockFetchProjects,
    });
    
    render(<ProjectsView />);
    fireEvent.click(screen.getByLabelText('Create new project'));
    
    expect(screen.getByTestId('create-dialog')).toBeInTheDocument();
  });
});