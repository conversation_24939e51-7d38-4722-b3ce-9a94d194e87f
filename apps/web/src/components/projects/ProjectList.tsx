'use client';

import { useCallback, useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FolderOpen, Calendar, Plus, Trash2, MoreVertical } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Project } from '@/types/project';
import { useProjectStore } from '@/stores/project.store';
import { useProject } from '@/contexts/project-context';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface ProjectListProps {
  projects: Project[];
  onCreateProject?: () => void;
}

export function ProjectList({ projects, onCreateProject }: ProjectListProps) {
  const router = useRouter();
  const { setCurrentProject } = useProject();
  const { selectedProject, selectProject, deleteProject } = useProjectStore();
  const isNavigatingRef = useRef(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleProjectClick = useCallback((project: Project) => {
    // Prevent multiple navigations
    if (isNavigatingRef.current) return;
    
    isNavigatingRef.current = true;
    selectProject(project);
    setCurrentProject(project);
    router.push(`/projects/${project.id}`);
    
    // Reset after navigation
    setTimeout(() => {
      isNavigatingRef.current = false;
    }, 1000);
  }, [router, selectProject, setCurrentProject]);

  const handleDeleteClick = useCallback((e: React.MouseEvent, project: Project) => {
    e.stopPropagation();
    setProjectToDelete(project);
    setDeleteDialogOpen(true);
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    if (!projectToDelete) return;

    setIsDeleting(true);
    try {
      await deleteProject(projectToDelete.id);
      setDeleteDialogOpen(false);
      setProjectToDelete(null);
    } catch (error) {
      // Error is handled in the store
    } finally {
      setIsDeleting(false);
    }
  }, [projectToDelete, deleteProject]);

  if (projects.length === 0) {
    return (
      <Card className="p-12 text-center">
        <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No projects yet</h3>
        <p className="text-gray-600 mb-4">Create your first project to get started</p>
        {onCreateProject && (
          <Button onClick={onCreateProject} className="gap-2">
            <Plus className="h-4 w-4" />
            Create Project
          </Button>
        )}
      </Card>
    );
  }

  return (
    <>
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {projects.map((project) => (
        <Card
          key={project.id}
          className="p-6 cursor-pointer transition-all hover:shadow-lg hover:scale-105 focus-within:ring-2 focus-within:ring-primary"
          onClick={() => handleProjectClick(project)}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleProjectClick(project);
            }
          }}
          aria-label={`Open project: ${project.name}`}
        >
          <div className="flex items-start justify-between mb-4">
            <FolderOpen className="h-8 w-8 text-primary" aria-hidden="true" />
            <div className="flex items-center gap-2">
              {selectedProject?.id === project.id && (
                <span className="text-xs bg-primary text-white px-2 py-1 rounded" aria-label="Currently selected">
                  Selected
                </span>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    aria-label="Project options"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="text-destructive cursor-pointer"
                    onClick={(e) => handleDeleteClick(e, project)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Project
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          <h3 className="text-lg font-semibold mb-2">{project.name}</h3>
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
            {project.description || 'No description'}
          </p>
          <div className="flex items-center text-xs text-gray-500">
            <Calendar className="h-3 w-3 mr-1" aria-hidden="true" />
            <time dateTime={new Date(project.createdAt).toISOString()}>
              {new Date(project.createdAt).toLocaleDateString()}
            </time>
          </div>
        </Card>
      ))}
    </div>

    <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Project</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete &quot;{projectToDelete?.name}&quot;? This action cannot be undone.
            All agents and data associated with this project will be permanently deleted.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDeleteConfirm}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? 'Deleting...' : 'Delete'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
    </>
  );
}