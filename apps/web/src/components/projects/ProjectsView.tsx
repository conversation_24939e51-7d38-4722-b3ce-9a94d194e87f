'use client';

import { useEffect, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useProjectStore } from '@/stores/project.store';
import { ProjectList } from './ProjectList';
import { CreateProjectDialog } from './CreateProjectDialog';
import { LoadingState } from '@/components/common/LoadingState';
import { ErrorState } from '@/components/common/ErrorState';

interface ProjectsViewProps {
  title?: string;
}

export function ProjectsView({ title = 'Projects' }: ProjectsViewProps) {
  const { projects, isLoading, error, fetchProjects } = useProjectStore();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);

  useEffect(() => {
    // Only fetch if we haven't fetched yet and we're not already loading
    if (!hasFetched && !isLoading) {
      fetchProjects();
      setHasFetched(true);
    }
  }, [hasFetched, isLoading, fetchProjects]);

  const handleRetry = useCallback(() => {
    setHasFetched(false);
  }, []);

  if (isLoading && projects.length === 0) {
    return <LoadingState title={title} cardCount={3} />;
  }

  if (error && projects.length === 0) {
    return <ErrorState error={error} onRetry={handleRetry} />;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">{title}</h1>
          <Button 
            onClick={() => setShowCreateDialog(true)} 
            variant="outline" 
            className="gap-2"
            aria-label="Create new project"
          >
            <Plus className="h-4 w-4" />
            New Project
          </Button>
        </div>

        <ProjectList 
          projects={projects} 
          onCreateProject={() => setShowCreateDialog(true)}
        />

        <CreateProjectDialog 
          open={showCreateDialog} 
          onOpenChange={setShowCreateDialog}
        />
      </div>
    </div>
  );
}