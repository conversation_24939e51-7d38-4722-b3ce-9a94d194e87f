import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ProjectList } from './ProjectList';
import { useRouter } from 'next/navigation';
import { useProjectStore } from '@/stores/project.store';
import { useProject } from '@/contexts/project-context';
import { Project } from '@/types/project';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/stores/project.store', () => ({
  useProjectStore: jest.fn(),
}));

jest.mock('@/contexts/project-context', () => ({
  useProject: jest.fn(),
}));

describe('ProjectList', () => {
  const mockPush = jest.fn();
  const mockSelectProject = jest.fn();
  const mockSetCurrentProject = jest.fn();
  const mockDeleteProject = jest.fn();

  const mockProjects: Project[] = [
    {
      id: '1',
      name: 'Project Alpha',
      description: 'First test project',
      accessType: 'PERSONAL' as const,
      userId: 'user1',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
    },
    {
      id: '2',
      name: 'Project Beta',
      description: null,
      accessType: 'COMPANY_WIDE' as const,
      userId: 'user1',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    (useProjectStore as jest.Mock).mockReturnValue({
      selectedProject: null,
      selectProject: mockSelectProject,
      deleteProject: mockDeleteProject,
    });
    (useProject as jest.Mock).mockReturnValue({
      setCurrentProject: mockSetCurrentProject,
    });
  });

  describe('Empty state', () => {
    it('should show empty state when no projects', () => {
      render(<ProjectList projects={[]} />);

      expect(screen.getByText('No projects yet')).toBeInTheDocument();
      expect(screen.getByText('Create your first project to get started')).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /create project/i })).not.toBeInTheDocument();
    });

    it('should show create button in empty state when onCreateProject provided', () => {
      const mockOnCreate = jest.fn();
      render(<ProjectList projects={[]} onCreateProject={mockOnCreate} />);

      const createButton = screen.getByRole('button', { name: /create project/i });
      expect(createButton).toBeInTheDocument();
      
      fireEvent.click(createButton);
      expect(mockOnCreate).toHaveBeenCalled();
    });
  });

  describe('Project list display', () => {
    it('should render all projects', () => {
      render(<ProjectList projects={mockProjects} />);

      expect(screen.getByText('Project Alpha')).toBeInTheDocument();
      expect(screen.getByText('Project Beta')).toBeInTheDocument();
      expect(screen.getByText('First test project')).toBeInTheDocument();
      expect(screen.getByText('No description')).toBeInTheDocument();
    });

    it('should format dates correctly', () => {
      render(<ProjectList projects={mockProjects} />);

      expect(screen.getByText('1/1/2024')).toBeInTheDocument();
      expect(screen.getByText('1/2/2024')).toBeInTheDocument();
    });

    it('should show selected indicator for selected project', () => {
      (useProjectStore as jest.Mock).mockReturnValue({
        selectedProject: mockProjects[0],
        selectProject: mockSelectProject,
        deleteProject: mockDeleteProject,
      });

      render(<ProjectList projects={mockProjects} />);

      expect(screen.getByText('Selected')).toBeInTheDocument();
    });
  });

  describe('Project interaction', () => {
    it('should handle project click correctly', () => {
      render(<ProjectList projects={mockProjects} />);

      const projectCard = screen.getByText('Project Alpha').closest('.cursor-pointer');
      fireEvent.click(projectCard!);

      expect(mockSelectProject).toHaveBeenCalledWith(mockProjects[0]);
      expect(mockSetCurrentProject).toHaveBeenCalledWith(mockProjects[0]);
      expect(mockPush).toHaveBeenCalledWith('/projects/1/agents');
    });

    it('should navigate to correct project', () => {
      render(<ProjectList projects={mockProjects} />);

      const projectCard = screen.getByText('Project Beta').closest('.cursor-pointer');
      fireEvent.click(projectCard!);

      expect(mockPush).toHaveBeenCalledWith('/projects/2/agents');
    });
  });

  describe('Responsive grid', () => {
    it('should apply responsive grid classes', () => {
      render(<ProjectList projects={mockProjects} />);

      const grid = screen.getByText('Project Alpha').closest('.grid');
      expect(grid).toHaveClass('grid', 'gap-4', 'md:grid-cols-2', 'lg:grid-cols-3');
    });
  });

  describe('Project deletion', () => {
    it('should show dropdown menu when more button is clicked', async () => {
      const user = userEvent.setup();
      render(<ProjectList projects={mockProjects} />);

      const moreButtons = screen.getAllByRole('button', { name: /project options/i });
      await user.click(moreButtons[0]);

      expect(screen.getByText('Delete Project')).toBeInTheDocument();
    });

    it('should open delete confirmation dialog', async () => {
      const user = userEvent.setup();
      render(<ProjectList projects={mockProjects} />);

      const moreButtons = screen.getAllByRole('button', { name: /project options/i });
      await user.click(moreButtons[0]);
      await user.click(screen.getByText('Delete Project'));

      expect(screen.getByText('Delete Project')).toBeInTheDocument();
      expect(screen.getByText(/Are you sure you want to delete "Project Alpha"/)).toBeInTheDocument();
    });

    it('should call deleteProject when confirmed', async () => {
      const user = userEvent.setup();
      render(<ProjectList projects={mockProjects} />);

      const moreButtons = screen.getAllByRole('button', { name: /project options/i });
      await user.click(moreButtons[0]);
      await user.click(screen.getByText('Delete Project'));

      const deleteButton = screen.getByRole('button', { name: 'Delete' });
      await user.click(deleteButton);

      await waitFor(() => {
        expect(mockDeleteProject).toHaveBeenCalledWith('1');
      });
    });

    it('should close dialog when cancelled', async () => {
      const user = userEvent.setup();
      render(<ProjectList projects={mockProjects} />);

      const moreButtons = screen.getAllByRole('button', { name: /project options/i });
      await user.click(moreButtons[0]);
      await user.click(screen.getByText('Delete Project'));

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      await user.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByText(/Are you sure you want to delete/)).not.toBeInTheDocument();
      });
      expect(mockDeleteProject).not.toHaveBeenCalled();
    });
  });
});