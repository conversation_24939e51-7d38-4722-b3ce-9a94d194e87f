'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Zap, MousePointer, Clock } from 'lucide-react';
import { agentService } from '@/services/agent.service';
import type { Agent } from '@/types/agent';

interface AgentsListProps {
  projectId: string;
  onAgentClick?: (agent: Agent) => void;
}

export function AgentsList({ projectId, onAgentClick }: AgentsListProps) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAgents = useCallback(async () => {
    if (!projectId) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await agentService.getAgentsByProjectId(projectId);
      setAgents(data);
    } catch (err: any) {
      console.error('Failed to fetch agents:', err);
      const errorMessage = err?.response?.data?.message || err?.message || 'Failed to load agents. Please try again.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchAgents();
  }, [fetchAgents]);

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-8">
        <div className="text-center">
          <p className="font-semibold mb-2 text-destructive">Error Loading Agents</p>
          <p className="text-sm text-muted-foreground mb-4">{error}</p>
          <Button 
            variant="outline" 
            size="sm"
            onClick={fetchAgents}
          >
            Try Again
          </Button>
        </div>
      </Card>
    );
  }

  if (agents.length === 0) {
    return (
      <Card className="p-12">
        <div className="text-center text-muted-foreground">
          <Zap className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium mb-2">No agents yet</p>
          <p className="text-sm">Click &quot;Create New Agent&quot; to add your first agent to this project.</p>
        </div>
      </Card>
    );
  }

  const getTriggerIcon = (triggerType: string) => {
    switch (triggerType.toLowerCase()) {
      case 'click':
      case 'button':
        return MousePointer;
      case 'schedule':
      case 'cron':
        return Clock;
      default:
        return Zap;
    }
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {agents.map((agent) => {
        const TriggerIcon = getTriggerIcon(agent.triggerType);
        
        return (
          <Card
            key={agent.id}
            className="p-6 hover:shadow-lg transition-all cursor-pointer"
            onClick={() => onAgentClick?.(agent)}
          >
            <div className="flex items-start justify-between mb-4">
              <TriggerIcon className="h-6 w-6 text-primary" />
              <Badge variant="secondary" className="text-xs">
                {agent.triggerType}
              </Badge>
            </div>
            
            <h3 className="font-semibold text-lg mb-2 line-clamp-1">
              {agent.title}
            </h3>
            
            <div className="flex items-center text-sm text-muted-foreground">
              <Calendar className="h-3 w-3 mr-1" />
              <span>Modified {new Date(agent.updatedAt).toLocaleDateString()}</span>
            </div>
          </Card>
        );
      })}
    </div>
  );
}