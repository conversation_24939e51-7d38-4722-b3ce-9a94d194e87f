import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { AgentsList } from './AgentsList';
import { agentService } from '@/services/agent.service';

jest.mock('@/services/agent.service', () => ({
  agentService: {
    getAgentsByProjectId: jest.fn(),
  },
}));

describe('AgentsList', () => {
  const mockAgents = [
    {
      id: 'agent-1',
      title: 'Test Agent 1',
      workflowJson: {},
      triggerType: 'click',
      projectId: 'project-123',
      createdAt: new Date('2025-01-01'),
      updatedAt: new Date('2025-01-15'),
    },
    {
      id: 'agent-2',
      title: 'Test Agent 2',
      workflowJson: {},
      triggerType: 'schedule',
      projectId: 'project-123',
      createdAt: new Date('2025-01-02'),
      updatedAt: new Date('2025-01-16'),
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading skeleton initially', () => {
    render(<AgentsList projectId="project-123" />);
    
    const skeletons = document.querySelectorAll('.animate-pulse');
    expect(skeletons).toHaveLength(3);
    expect(skeletons[0]).toHaveClass('p-6');
  });

  it('should fetch and display agents', async () => {
    (agentService.getAgentsByProjectId as jest.Mock).mockResolvedValue(mockAgents);
    
    render(<AgentsList projectId="project-123" />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Agent 1')).toBeInTheDocument();
      expect(screen.getByText('Test Agent 2')).toBeInTheDocument();
      expect(screen.getByText('click')).toBeInTheDocument();
      expect(screen.getByText('schedule')).toBeInTheDocument();
    });
  });

  it('should display empty state when no agents', async () => {
    (agentService.getAgentsByProjectId as jest.Mock).mockResolvedValue([]);
    
    render(<AgentsList projectId="project-123" />);
    
    await waitFor(() => {
      expect(screen.getByText('No agents yet')).toBeInTheDocument();
      expect(screen.getByText(/Click "Create New Agent"/)).toBeInTheDocument();
    });
  });

  it('should display error state on fetch failure', async () => {
    (agentService.getAgentsByProjectId as jest.Mock).mockRejectedValue(new Error('Network error'));
    
    render(<AgentsList projectId="project-123" />);
    
    await waitFor(() => {
      expect(screen.getByText('Error Loading Agents')).toBeInTheDocument();
      expect(screen.getByText('Network error')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
    });
  });

  it('should call onAgentClick when agent card is clicked', async () => {
    const mockOnAgentClick = jest.fn();
    (agentService.getAgentsByProjectId as jest.Mock).mockResolvedValue(mockAgents);
    
    render(<AgentsList projectId="project-123" onAgentClick={mockOnAgentClick} />);
    
    await waitFor(() => {
      const agentCard = screen.getByText('Test Agent 1').closest('.cursor-pointer');
      fireEvent.click(agentCard!);
    });
    
    expect(mockOnAgentClick).toHaveBeenCalledWith(mockAgents[0]);
  });

  it('should display correct icon for trigger types', async () => {
    const agentsWithDifferentTriggers = [
      { ...mockAgents[0], triggerType: 'click' },
      { ...mockAgents[1], triggerType: 'schedule' },
      { ...mockAgents[0], id: 'agent-3', triggerType: 'api' },
    ];
    
    (agentService.getAgentsByProjectId as jest.Mock).mockResolvedValue(agentsWithDifferentTriggers);
    
    render(<AgentsList projectId="project-123" />);
    
    await waitFor(() => {
      const badges = screen.getAllByRole('status');
      expect(badges[0]).toHaveTextContent('click');
      expect(badges[1]).toHaveTextContent('schedule');
      expect(badges[2]).toHaveTextContent('api');
    });
  });

  it('should format date correctly', async () => {
    (agentService.getAgentsByProjectId as jest.Mock).mockResolvedValue(mockAgents);
    
    render(<AgentsList projectId="project-123" />);
    
    await waitFor(() => {
      expect(screen.getByText(/Modified 1\/15\/2025/)).toBeInTheDocument();
      expect(screen.getByText(/Modified 1\/16\/2025/)).toBeInTheDocument();
    });
  });

  it('should not fetch agents when projectId is not provided', () => {
    render(<AgentsList projectId="" />);
    
    expect(agentService.getAgentsByProjectId).not.toHaveBeenCalled();
  });
});