import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MembersList } from './MembersList';
import { memberService } from '@/services/member.service';
import { useMembersStore } from '@/stores/members.store';
import { useAuth } from '@/lib/auth-client';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/services/member.service');
jest.mock('@/stores/members.store');
jest.mock('@/lib/auth-client');
jest.mock('sonner');

const mockMemberService = memberService as jest.Mocked<typeof memberService>;
const mockUseMembersStore = useMembersStore as jest.MockedFunction<typeof useMembersStore>;
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockToast = toast as jest.Mocked<typeof toast>;

describe('MembersList', () => {
  const mockProjectId = 'project-123';
  const mockCurrentUser = { id: 'user-123', email: '<EMAIL>' };
  const mockMembers = [
    {
      userId: 'user-123',
      projectId: mockProjectId,
      role: 'ADMIN' as const,
      invitedAt: '2025-07-22T10:00:00Z',
      joinedAt: '2025-07-22T10:00:00Z',
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Admin User',
        emailVerified: true,
        image: null,
        phone: null,
        country: null,
        createdAt: '2025-07-22T10:00:00Z',
        updatedAt: '2025-07-22T10:00:00Z',
      },
    },
    {
      userId: 'user-456',
      projectId: mockProjectId,
      role: 'MEMBER' as const,
      invitedAt: '2025-07-22T11:00:00Z',
      joinedAt: '2025-07-22T11:00:00Z',
      user: {
        id: 'user-456',
        email: '<EMAIL>',
        name: 'Member User',
        emailVerified: true,
        image: null,
        phone: null,
        country: null,
        createdAt: '2025-07-22T11:00:00Z',
        updatedAt: '2025-07-22T11:00:00Z',
      },
    },
  ];

  const mockStoreFunctions = {
    setMembers: jest.fn(),
    updateMember: jest.fn(),
    removeMember: jest.fn(),
    setLoading: jest.fn(),
    setError: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseAuth.mockReturnValue({
      user: mockCurrentUser,
      isLoading: false,
      signIn: jest.fn(),
      signOut: jest.fn(),
    } as any);

    mockUseMembersStore.mockReturnValue({
      members: new Map([[mockProjectId, mockMembers]]),
      loading: false,
      error: null,
      ...mockStoreFunctions,
    } as any);

    mockMemberService.getProjectMembers.mockResolvedValue(mockMembers);
  });

  it('renders loading state', () => {
    mockUseMembersStore.mockReturnValue({
      members: new Map(),
      loading: true,
      error: null,
      ...mockStoreFunctions,
    } as any);

    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    expect(screen.getByText('Searching...')).toBeInTheDocument();
  });

  it('renders error state', () => {
    const errorMessage = 'Failed to load members';
    mockUseMembersStore.mockReturnValue({
      members: new Map(),
      loading: false,
      error: errorMessage,
      ...mockStoreFunctions,
    } as any);

    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('renders empty state', () => {
    mockUseMembersStore.mockReturnValue({
      members: new Map([[mockProjectId, []]]),
      loading: false,
      error: null,
      ...mockStoreFunctions,
    } as any);

    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    expect(screen.getByText('No members yet')).toBeInTheDocument();
    expect(screen.getByText('Add team members to collaborate on this project.')).toBeInTheDocument();
  });

  it('renders members list', () => {
    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    expect(screen.getByText('Admin User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Member User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('shows admin badge for admin members', () => {
    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    const adminMemberElement = screen.getByText('Admin User').closest('div');
    expect(adminMemberElement).toHaveTextContent('Admin User');
    // Check for shield icon by looking for svg with appropriate class
    const shieldIcon = adminMemberElement?.querySelector('svg.text-primary');
    expect(shieldIcon).toBeInTheDocument();
  });

  it('shows actions menu for admin users', () => {
    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    const actionButtons = screen.getAllByRole('button');
    // Should have action buttons for the non-current user
    expect(actionButtons.length).toBeGreaterThan(0);
  });

  it('hides actions menu for non-admin users', () => {
    render(<MembersList projectId={mockProjectId} currentUserRole="MEMBER" />);
    
    const actionButtons = screen.queryAllByRole('button');
    // Should not have action buttons except "Try Again" if error
    expect(actionButtons.filter(btn => btn.textContent !== 'Try Again').length).toBe(0);
  });

  it('changes member role from member to admin', async () => {
    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    // Find and click the action menu for the member user
    const memberRow = screen.getByText('Member User').closest('.flex');
    const actionButton = memberRow?.querySelector('button');
    fireEvent.click(actionButton!);
    
    // Click "Make Admin"
    const makeAdminButton = await screen.findByText('Make Admin');
    fireEvent.click(makeAdminButton);
    
    await waitFor(() => {
      expect(mockMemberService.updateMemberRole).toHaveBeenCalledWith(
        mockProjectId,
        'user-456',
        { role: 'ADMIN' }
      );
      expect(mockToast.success).toHaveBeenCalledWith('Member User is now an admin');
    });
  });

  it('shows remove confirmation dialog', async () => {
    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    // Find and click the action menu for the member user
    const memberRow = screen.getByText('Member User').closest('.flex');
    const actionButton = memberRow?.querySelector('button');
    fireEvent.click(actionButton!);
    
    // Click "Remove from Project"
    const removeButton = await screen.findByText('Remove from Project');
    fireEvent.click(removeButton);
    
    // Check confirmation dialog appears
    expect(screen.getByText('Remove Member')).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to remove Member User/)).toBeInTheDocument();
  });

  it('removes member after confirmation', async () => {
    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    // Open action menu and click remove
    const memberRow = screen.getByText('Member User').closest('.flex');
    const actionButton = memberRow?.querySelector('button');
    fireEvent.click(actionButton!);
    
    const removeButton = await screen.findByText('Remove from Project');
    fireEvent.click(removeButton);
    
    // Confirm removal
    const confirmButton = screen.getByRole('button', { name: 'Remove' });
    fireEvent.click(confirmButton);
    
    await waitFor(() => {
      expect(mockMemberService.removeMember).toHaveBeenCalledWith(mockProjectId, 'user-456');
      expect(mockToast.success).toHaveBeenCalledWith('Member User has been removed from the project');
    });
  });

  it('prevents removing last admin', () => {
    // Only one admin in the list
    mockUseMembersStore.mockReturnValue({
      members: new Map([[mockProjectId, [mockMembers[0]]]]),
      loading: false,
      error: null,
      ...mockStoreFunctions,
    } as any);

    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    // Should show "You" text instead of action menu
    expect(screen.getByText('You')).toBeInTheDocument();
  });

  it('loads members on mount', () => {
    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    expect(mockStoreFunctions.setLoading).toHaveBeenCalledWith(true);
    expect(mockMemberService.getProjectMembers).toHaveBeenCalledWith(mockProjectId);
  });

  it('handles error during member loading', async () => {
    const error = new Error('Network error');
    mockMemberService.getProjectMembers.mockRejectedValueOnce(error);
    
    render(<MembersList projectId={mockProjectId} currentUserRole="ADMIN" />);
    
    await waitFor(() => {
      expect(mockStoreFunctions.setError).toHaveBeenCalledWith('Failed to load members');
      expect(mockToast.error).toHaveBeenCalledWith('Failed to load members');
    });
  });
});