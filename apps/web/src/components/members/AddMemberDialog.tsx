'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Search, Loader2 } from 'lucide-react';
import { memberService } from '@/services/member.service';
import { userService } from '@/services/user.service';
import { toast } from 'sonner';
import type { User } from '@/types/user';

interface AddMemberDialogProps {
  projectId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onMemberAdded: () => void;
  existingMemberIds: string[];
}

export function AddMemberDialog({
  projectId,
  open,
  onOpenChange,
  onMemberAdded,
  existingMemberIds,
}: AddMemberDialogProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<'MEMBER' | 'ADMIN'>('MEMBER');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [searching, setSearching] = useState(false);
  const [adding, setAdding] = useState(false);

  useEffect(() => {
    if (!open) {
      // Reset form when dialog closes
      setSearchTerm('');
      setSelectedUserId(null);
      setSelectedRole('MEMBER');
      setSearchResults([]);
    }
  }, [open]);

  useEffect(() => {
    const searchUsers = async () => {
      if (searchTerm.length < 2) {
        setSearchResults([]);
        return;
      }

      setSearching(true);
      try {
        const users = await userService.searchUsers(searchTerm);
        // Filter out existing members
        const availableUsers = users.filter(user => !existingMemberIds.includes(user.id));
        setSearchResults(availableUsers);
      } catch (err) {
        console.error('Failed to search users:', err);
        toast.error('Failed to search users');
      } finally {
        setSearching(false);
      }
    };

    const debounceTimer = setTimeout(searchUsers, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchTerm, existingMemberIds]);

  const handleAddMember = async () => {
    if (!selectedUserId) return;

    setAdding(true);
    try {
      await memberService.addMember(projectId, {
        userId: selectedUserId,
        role: selectedRole,
      });
      toast.success('Member added successfully');
      onMemberAdded();
      onOpenChange(false);
    } catch (err: any) {
      toast.error(err.response?.data?.error || 'Failed to add member');
    } finally {
      setAdding(false);
    }
  };

  const getInitials = (name?: string, email?: string) => {
    if (name) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return email?.charAt(0).toUpperCase() || '?';
  };

  const selectedUser = searchResults.find(u => u.id === selectedUserId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Team Member</DialogTitle>
          <DialogDescription>
            Search for users to add to your project. You can set their role as either a member or an admin.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Search Input */}
          <div className="space-y-2">
            <Label htmlFor="search">Search Users</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Search Results */}
          {searchTerm.length >= 2 && (
            <div className="space-y-2">
              <Label>Select User</Label>
              <div className="border rounded-lg max-h-[200px] overflow-y-auto">
                {searching ? (
                  <div className="p-4 text-center text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin mx-auto mb-2" />
                    Searching...
                  </div>
                ) : searchResults.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground">
                    No users found
                  </div>
                ) : (
                  searchResults.map((user) => (
                    <button
                      key={user.id}
                      onClick={() => setSelectedUserId(user.id)}
                      className={`w-full p-3 flex items-center gap-3 hover:bg-accent/50 transition-colors ${
                        selectedUserId === user.id ? 'bg-accent' : ''
                      }`}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.image || undefined} />
                        <AvatarFallback>{getInitials(user.name, user.email)}</AvatarFallback>
                      </Avatar>
                      <div className="text-left">
                        <p className="font-medium text-sm">{user.name || user.email}</p>
                        {user.name && (
                          <p className="text-xs text-muted-foreground">{user.email}</p>
                        )}
                      </div>
                    </button>
                  ))
                )}
              </div>
            </div>
          )}

          {/* Selected User & Role */}
          {selectedUser && (
            <>
              <div className="space-y-2">
                <Label>Selected User</Label>
                <div className="p-3 border rounded-lg flex items-center gap-3 bg-accent/50">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={selectedUser.image || undefined} />
                    <AvatarFallback>{getInitials(selectedUser.name, selectedUser.email)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-sm">{selectedUser.name || selectedUser.email}</p>
                    {selectedUser.name && (
                      <p className="text-xs text-muted-foreground">{selectedUser.email}</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as 'MEMBER' | 'ADMIN')}>
                  <SelectTrigger id="role">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MEMBER">Member</SelectItem>
                    <SelectItem value="ADMIN">Admin</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  {selectedRole === 'ADMIN'
                    ? 'Admins can manage project settings and team members'
                    : 'Members can view and work with project agents'}
                </p>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={adding}>
            Cancel
          </Button>
          <Button
            onClick={handleAddMember}
            disabled={!selectedUserId || adding}
          >
            {adding && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Add Member
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}