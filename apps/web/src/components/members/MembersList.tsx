'use client';

import React, { useEffect, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { MoreHorizontal, Shield, User, UserMinus } from 'lucide-react';
import type { ProjectMember } from '@/types/member';
import { memberService } from '@/services/member.service';
import { useMembersStore } from '@/stores/members.store';
import { useSession } from '@/lib/auth-client';
import { toast } from 'sonner';

interface MembersListProps {
  projectId: string;
  currentUserRole?: 'ADMIN' | 'MEMBER';
}

export function MembersList({ projectId, currentUserRole }: MembersListProps) {
  const { data: session } = useSession();
  const currentUser = session?.user;
  const { members, loading, error, setMembers, updateMember, removeMember, setLoading, setError } = useMembersStore();
  const projectMembers = members.get(projectId) || [];
  const [removingMemberId, setRemovingMemberId] = useState<string | null>(null);

  useEffect(() => {
    loadMembers();
  }, [projectId]);

  const loadMembers = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await memberService.getProjectMembers(projectId);
      setMembers(projectId, data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load members');
      toast.error('Failed to load members');
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async (member: ProjectMember, newRole: 'ADMIN' | 'MEMBER') => {
    try {
      const updated = await memberService.updateMemberRole(projectId, member.userId, { role: newRole });
      updateMember(projectId, member.userId, updated);
      toast.success(`${member.user.name || member.user.email} is now ${newRole === 'ADMIN' ? 'an admin' : 'a member'}`);
    } catch (err: any) {
      toast.error(err.response?.data?.error || 'Failed to update role');
    }
  };

  const handleRemoveMember = async (member: ProjectMember) => {
    try {
      await memberService.removeMember(projectId, member.userId);
      removeMember(projectId, member.userId);
      toast.success(`${member.user.name || member.user.email} has been removed from the project`);
      setRemovingMemberId(null);
    } catch (err: any) {
      toast.error(err.response?.data?.error || 'Failed to remove member');
    }
  };

  const getInitials = (name?: string, email?: string) => {
    if (name) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return email?.charAt(0).toUpperCase() || '?';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 bg-gray-200 rounded-full" />
              <div>
                <div className="h-4 w-32 bg-gray-200 rounded mb-2" />
                <div className="h-3 w-24 bg-gray-200 rounded" />
              </div>
            </div>
            <div className="h-8 w-8 bg-gray-200 rounded" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-600">
        <p>{error}</p>
        <Button onClick={loadMembers} variant="outline" className="mt-4">
          Try Again
        </Button>
      </div>
    );
  }

  if (projectMembers.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <User className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>No members yet</p>
        <p className="text-sm mt-2">Add team members to collaborate on this project.</p>
      </div>
    );
  }

  const isCurrentUserAdmin = currentUserRole === 'ADMIN';
  const adminCount = projectMembers.filter(m => m.role === 'ADMIN').length;

  return (
    <>
      <div className="space-y-2">
        {projectMembers.map((member) => {
          const isCurrentUser = member.userId === currentUser?.id;
          const canManageMember = isCurrentUserAdmin && (!isCurrentUser || (isCurrentUser && adminCount > 1));

          return (
            <div key={member.userId} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={member.user.image || undefined} />
                  <AvatarFallback>{getInitials(member.user.name, member.user.email)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">{member.user.name || member.user.email}</p>
                    {member.role === 'ADMIN' && (
                      <Shield className="h-4 w-4 text-primary" />
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{member.user.email}</p>
                </div>
              </div>

              {canManageMember && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {member.role === 'MEMBER' ? (
                      <DropdownMenuItem onClick={() => handleRoleChange(member, 'ADMIN')}>
                        <Shield className="h-4 w-4 mr-2" />
                        Make Admin
                      </DropdownMenuItem>
                    ) : (
                      adminCount > 1 && (
                        <DropdownMenuItem onClick={() => handleRoleChange(member, 'MEMBER')}>
                          <User className="h-4 w-4 mr-2" />
                          Make Member
                        </DropdownMenuItem>
                      )
                    )}
                    {(!isCurrentUser || adminCount > 1) && (
                      <DropdownMenuItem
                        onClick={() => setRemovingMemberId(member.userId)}
                        className="text-red-600"
                      >
                        <UserMinus className="h-4 w-4 mr-2" />
                        Remove from Project
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {isCurrentUser && !canManageMember && (
                <span className="text-sm text-muted-foreground">You</span>
              )}
            </div>
          );
        })}
      </div>

      <AlertDialog open={!!removingMemberId} onOpenChange={() => setRemovingMemberId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove {projectMembers.find(m => m.userId === removingMemberId)?.user.name || 'this member'} from the project? They will lose access to all project resources.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                const member = projectMembers.find(m => m.userId === removingMemberId);
                if (member) handleRemoveMember(member);
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}