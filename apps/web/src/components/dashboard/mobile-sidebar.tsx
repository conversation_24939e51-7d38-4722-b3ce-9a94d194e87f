"use client"

import { Sheet, SheetContent } from "@/components/ui/sheet"
import { Sidebar } from "./sidebar"

interface MobileSidebarProps {
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}

export function MobileSidebar({ isOpen, setIsOpen }: MobileSidebarProps) {
  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetContent side="left" className="p-0 w-[240px]">
        <Sidebar isCollapsed={false} setIsCollapsed={() => {}} />
      </SheetContent>
    </Sheet>
  )
}