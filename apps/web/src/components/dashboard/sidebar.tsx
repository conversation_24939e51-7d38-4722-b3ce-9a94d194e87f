"use client"

import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Home,
  LogOut,
  ChevronLeft,
  Menu,
  FolderOpen,
  Database,
} from "lucide-react"
import { useState, useEffect } from "react"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { authClient } from "@/lib/auth-client"
import { useAuthStore } from "@/stores/auth.store"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  isCollapsed: boolean
  setIsCollapsed: (collapsed: boolean) => void
}

export function Sidebar({ className, isCollapsed, setIsCollapsed }: SidebarProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { user, clearAuth } = useAuthStore()
  const [sessionUser, setSessionUser] = useState<any>(null)

  useEffect(() => {
    const fetchUser = async () => {
      const session = await authClient.getSession()
      if (session?.data?.user) {
        setSessionUser(session.data.user)
      }
    }
    fetchUser()
  }, [])

  const handleLogout = async () => {
    await authClient.signOut()
    clearAuth()
    router.push('/login')
  }

  const currentUser = user || sessionUser
  const userInitials = currentUser?.name
    ? currentUser.name
        .split(' ')
        .map((n: string) => n[0])
        .join('')
        .toUpperCase()
    : currentUser?.email?.[0]?.toUpperCase() || 'U'

  const routes = [
    {
      label: "Dashboard",
      icon: Home,
      href: "/dashboard",
    },
    {
      label: "Projects",
      icon: FolderOpen,
      href: "/projects",
    },
    {
      label: "Knowledge Base",
      icon: Database,
      href: "/knowledge-base",
    },
  ]

  return (
    <div
      className={cn(
        "relative flex h-full flex-col bg-muted/10 border-r transition-all duration-300 ease-in-out",
        isCollapsed ? "w-[60px]" : "w-[240px]",
        className
      )}
    >
      <div className="flex h-14 items-center justify-between border-b px-3">
        <Link 
          href="/dashboard" 
          className={cn(
            "flex items-center gap-2 transition-all duration-300",
            isCollapsed && "justify-center"
          )}
        >
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground transition-transform hover:scale-110">
            S
          </div>
          {!isCollapsed && (
            <span className="text-lg font-semibold animate-in fade-in-0 slide-in-from-left-2 duration-200">
              sflow
            </span>
          )}
        </Link>
        {!isCollapsed && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 shrink-0"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}
      </div>
      {isCollapsed && (
        <div className="px-3 py-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-10 w-10"
            onClick={() => setIsCollapsed(false)}
          >
            <Menu className="h-4 w-4" />
          </Button>
        </div>
      )}
      <ScrollArea className="flex-1 px-3">
        <TooltipProvider delayDuration={0}>
          <div className="space-y-1 py-2">
            {routes.map((route) => (
              <Tooltip key={route.href}>
                <TooltipTrigger asChild>
                  <Link href={route.href}>
                    <Button
                      variant={pathname === route.href ? "secondary" : "ghost"}
                      className={cn(
                        "w-full justify-start",
                        isCollapsed && "h-10 w-10 p-0 justify-center"
                      )}
                    >
                      <route.icon className={cn("h-4 w-4", !isCollapsed && "mr-2")} />
                      {!isCollapsed && <span>{route.label}</span>}
                    </Button>
                  </Link>
                </TooltipTrigger>
                {isCollapsed && (
                  <TooltipContent side="right" className="flex items-center gap-4">
                    {route.label}
                  </TooltipContent>
                )}
              </Tooltip>
            ))}
          </div>
        </TooltipProvider>
      </ScrollArea>
      <div className="border-t p-3 space-y-3">
        {currentUser && (
          <div className={cn(
            "flex items-center gap-3",
            isCollapsed && "justify-center"
          )}>
            <Avatar className={cn(
              "h-9 w-9 shrink-0",
              isCollapsed && "h-10 w-10"
            )}>
              <AvatarImage src={currentUser.image || undefined} alt={currentUser.name || currentUser.email} />
              <AvatarFallback className="text-xs font-medium">
                {userInitials}
              </AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <div className="flex flex-col min-w-0">
                <p className="text-sm font-medium leading-none truncate">
                  {currentUser.name || 'User'}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {currentUser.email}
                </p>
              </div>
            )}
          </div>
        )}
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start text-destructive hover:text-destructive",
                  isCollapsed && "h-10 w-10 p-0 justify-center"
                )}
                onClick={handleLogout}
              >
                <LogOut className={cn("h-4 w-4", !isCollapsed && "mr-2")} />
                {!isCollapsed && <span>Logout</span>}
              </Button>
            </TooltipTrigger>
            {isCollapsed && (
              <TooltipContent side="right" className="flex items-center gap-4">
                Logout
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  )
}