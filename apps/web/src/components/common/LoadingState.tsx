import { Skeleton } from '@/components/ui/skeleton';

interface LoadingStateProps {
  title?: string;
  cardCount?: number;
}

export function LoadingState({ title = 'Loading', cardCount = 2 }: LoadingStateProps) {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-6xl mx-auto">
        <div className="animate-pulse">
          <Skeleton className="h-8 w-1/4 mb-8" />
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: cardCount }).map((_, i) => (
              <Skeleton key={i} className="h-32" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}