import { createAuthClient } from "better-auth/react";
import { emailOTPClient } from "better-auth/client/plugins";
import { env } from "@/config/env";

const baseURL = `${env.NEXT_PUBLIC_API_URL}/api/auth`;
console.log('Auth client baseURL:', baseURL);

export const authClient = createAuthClient({
  baseURL,
  plugins: [
    emailOTPClient()
  ],
  fetchOptions: {
    credentials: 'include'
  }
});

export const { signIn, signUp, signOut, useSession, emailOtp, getSession } = authClient;