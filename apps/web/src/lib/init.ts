import { validateEnv } from '@/config/env';

// Validate environment variables on app initialization
export function initializeApp() {
  try {
    validateEnv();
  } catch (error) {
    console.error('Environment validation failed:', error);
    // In development, log the error but continue
    // In production, you might want to throw the error
    if (process.env.NODE_ENV === 'production') {
      throw error;
    }
  }
}