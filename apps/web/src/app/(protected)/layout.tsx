"use client"

import { redirect } from 'next/navigation'
import { authClient } from '@/lib/auth-client'
import { Sidebar } from '@/components/dashboard/sidebar'
import { Header } from '@/components/dashboard/header'
import { MobileSidebar } from '@/components/dashboard/mobile-sidebar'
import { useState, useEffect } from 'react'
import { ProjectProvider } from '@/contexts/project-context'

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const session = await authClient.getSession()
        console.log('Session check result:', session)
        if (!session?.data?.user) {
          console.log('No session found, redirecting to login')
          redirect('/login')
        } else {
          console.log('Session found, user:', session.data.user)
          setIsAuthenticated(true)
        }
      } catch (error) {
        console.error('Session check error:', error)
        redirect('/login')
      } finally {
        setIsLoading(false)
      }
    }
    checkAuth()
  }, [])

  if (isLoading) {
    return <div className="flex h-screen items-center justify-center">Loading...</div>
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <ProjectProvider>
      <div className="flex h-screen overflow-hidden">
        <div className="hidden lg:flex">
          <Sidebar isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
        </div>
        <MobileSidebar isOpen={isMobileOpen} setIsOpen={setIsMobileOpen} />
        <div className="flex flex-1 flex-col overflow-hidden">
          <Header onMenuClick={() => setIsMobileOpen(true)} />
          <main className="flex-1 overflow-y-auto bg-background">
            {children}
          </main>
        </div>
      </div>
    </ProjectProvider>
  )
}