'use client';

import { BlockEditor } from '@/components/editor/block-editor';
import { useEditorStore } from '@/stores/editor.store';
import { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Loader2, Plus } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { agentService } from '@/services/agent.service';
import { InputDisplay } from '@/components/editor/input-display';
import { InputConfigDrawer } from '@/components/editor/input-config-drawer';
import type { BaseInputDefinition } from '@/types/input-types';
import { useTheme } from 'next-themes';
import { DeployButton } from '@/components/editor/deploy-button';

export default function EditorPage() {
  const { 
    content, 
    setContent, 
    isDirty, 
    agentTitle, 
    setAgentTitle, 
    saveStatus, 
    setSaveStatus,
    saveError,
    inputDefinitions,
    removeInputDefinition,
    updateInputDefinition,
    addInputDefinition,
    loadAgent,
    currentAgentId,
    resetContent
  } = useEditorStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');
  const [titleError, setTitleError] = useState<string>('');
  const [isInputDrawerOpen, setIsInputDrawerOpen] = useState(false);
  const [editingInput, setEditingInput] = useState<BaseInputDefinition | undefined>();
  const [isLoading, setIsLoading] = useState(true);
  const agentId = searchParams.get('agentId');
  const { resolvedTheme } = useTheme();

  const handleEditorChange = (content: any) => {
    setContent(content);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAgentTitle(e.target.value);
    if (titleError && e.target.value.trim()) {
      setTitleError('');
    }
  };

  // Load agent if agentId is provided, otherwise reset for new agent
  useEffect(() => {
    const loadAgentData = async () => {
      if (!projectId) {
        router.push('/projects');
        return;
      }

      if (agentId) {
        try {
          setIsLoading(true);
          const agent = await agentService.getAgent(agentId);
          const workflowJson = agent.workflowJson || {};
          
          loadAgent(
            agent.id,
            agent.title,
            workflowJson.blocks || [],
            workflowJson.inputDefinitions || []
          );
        } catch (error) {
          console.error('Failed to load agent:', error);
          setSaveStatus('error', 'Failed to load agent');
        } finally {
          setIsLoading(false);
        }
      } else {
        // Reset for new agent
        resetContent();
        setIsLoading(false);
      }
    };

    loadAgentData();
  }, [agentId, projectId, loadAgent, resetContent, router, setSaveStatus]);

  const handleSave = async () => {
    // Validate agent title
    if (!agentTitle.trim()) {
      setTitleError('Agent title is required');
      return;
    }

    if (!projectId) {
      setSaveStatus('error', 'Project ID is missing');
      return;
    }

    setSaveStatus('saving');
    
    try {
      let agent;
      
      const workflowJson = { 
        blocks: content,
        inputDefinitions: inputDefinitions 
      };

      if (currentAgentId) {
        // Update existing agent
        agent = await agentService.updateAgent(currentAgentId, {
          title: agentTitle,
          workflowJson,
        });
      } else {
        // Create new agent
        agent = await agentService.createAgent(projectId, {
          title: agentTitle,
          workflowJson,
          triggerType: 'web_app'
        });
        // Update URL to include agentId for future saves
        router.replace(`/editor?projectId=${projectId}&agentId=${agent.id}`);
      }
      
      setSaveStatus('saved');
      // Reset the dirty state after successful save
      useEditorStore.getState().saveContent();
      
      // Navigate back to agents list
      setTimeout(() => {
        router.push(`/projects/${projectId}/agents`);
      }, 1500);
    } catch (error) {
      setSaveStatus('error', error instanceof Error ? error.message : 'Failed to save agent');
    }
  };

  useEffect(() => {
    // Warn user about unsaved changes when leaving
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isDirty) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isDirty]);

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto flex items-center justify-center h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-gray-600">Loading agent...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Agent Editor</h1>
          <div className="flex items-center gap-2">
            <DeployButton 
              agentId={currentAgentId} 
              disabled={isDirty || !currentAgentId}
            />
            <Button 
              onClick={handleSave} 
              disabled={!isDirty || saveStatus === 'saving'}
              className="min-w-[100px]"
            >
              {saveStatus === 'saving' ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Agent'
              )}
            </Button>
          </div>
        </div>
        
        <div className="mb-6">
          <Label htmlFor="agent-title" className="text-base font-medium mb-2 block">
            Agent Title <span className="text-red-500">*</span>
          </Label>
          <Input
            id="agent-title"
            type="text"
            placeholder="Enter a descriptive title for your agent"
            value={agentTitle}
            onChange={handleTitleChange}
            className={titleError ? 'border-red-500' : ''}
          />
          {titleError && (
            <p className="text-sm text-red-500 mt-1">{titleError}</p>
          )}
          {saveError && (
            <p className="text-sm text-red-500 mt-1">{saveError}</p>
          )}
          {saveStatus === 'saved' && (
            <p className="text-sm text-green-600 mt-1">Agent saved successfully!</p>
          )}
        </div>

        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Create your agent by adding and arranging text blocks below. Use <code className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded">/input</code> to define input fields.
        </p>
        
        <div className="space-y-4">
          <InputDisplay
            inputs={inputDefinitions}
            onRemove={removeInputDefinition}
            onEdit={(input) => {
              setEditingInput(input);
              setIsInputDrawerOpen(true);
            }}
          />
          
          <Button
            onClick={() => {
              setEditingInput(undefined);
              setIsInputDrawerOpen(true);
            }}
            variant="outline"
            size="sm"
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Input Field
          </Button>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors">
          <BlockEditor 
            initialContent={content}
            onChange={handleEditorChange}
            onInputClick={(inputName) => {
              const input = inputDefinitions.find(i => i.name === inputName);
              if (input) {
                setEditingInput(input);
                setIsInputDrawerOpen(true);
              }
            }}
            projectId={projectId || undefined}
          />
        </div>
        
        <InputConfigDrawer
          open={isInputDrawerOpen}
          onOpenChange={(open) => {
            setIsInputDrawerOpen(open);
            if (!open) {
              setEditingInput(undefined);
            }
          }}
          onSave={(inputConfig) => {
            if (editingInput) {
              updateInputDefinition(editingInput.id, inputConfig);
            } else {
              addInputDefinition(inputConfig);
            }
            setIsInputDrawerOpen(false);
            setEditingInput(undefined);
          }}
          existingInput={editingInput}
        />
      </div>
    </div>
  );
}