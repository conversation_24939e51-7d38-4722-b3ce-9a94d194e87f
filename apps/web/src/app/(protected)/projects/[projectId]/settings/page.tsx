'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Settings, Users, BookOpen, UserPlus } from 'lucide-react';
import { projectService } from '@/services/project.service';
import { memberService } from '@/services/member.service';
import { useProjectStore } from '@/stores/project.store';
import { useMembersStore } from '@/stores/members.store';
import { useSession } from '@/lib/auth-client';
import type { Project } from '@/types/project';
import type { ProjectMember } from '@/types/member';
import { MembersList } from '@/components/members/MembersList';
import { AddMemberDialog } from '@/components/members/AddMemberDialog';
import { KnowledgeBaseList } from '@/components/knowledge-base/KnowledgeBaseList';

export default function ProjectSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.projectId as string;
  
  const { data: session } = useSession();
  const user = session?.user;
  const { currentProject, setCurrentProject } = useProjectStore();
  const { members, setMembers } = useMembersStore();
  const [project, setProject] = useState<Project | null>(currentProject);
  const [loading, setLoading] = useState(!currentProject || currentProject.id !== projectId);
  const [currentUserRole, setCurrentUserRole] = useState<'ADMIN' | 'MEMBER'>('MEMBER');
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);

  useEffect(() => {
    const fetchProject = async () => {
      if (currentProject && currentProject.id === projectId) {
        setProject(currentProject);
        setLoading(false);
        // Still need to fetch members even if we have the project
        try {
          const membersData = await memberService.getProjectMembers(projectId);
          setMembers(projectId, membersData);
          
          // Find current user's role
          if (membersData && membersData.length > 0 && user?.id) {
            const currentMember = membersData.find(m => m.userId === user.id);
            if (currentMember) {
              setCurrentUserRole(currentMember.role);
            }
          }
        } catch (err) {
          console.error('Failed to fetch members:', err);
        }
        return;
      }

      try {
        setLoading(true);
        const [projectData, membersData] = await Promise.all([
          projectService.getProjectById(projectId),
          memberService.getProjectMembers(projectId)
        ]);
        setProject(projectData);
        setCurrentProject(projectData);
        setMembers(projectId, membersData);
        
        // Find current user's role
        if (membersData && membersData.length > 0 && user?.id) {
          const currentMember = membersData.find(m => m.userId === user.id);
          if (currentMember) {
            setCurrentUserRole(currentMember.role);
          } else {
            // If user is the project owner but not in members list (shouldn't happen)
            if (projectData.userId === user.id) {
              setCurrentUserRole('ADMIN');
            }
          }
        }
      } catch (err) {
        console.error('Failed to fetch project:', err);
        router.push('/projects');
      } finally {
        setLoading(false);
      }
    };

    if (projectId) {
      fetchProject();
    }
  }, [projectId, currentProject, router, setCurrentProject, setMembers, user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!project) {
    return null;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Breadcrumb Navigation */}
      <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Dashboard
        </Link>
        <span>/</span>
        <Link href="/projects" className="hover:text-foreground transition-colors">
          Projects
        </Link>
        <span>/</span>
        <Link href={`/projects/${projectId}`} className="hover:text-foreground transition-colors">
          {project.name}
        </Link>
        <span>/</span>
        <span className="text-foreground">Settings</span>
      </nav>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/projects/${projectId}`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-semibold">Project Settings</h1>
            <p className="text-muted-foreground">{project.name}</p>
          </div>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general" className="gap-2">
            <Settings className="h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="members" className="gap-2">
            <Users className="h-4 w-4" />
            Members
          </TabsTrigger>
          <TabsTrigger value="knowledge" className="gap-2">
            <BookOpen className="h-4 w-4" />
            Knowledge Base
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">General Settings</h2>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Project Name</label>
                <p className="text-sm text-muted-foreground mt-1">{project.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Description</label>
                <p className="text-sm text-muted-foreground mt-1">
                  {project.description || 'No description provided'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium">Access Type</label>
                <p className="text-sm text-muted-foreground mt-1">
                  {project.accessType === 'PERSONAL' ? 'Personal' : 'Company-wide'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium">Created</label>
                <p className="text-sm text-muted-foreground mt-1">
                  {new Date(project.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="members">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold">Team Members</h2>
              {currentUserRole === 'ADMIN' && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="gap-2"
                  onClick={() => setAddMemberDialogOpen(true)}
                >
                  <UserPlus className="h-4 w-4" />
                  Add Member
                </Button>
              )}
            </div>
            <MembersList projectId={projectId} currentUserRole={currentUserRole} />
          </Card>
        </TabsContent>

        <TabsContent value="knowledge">
          <Card className="p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold">Knowledge Base</h2>
              <p className="text-sm text-muted-foreground mt-1">
                Select which documents from the central Knowledge Base are available to this project.
              </p>
            </div>
            <KnowledgeBaseList projectId={projectId} isAdmin={currentUserRole === 'ADMIN'} />
          </Card>
        </TabsContent>
      </Tabs>

      <AddMemberDialog
        projectId={projectId}
        open={addMemberDialogOpen}
        onOpenChange={setAddMemberDialogOpen}
        onMemberAdded={async () => {
          // Refresh members list
          try {
            const membersData = await memberService.getProjectMembers(projectId);
            setMembers(projectId, membersData);
          } catch (err) {
            console.error('Failed to refresh members:', err);
          }
        }}
        existingMemberIds={(members.get(projectId) || []).map(m => m.userId)}
      />
    </div>
  );
}