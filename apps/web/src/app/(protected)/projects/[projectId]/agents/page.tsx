'use client';

import { useEffect, useState, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Plus, FileText, Calendar, Edit2, Trash2 } from 'lucide-react';
import { agentService, type Agent } from '@/services/agent.service';
import { useProjectStore } from '@/stores/project.store';
import { LoadingState } from '@/components/common/LoadingState';
import { ErrorState } from '@/components/common/ErrorState';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export default function ProjectAgentsPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.projectId as string;
  const { projects } = useProjectStore();
  const project = projects.find(p => p.id === projectId);
  
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteAgentId, setDeleteAgentId] = useState<string | null>(null);

  const loadAgents = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const fetchedAgents = await agentService.getAgents(projectId);
      setAgents(fetchedAgents);
    } catch (err) {
      setError('Failed to load agents');
      console.error('Error loading agents:', err);
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    if (!projectId) {
      router.push('/projects');
      return;
    }

    loadAgents();
  }, [projectId, router, loadAgents]);

  const handleCreateAgent = () => {
    router.push(`/editor?projectId=${projectId}`);
  };

  const handleEditAgent = (agent: Agent) => {
    router.push(`/editor?projectId=${projectId}&agentId=${agent.id}`);
  };

  const handleDeleteAgent = async () => {
    if (!deleteAgentId) return;
    
    try {
      await agentService.deleteAgent(deleteAgentId);
      setAgents(agents.filter(a => a.id !== deleteAgentId));
      setDeleteAgentId(null);
    } catch (err) {
      console.error('Error deleting agent:', err);
    }
  };

  if (isLoading) {
    return <LoadingState title="Loading agents..." cardCount={3} />;
  }

  if (error) {
    return <ErrorState error={error} onRetry={loadAgents} />;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/projects')}
            className="mb-4"
          >
            ← Back to Projects
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">{project?.name || 'Project'} - Agents</h1>
              <p className="text-gray-600 mt-2">{project?.description}</p>
            </div>
            <Button 
              onClick={handleCreateAgent} 
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Create Agent
            </Button>
          </div>
        </div>

        {agents.length === 0 ? (
          <Card className="p-12 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No agents yet</h3>
            <p className="text-gray-600 mb-4">Create your first agent to get started</p>
            <Button onClick={handleCreateAgent} className="gap-2">
              <Plus className="h-4 w-4" />
              Create Agent
            </Button>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {agents.map((agent) => (
              <Card
                key={agent.id}
                className="p-6 hover:shadow-lg transition-all"
              >
                <div className="flex items-start justify-between mb-4">
                  <FileText className="h-8 w-8 text-primary" />
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleEditAgent(agent)}
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setDeleteAgentId(agent.id)}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </div>
                <h3 className="text-lg font-semibold mb-2">{agent.title}</h3>
                <div className="text-sm text-gray-500 mb-2">
                  {agent.workflowJson?.inputDefinitions?.length || 0} inputs configured
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  {new Date(agent.createdAt).toLocaleDateString()}
                </div>
              </Card>
            ))}
          </div>
        )}

        <Dialog open={!!deleteAgentId} onOpenChange={() => setDeleteAgentId(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Agent</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this agent? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteAgentId(null)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDeleteAgent}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}