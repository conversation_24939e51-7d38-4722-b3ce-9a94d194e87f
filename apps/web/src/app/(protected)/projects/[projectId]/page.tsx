'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Plus, Settings } from 'lucide-react';
import { AgentsList } from '@/components/agents/AgentsList';
import { projectService } from '@/services/project.service';
import { useProjectStore } from '@/stores/project.store';
import type { Project } from '@/types/project';

export default function ProjectDashboardPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.projectId as string;
  const { setCurrentProject } = useProjectStore();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Validate projectId format to prevent injection attacks
  useEffect(() => {
    if (projectId && !/^[a-zA-Z0-9-_]+$/.test(projectId)) {
      router.push('/projects');
    }
  }, [projectId, router]);

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true);
        setError(null);
        const projectData = await projectService.getProjectById(projectId);
        setProject(projectData);
        setCurrentProject(projectData);
      } catch (err: any) {
        console.error('Failed to fetch project:', err);
        
        if (err?.response?.status === 404) {
          router.push('/projects');
          return;
        }
        
        const errorMessage = err?.response?.data?.message || err?.message || 'Failed to load project. Please try again.';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (projectId && /^[a-zA-Z0-9-_]+$/.test(projectId)) {
      fetchProject();
    }
  }, [projectId, router, setCurrentProject]);

  // Handle invalid projectId format
  if (projectId && !/^[a-zA-Z0-9-_]+$/.test(projectId)) {
    return null; // Router will redirect
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-2">Error Loading Project</h2>
          <p className="text-muted-foreground mb-4">{error || 'Project not found'}</p>
          <Button onClick={() => router.push('/projects')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Breadcrumb Navigation */}
      <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Dashboard
        </Link>
        <span>/</span>
        <Link href="/projects" className="hover:text-foreground transition-colors">
          Projects
        </Link>
        <span>/</span>
        <span className="text-foreground">{project.name}</span>
      </nav>

      {/* Project Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-semibold">{project.name}</h1>
          {project.description && (
            <p className="text-muted-foreground">{project.description}</p>
          )}
          <div className="flex items-center gap-4 mt-2">
            <Badge variant={project.accessType === 'PERSONAL' ? 'default' : 'secondary'}>
              {project.accessType === 'PERSONAL' ? 'Personal' : 'Company-wide'}
            </Badge>
            <span className="text-sm text-muted-foreground">
              Last updated: {new Date(project.updatedAt).toLocaleDateString()}
            </span>
          </div>
        </div>
        <Link href={`/projects/${projectId}/settings`}>
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </Link>
      </div>

      {/* Agents Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Agents</h2>
          <Button onClick={() => router.push(`/editor?projectId=${projectId}`)}>
            <Plus className="mr-2 h-4 w-4" />
            Create New Agent
          </Button>
        </div>

        <AgentsList 
          projectId={projectId} 
          onAgentClick={(agent) => router.push(`/editor?projectId=${projectId}&agentId=${agent.id}`)}
        />
      </div>
    </div>
  );
}