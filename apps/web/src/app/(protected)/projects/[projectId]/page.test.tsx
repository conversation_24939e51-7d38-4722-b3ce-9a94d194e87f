import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useParams, useRouter } from 'next/navigation';
import ProjectDashboardPage from './page';
import { projectService } from '@/services/project.service';
import { useProjectStore } from '@/stores/project.store';

jest.mock('next/navigation', () => ({
  useParams: jest.fn(),
  useRouter: jest.fn(),
}));

jest.mock('@/services/project.service', () => ({
  projectService: {
    getProjectById: jest.fn(),
  },
}));

jest.mock('@/stores/project.store', () => ({
  useProjectStore: jest.fn(),
}));

jest.mock('@/components/agents/AgentsList', () => ({
  AgentsList: ({ projectId, onAgentClick }: any) => (
    <div data-testid="agents-list">
      Agents List for project {projectId}
    </div>
  ),
}));

describe('ProjectDashboardPage', () => {
  const mockRouter = {
    push: jest.fn(),
  };
  
  const mockSetCurrentProject = jest.fn();
  
  const mockProject = {
    id: 'project-123',
    name: 'Test Project',
    description: 'Test project description',
    accessType: 'PERSONAL',
    userId: 'user-123',
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-15'),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useParams as jest.Mock).mockReturnValue({ projectId: 'project-123' });
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useProjectStore as jest.Mock).mockReturnValue({
      setCurrentProject: mockSetCurrentProject,
    });
  });

  it('should render loading state initially', () => {
    render(<ProjectDashboardPage />);
    
    const loadingElement = screen.getByRole('status', { hidden: true });
    expect(loadingElement).toHaveClass('animate-spin');
  });

  it('should fetch and display project data', async () => {
    (projectService.getProjectById as jest.Mock).mockResolvedValue(mockProject);
    
    render(<ProjectDashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument();
      expect(screen.getByText('Test project description')).toBeInTheDocument();
      expect(screen.getByText('Personal')).toBeInTheDocument();
    });
    
    expect(mockSetCurrentProject).toHaveBeenCalledWith(mockProject);
  });

  it('should display breadcrumb navigation', async () => {
    (projectService.getProjectById as jest.Mock).mockResolvedValue(mockProject);
    
    render(<ProjectDashboardPage />);
    
    await waitFor(() => {
      const dashboardLink = screen.getByRole('link', { name: /dashboard/i });
      const projectsLink = screen.getByRole('link', { name: /projects/i });
      
      expect(dashboardLink).toHaveAttribute('href', '/dashboard');
      expect(projectsLink).toHaveAttribute('href', '/projects');
      expect(screen.getByText('Test Project')).toBeInTheDocument();
    });
  });

  it('should display settings button', async () => {
    (projectService.getProjectById as jest.Mock).mockResolvedValue(mockProject);
    
    render(<ProjectDashboardPage />);
    
    await waitFor(() => {
      const settingsLink = screen.getByRole('link', { name: /settings/i });
      expect(settingsLink).toHaveAttribute('href', '/projects/project-123/settings');
    });
  });

  it('should display create new agent button', async () => {
    (projectService.getProjectById as jest.Mock).mockResolvedValue(mockProject);
    
    render(<ProjectDashboardPage />);
    
    await waitFor(() => {
      const createButton = screen.getByRole('button', { name: /create new agent/i });
      expect(createButton).toBeInTheDocument();
    });
  });

  it('should render AgentsList component', async () => {
    (projectService.getProjectById as jest.Mock).mockResolvedValue(mockProject);
    
    render(<ProjectDashboardPage />);
    
    await waitFor(() => {
      const agentsList = screen.getByTestId('agents-list');
      expect(agentsList).toBeInTheDocument();
      expect(agentsList).toHaveTextContent('Agents List for project project-123');
    });
  });

  it('should handle project not found error', async () => {
    const error = { response: { status: 404 } };
    (projectService.getProjectById as jest.Mock).mockRejectedValue(error);
    
    render(<ProjectDashboardPage />);
    
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/projects');
    });
  });

  it('should display error message for other errors', async () => {
    const error = new Error('Network error');
    (projectService.getProjectById as jest.Mock).mockRejectedValue(error);
    
    render(<ProjectDashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Error Loading Project')).toBeInTheDocument();
      expect(screen.getByText('Failed to load project. Please try again.')).toBeInTheDocument();
    });
  });

  it('should display company-wide badge for company projects', async () => {
    const companyProject = { ...mockProject, accessType: 'COMPANY_WIDE' };
    (projectService.getProjectById as jest.Mock).mockResolvedValue(companyProject);
    
    render(<ProjectDashboardPage />);
    
    await waitFor(() => {
      expect(screen.getByText('Company-wide')).toBeInTheDocument();
    });
  });
});