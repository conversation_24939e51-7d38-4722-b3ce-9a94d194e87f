import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { act } from 'react'
import KnowledgeBasePage from './page'
import { documentService } from '@/services/document.service'
import { toast } from 'sonner'

// Mock dependencies
jest.mock('@/services/document.service')
jest.mock('sonner')

// Mock scrollIntoView for jsdom
window.HTMLElement.prototype.scrollIntoView = jest.fn()

// Mock the FileUpload component
jest.mock('@/components/knowledge-base/FileUpload', () => ({
  FileUpload: ({ onUploadSuccess }: { onUploadSuccess: () => void }) => (
    <div data-testid="file-upload">File Upload Component</div>
  )
}))

// Mock the DocumentListItem component
jest.mock('@/components/knowledge-base/DocumentListItem', () => ({
  DocumentListItem: ({ document, onDelete, isSelected, onSelectionChange, selectionMode }: any) => (
    <div data-testid={`document-${document.id}`}>
      {selectionMode && (
        <input
          type="checkbox"
          checked={isSelected}
          onChange={(e) => onSelectionChange(document.id, e.target.checked)}
          aria-label={`Select ${document.fileName}`}
        />
      )}
      <span>{document.fileName}</span>
      <span>{document.status}</span>
      <span>{document.fileType}</span>
      <button onClick={() => onDelete(document.id)}>Delete</button>
    </div>
  )
}))

describe('KnowledgeBasePage', () => {
  const mockDocuments = [
    {
      id: '1',
      fileName: 'test.pdf',
      fileType: 'application/pdf',
      status: 'COMPLETED',
      fileSize: 1024,
      uploadedAt: new Date().toISOString(),
      workspaceId: 'workspace-1',
      storedFileName: 'stored-1.pdf',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: '2',
      fileName: 'document.docx',
      fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      status: 'PROCESSING',
      fileSize: 2048,
      uploadedAt: new Date().toISOString(),
      workspaceId: 'workspace-1',
      storedFileName: 'stored-2.docx',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: '3',
      fileName: 'notes.txt',
      fileType: 'text/plain',
      status: 'FAILED',
      fileSize: 512,
      uploadedAt: new Date().toISOString(),
      workspaceId: 'workspace-1',
      storedFileName: 'stored-3.txt',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
    ;(documentService.getWorkspaceDocuments as jest.Mock).mockResolvedValue({
      documents: mockDocuments
    })
  })

  it('renders search and filter controls when documents exist', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search documents...')).toBeInTheDocument()
      expect(screen.getByText('All Status')).toBeInTheDocument()
      expect(screen.getByText('All Types')).toBeInTheDocument()
    })
  })

  it('filters documents by search query', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
      expect(screen.getByTestId('document-2')).toBeInTheDocument()
      expect(screen.getByTestId('document-3')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Search documents...')
    fireEvent.change(searchInput, { target: { value: 'test' } })

    await waitFor(() => {
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
      expect(screen.queryByTestId('document-2')).not.toBeInTheDocument()
      expect(screen.queryByTestId('document-3')).not.toBeInTheDocument()
    })
  })

  it('filters documents by status', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getAllByRole('button').length).toBeGreaterThan(0)
    })

    // Click on status filter
    const statusFilter = screen.getByText('All Status')
    fireEvent.click(statusFilter)

    // Select COMPLETED status
    const completedOption = await screen.findByText('Completed')
    fireEvent.click(completedOption)

    await waitFor(() => {
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
      expect(screen.queryByTestId('document-2')).not.toBeInTheDocument()
      expect(screen.queryByTestId('document-3')).not.toBeInTheDocument()
    })
  })

  it('filters documents by file type', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getAllByRole('button').length).toBeGreaterThan(0)
    })

    // Click on file type filter
    const fileTypeFilter = screen.getByText('All Types')
    fireEvent.click(fileTypeFilter)

    // Select PDF type
    const pdfOption = await screen.findByText('PDF')
    fireEvent.click(pdfOption)

    await waitFor(() => {
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
      expect(screen.queryByTestId('document-2')).not.toBeInTheDocument()
      expect(screen.queryByTestId('document-3')).not.toBeInTheDocument()
    })
  })

  it('shows filtered count when filters are applied', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search documents...')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Search documents...')
    fireEvent.change(searchInput, { target: { value: 'test' } })

    await waitFor(() => {
      expect(screen.getByText('Showing 1 of 3 documents')).toBeInTheDocument()
    })
  })

  it('shows no results message when no documents match filters', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search documents...')).toBeInTheDocument()
    })

    const searchInput = screen.getByPlaceholderText('Search documents...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })

    await waitFor(() => {
      expect(screen.getByText('No documents match your search criteria.')).toBeInTheDocument()
      expect(screen.getByText('Clear filters')).toBeInTheDocument()
    })
  })

  it('clears all filters when clear filters button is clicked', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search documents...')).toBeInTheDocument()
    })

    // Apply search filter
    const searchInput = screen.getByPlaceholderText('Search documents...')
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } })

    await waitFor(() => {
      expect(screen.getByText('Clear filters')).toBeInTheDocument()
    })

    // Click clear filters
    fireEvent.click(screen.getByText('Clear filters'))

    await waitFor(() => {
      expect(searchInput).toHaveValue('')
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
      expect(screen.getByTestId('document-2')).toBeInTheDocument()
      expect(screen.getByTestId('document-3')).toBeInTheDocument()
    })
  })

  it('does not show search controls when no documents exist', async () => {
    ;(documentService.getWorkspaceDocuments as jest.Mock).mockResolvedValue({
      documents: []
    })

    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByText('No documents uploaded yet. Start by uploading your first document above.')).toBeInTheDocument()
      expect(screen.queryByPlaceholderText('Search documents...')).not.toBeInTheDocument()
    })
  })

  it('shows batch operations controls', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByLabelText('Select all documents')).toBeInTheDocument()
      expect(screen.getByText('Select all')).toBeInTheDocument()
    })
  })

  it('allows selecting individual documents', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
    })

    const checkbox1 = screen.getByLabelText('Select test.pdf')
    fireEvent.click(checkbox1)

    await waitFor(() => {
      expect(screen.getByText('1 selected')).toBeInTheDocument()
      expect(screen.getByText('Delete Selected (1)')).toBeInTheDocument()
    })
  })

  it('allows selecting all documents', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByLabelText('Select all documents')).toBeInTheDocument()
    })

    const selectAllCheckbox = screen.getByLabelText('Select all documents')
    fireEvent.click(selectAllCheckbox)

    await waitFor(() => {
      expect(screen.getByText('3 selected')).toBeInTheDocument()
      expect(screen.getByText('Delete Selected (3)')).toBeInTheDocument()
    })
  })

  it('performs batch deletion successfully', async () => {
    ;(documentService.deleteDocument as jest.Mock).mockResolvedValue(undefined)

    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
    })

    // Select two documents
    const checkbox1 = screen.getByLabelText('Select test.pdf')
    const checkbox2 = screen.getByLabelText('Select document.docx')
    fireEvent.click(checkbox1)
    fireEvent.click(checkbox2)

    await waitFor(() => {
      expect(screen.getByText('Delete Selected (2)')).toBeInTheDocument()
    })

    // Click delete button
    fireEvent.click(screen.getByText('Delete Selected (2)'))

    await waitFor(() => {
      expect(documentService.deleteDocument).toHaveBeenCalledWith('1')
      expect(documentService.deleteDocument).toHaveBeenCalledWith('2')
      expect(toast.success).toHaveBeenCalledWith('Successfully deleted 2 documents')
    })
  })

  it('handles partial batch deletion failure', async () => {
    ;(documentService.deleteDocument as jest.Mock)
      .mockResolvedValueOnce(undefined) // First delete succeeds
      .mockRejectedValueOnce(new Error('Delete failed')) // Second delete fails

    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
    })

    // Select two documents
    const checkbox1 = screen.getByLabelText('Select test.pdf')
    const checkbox2 = screen.getByLabelText('Select document.docx')
    fireEvent.click(checkbox1)
    fireEvent.click(checkbox2)

    // Click delete button
    fireEvent.click(screen.getByText('Delete Selected (2)'))

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Successfully deleted 1 document')
      expect(toast.error).toHaveBeenCalledWith('Failed to delete 1 document')
    })
  })

  it('clears selection when filters change', async () => {
    render(<KnowledgeBasePage />)

    await waitFor(() => {
      expect(screen.getByTestId('document-1')).toBeInTheDocument()
    })

    // Select a document
    const checkbox1 = screen.getByLabelText('Select test.pdf')
    fireEvent.click(checkbox1)

    await waitFor(() => {
      expect(screen.getByText('1 selected')).toBeInTheDocument()
    })

    // Change search filter
    const searchInput = screen.getByPlaceholderText('Search documents...')
    fireEvent.change(searchInput, { target: { value: 'test' } })

    await waitFor(() => {
      expect(screen.getByText('Select all')).toBeInTheDocument()
      expect(screen.queryByText('1 selected')).not.toBeInTheDocument()
    })
  })
})