"use client"

import { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileUpload } from "@/components/knowledge-base/FileUpload"
import { DocumentListItem } from "@/components/knowledge-base/DocumentListItem"
import { documentService } from "@/services/document.service"
import { toast } from "sonner"
import { Loader2, FileX, Search, Trash2, Play } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import type { KnowledgeBaseDocument } from "@/types/document"

export default function KnowledgeBasePage() {
  const [documents, setDocuments] = useState<KnowledgeBaseDocument[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [fileTypeFilter, setFileTypeFilter] = useState<string>("all")
  const [selectedDocuments, setSelectedDocuments] = useState<Set<string>>(new Set())
  const [isDeleting, setIsDeleting] = useState(false)

  const fetchDocuments = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await documentService.getWorkspaceDocuments()
      setDocuments(response.documents)
    } catch (error) {
      console.error('Failed to fetch documents:', error)
      setError('Failed to load documents. Please try again.')
      toast.error('Failed to load documents')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDocuments()
  }, [])

  // Set up periodic refresh to check document status
  useEffect(() => {
    const interval = setInterval(() => {
      // Only refresh if there are documents being processed
      const hasProcessingDocs = documents.some(doc => 
        doc.status === 'PROCESSING'
      )
      
      if (hasProcessingDocs) {
        fetchDocuments()
      }
    }, 5000) // Check every 5 seconds
    
    return () => clearInterval(interval)
  }, [documents])

  const handleDelete = async (documentId: string) => {
    try {
      // Optimistic update
      setDocuments(prev => prev.filter(doc => doc.id !== documentId))
      
      await documentService.deleteDocument(documentId)
      toast.success('Document deleted successfully')
    } catch (error) {
      console.error('Failed to delete document:', error)
      toast.error('Failed to delete document')
      // Revert optimistic update on error
      fetchDocuments()
    }
  }

  const handleBatchDelete = async () => {
    if (selectedDocuments.size === 0) return
    
    setIsDeleting(true)
    const documentsToDelete = Array.from(selectedDocuments)
    let successCount = 0
    let failCount = 0
    
    try {
      // Optimistic update
      setDocuments(prev => prev.filter(doc => !selectedDocuments.has(doc.id)))
      
      // Delete documents in parallel
      const deletePromises = documentsToDelete.map(async (docId) => {
        try {
          await documentService.deleteDocument(docId)
          successCount++
        } catch (error) {
          console.error(`Failed to delete document ${docId}:`, error)
          failCount++
        }
      })
      
      await Promise.all(deletePromises)
      
      if (successCount > 0) {
        toast.success(`Successfully deleted ${successCount} document${successCount > 1 ? 's' : ''}`)
      }
      
      if (failCount > 0) {
        toast.error(`Failed to delete ${failCount} document${failCount > 1 ? 's' : ''}`)
        // Refresh to show correct state
        fetchDocuments()
      }
      
      // Clear selection
      setSelectedDocuments(new Set())
    } catch (error) {
      console.error('Failed to delete documents:', error)
      toast.error('Failed to delete documents')
      fetchDocuments()
    } finally {
      setIsDeleting(false)
    }
  }

  const handleSelectionChange = (documentId: string, isSelected: boolean) => {
    setSelectedDocuments(prev => {
      const newSet = new Set(prev)
      if (isSelected) {
        newSet.add(documentId)
      } else {
        newSet.delete(documentId)
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    if (selectedDocuments.size === filteredDocuments.length) {
      setSelectedDocuments(new Set())
    } else {
      setSelectedDocuments(new Set(filteredDocuments.map(doc => doc.id)))
    }
  }

  const handleUploadSuccess = () => {
    // Refresh the document list after successful upload
    fetchDocuments()
    toast.success('Documents uploaded and processing started automatically')
  }

  const handleProcessDocument = async (documentId: string) => {
    try {
      const updatedDoc = await documentService.processDocument(documentId)
      
      // Update the document in the list
      setDocuments(prev => prev.map(doc => 
        doc.id === documentId ? updatedDoc : doc
      ))
      
      toast.success('Document processing started')
    } catch (error) {
      console.error('Failed to process document:', error)
      toast.error('Failed to process document')
    }
  }

  // Filter documents based on search and filters
  const filteredDocuments = useMemo(() => {
    // Clear selections when filters change
    setSelectedDocuments(new Set())
    
    return documents.filter(doc => {
      // Search filter
      const matchesSearch = searchQuery === "" || 
        doc.fileName.toLowerCase().includes(searchQuery.toLowerCase())
      
      // Status filter
      const matchesStatus = statusFilter === "all" || doc.status === statusFilter
      
      // File type filter
      const matchesFileType = fileTypeFilter === "all" || 
        (doc.fileType && doc.fileType === fileTypeFilter)
      
      return matchesSearch && matchesStatus && matchesFileType
    })
  }, [documents, searchQuery, statusFilter, fileTypeFilter])

  // Get unique file types from documents
  const fileTypes = useMemo(() => {
    const types = new Set(documents
      .filter(doc => doc.fileType) // Filter out documents without fileType
      .map(doc => doc.fileType)
    )
    return Array.from(types)
  }, [documents])

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Knowledge Base</h1>
        <p className="text-muted-foreground">
          Upload and manage documents to create your personal knowledge repository for AI agents.
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Document Upload</CardTitle>
          <CardDescription>
            Upload PDF, TXT, or DOCX files to build your knowledge base
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FileUpload onUploadSuccess={handleUploadSuccess} />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Your Documents</CardTitle>
          <CardDescription>
            Manage your uploaded documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filter Controls */}
          {documents.length > 0 && (
            <div className="mb-6 space-y-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      type="text"
                      placeholder="Search documents..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                      <SelectItem value="PROCESSING">Processing</SelectItem>
                      <SelectItem value="COMPLETED">Completed</SelectItem>
                      <SelectItem value="FAILED">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={fileTypeFilter} onValueChange={setFileTypeFilter}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="File Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      {fileTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type === 'application/pdf' ? 'PDF' :
                           type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ? 'DOCX' :
                           type === 'text/plain' ? 'TXT' : 
                           type || 'Unknown'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              {/* Batch Operations */}
              {filteredDocuments.length > 0 && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={selectedDocuments.size === filteredDocuments.length && filteredDocuments.length > 0}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all documents"
                    />
                    <span className="text-sm text-muted-foreground">
                      {selectedDocuments.size > 0 
                        ? `${selectedDocuments.size} selected` 
                        : 'Select all'}
                    </span>
                  </div>
                  
                  <div className="flex gap-2">
                    {(() => {
                      const pendingCount = filteredDocuments.filter(d => d.status === 'PENDING').length;
                      return pendingCount > 0 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            const pendingDocs = filteredDocuments.filter(d => d.status === 'PENDING');
                            toast.info(`Processing ${pendingCount} document${pendingCount > 1 ? 's' : ''}...`);
                            
                            const processPromises = pendingDocs.map(doc => 
                              handleProcessDocument(doc.id).catch(err => {
                                console.error(`Failed to process ${doc.fileName}:`, err);
                              })
                            );
                            
                            await Promise.all(processPromises);
                          }}
                        >
                          <Play className="mr-2 h-4 w-4" />
                          Process All Pending ({pendingCount})
                        </Button>
                      );
                    })()}
                    
                    {selectedDocuments.size > 0 && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={handleBatchDelete}
                        disabled={isDeleting}
                      >
                        {isDeleting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Deleting...
                          </>
                        ) : (
                          <>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Selected ({selectedDocuments.size})
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              )}
              
              {filteredDocuments.length !== documents.length && (
                <p className="text-sm text-muted-foreground">
                  Showing {filteredDocuments.length} of {documents.length} documents
                </p>
              )}
            </div>
          )}
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-destructive">{error}</p>
              <button 
                onClick={fetchDocuments}
                className="mt-2 text-sm text-primary hover:underline"
              >
                Try again
              </button>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-12">
              <FileX className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                No documents uploaded yet. Start by uploading your first document above.
              </p>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                No documents match your search criteria.
              </p>
              <button 
                onClick={() => {
                  setSearchQuery("")
                  setStatusFilter("all")
                  setFileTypeFilter("all")
                }}
                className="mt-2 text-sm text-primary hover:underline"
              >
                Clear filters
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredDocuments.map((document) => (
                <DocumentListItem
                  key={document.id}
                  document={document}
                  onDelete={handleDelete}
                  onProcess={handleProcessDocument}
                  isSelected={selectedDocuments.has(document.id)}
                  onSelectionChange={handleSelectionChange}
                  selectionMode={true}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}