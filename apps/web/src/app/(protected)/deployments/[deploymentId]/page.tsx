'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Copy, 
  CheckCircle, 
  ExternalLink,
  FileText,
  Code,
  Settings
} from 'lucide-react';
import { deploymentService } from '@/services/deployment.service';
import { toast } from 'sonner';

interface DeploymentInfo {
  id: string;
  agentId: string;
  deploymentUrl: string;
  apiKey: string;
  status: string;
  createdAt: string;
}

interface InputField {
  name: string;
  type: string;
  description: string;
  required: boolean;
}

interface ApiDocumentation {
  title: string;
  description: string;
  version: string;
  endpoint: {
    url: string;
    method: string;
    headers: Record<string, string>;
  };
  inputs: InputField[];
  examples: {
    curl: string;
    javascript: string;
    python: string;
  };
}

export default function DeploymentDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const deploymentId = params.deploymentId as string;
  
  const [deployment, setDeployment] = useState<DeploymentInfo | null>(null);
  const [apiDocs, setApiDocs] = useState<ApiDocumentation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  useEffect(() => {
    const loadDeploymentDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const deploymentData = await deploymentService.getDeployment(deploymentId);
        setDeployment(deploymentData);
        
        const docsData = await deploymentService.getApiDocumentation(deploymentId);
        setApiDocs(docsData);
      } catch (err) {
        setError('Failed to load deployment details');
        console.error('Error loading deployment:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (deploymentId) {
      loadDeploymentDetails();
    }
  }, [deploymentId]);

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      toast.success(`${fieldName} copied to clipboard`);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      
      try {
        document.execCommand('copy');
        textArea.remove();
        setCopiedField(fieldName);
        toast.success(`${fieldName} copied to clipboard`);
        setTimeout(() => setCopiedField(null), 2000);
      } catch (err) {
        textArea.remove();
        toast.error('Failed to copy to clipboard');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !deployment || !apiDocs) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto">
          <Button 
            variant="ghost" 
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Card className="p-8 text-center">
            <h2 className="text-xl font-semibold mb-2">Error Loading Deployment</h2>
            <p className="text-gray-600">{error || 'Deployment not found'}</p>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              onClick={() => router.back()}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Deployments</h1>
              <p className="text-gray-600">
                <FileText className="inline mr-1 h-4 w-4" />
                {apiDocs.title}
              </p>
            </div>
          </div>
          <Badge variant={deployment.status === 'ACTIVE' ? 'default' : 'secondary'}>
            {deployment.status}
          </Badge>
        </div>

        {/* API Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - API Details */}
          <div className="space-y-6">
            <Card className="p-6">
              <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Code className="h-5 w-5" />
                API
              </h2>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="api-url">URL</Label>
                  <div className="flex gap-2 mt-1">
                    <Input
                      id="api-url"
                      value={apiDocs.endpoint.url}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(apiDocs.endpoint.url, 'API URL')}
                    >
                      {copiedField === 'API URL' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div>
                  <Label>Request Body</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    You have to pass two parameters to the request body: <span className="font-mono bg-gray-100 px-1 rounded">inputs</span> and <span className="font-mono bg-gray-100 px-1 rounded">version</span>.
                  </p>
                </div>

                <div>
                  <Label>Inputs</Label>
                  <p className="text-sm text-gray-600 mt-1">These are the inputs for this WordApp:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    {apiDocs.inputs.map((input) => (
                      <li key={input.name} className="text-sm">
                        <span className="font-mono font-medium">{input.name}</span>
                        <span className="text-gray-500"> ({input.type})</span>
                        {input.required && <span className="text-red-500">*</span>}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <Label>Version</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    We use a simplified semantic versioning. All versions follow the format <span className="font-mono bg-gray-100 px-1 rounded">&lt;major&gt;.&lt;minor&gt;</span>, for example <span className="font-mono bg-gray-100 px-1 rounded">1.0</span>.
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    You have to pass the version number you want to use. You can use the caret syntax like this to get the latest non-breaking version: <span className="font-mono bg-gray-100 px-1 rounded">^1.0</span>. We recommend you use this syntax when you build your APIs. Example:
                  </p>
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                    <code className="text-sm">{"version": "^1.0"}</code>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Right Column - Testing */}
          <div className="space-y-6">
            <Card className="p-6">
              <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Testing
              </h2>
              
              <div className="space-y-4">
                <div>
                  <Label>Setup</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    To get started, export your API key in your terminal. You can <button className="text-blue-600 hover:underline">generate a key</button> directly or in the <button className="text-blue-600 hover:underline">api key settings</button>.
                  </p>
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg flex items-center justify-between">
                    <code className="text-sm">$ export WORDWARE_API_KEY=your_api_key</code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard('export WORDWARE_API_KEY=your_api_key', 'Export command')}
                    >
                      {copiedField === 'Export command' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div>
                  <Label>cURL</Label>
                  <p className="text-sm text-gray-600 mt-1">Use this cURL command to send your request:</p>
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-start justify-between">
                      <pre className="text-sm overflow-x-auto flex-1">
                        <code>{apiDocs.examples.curl}</code>
                      </pre>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(apiDocs.examples.curl, 'cURL example')}
                        className="ml-2 flex-shrink-0"
                      >
                        {copiedField === 'cURL example' ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Complete Body Example */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Complete Body</h3>
          <p className="text-sm text-gray-600 mb-4">The whole request body looks like this:</p>
          
          <Tabs defaultValue="curl" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="curl">cURL</TabsTrigger>
              <TabsTrigger value="javascript">JavaScript</TabsTrigger>
              <TabsTrigger value="python">Python</TabsTrigger>
            </TabsList>
            
            <TabsContent value="curl" className="space-y-2">
              <div className="flex justify-between items-center">
                <Label>Example Request</Label>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => copyToClipboard(apiDocs.examples.curl, 'cURL example')}
                >
                  {copiedField === 'cURL example' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
                <code className="text-sm">{apiDocs.examples.curl}</code>
              </pre>
            </TabsContent>
            
            <TabsContent value="javascript" className="space-y-2">
              <div className="flex justify-between items-center">
                <Label>Example Request</Label>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => copyToClipboard(apiDocs.examples.javascript, 'JavaScript example')}
                >
                  {copiedField === 'JavaScript example' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
                <code className="text-sm">{apiDocs.examples.javascript}</code>
              </pre>
            </TabsContent>
            
            <TabsContent value="python" className="space-y-2">
              <div className="flex justify-between items-center">
                <Label>Example Request</Label>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => copyToClipboard(apiDocs.examples.python, 'Python example')}
                >
                  {copiedField === 'Python example' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto">
                <code className="text-sm">{apiDocs.examples.python}</code>
              </pre>
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </div>
  );
}
