import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL ? `${process.env.NEXT_PUBLIC_API_URL}/api` : 'http://localhost:3001/api';

interface DeploymentResponse {
  id: string;
  agentId: string;
  deploymentUrl: string;
  apiKey: string;
  status: string;
  createdAt: string;
}

interface ApiDocumentation {
  title: string;
  description: string;
  version: string;
  endpoint: {
    url: string;
    method: string;
    headers: Record<string, string>;
  };
  inputs: Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
  }>;
  examples: {
    curl: string;
    javascript: string;
    python: string;
  };
}

class DeploymentService {
  async deployAgent(agentId: string): Promise<DeploymentResponse> {
    const response = await axios.post(
      `${API_URL}/agents/${agentId}/deploy`,
      {},
      {
        withCredentials: true,
      }
    );
    return response.data;
  }

  async getDeployments(agentId: string): Promise<DeploymentResponse[]> {
    const response = await axios.get(
      `${API_URL}/agents/${agentId}/deployments`,
      {
        withCredentials: true,
      }
    );
    return response.data.deployments;
  }

  async getDeployment(deploymentId: string): Promise<DeploymentResponse> {
    const response = await axios.get(
      `${API_URL}/deployments/${deploymentId}`,
      {
        withCredentials: true,
      }
    );
    return response.data;
  }

  async updateDeploymentStatus(deploymentId: string, status: 'ACTIVE' | 'INACTIVE'): Promise<DeploymentResponse> {
    const response = await axios.patch(
      `${API_URL}/deployments/${deploymentId}`,
      { status },
      {
        withCredentials: true,
      }
    );
    return response.data;
  }

  async deleteDeployment(deploymentId: string): Promise<void> {
    await axios.delete(
      `${API_URL}/deployments/${deploymentId}`,
      {
        withCredentials: true,
      }
    );
  }

  async getApiDocumentation(deploymentId: string): Promise<ApiDocumentation> {
    const deployment = await this.getDeployment(deploymentId);
    const response = await axios.get(`${deployment.deploymentUrl}`);
    return response.data;
  }
}

export const deploymentService = new DeploymentService();