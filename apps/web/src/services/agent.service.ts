import api from '@/lib/api';
import { Agent, CreateAgentInput } from '@/types/agent';

class AgentService {
  async getAgents(projectId: string): Promise<Agent[]> {
    const response = await api.get<Agent[]>(`/api/agents`, {
      params: { projectId }
    });
    return response.data;
  }

  async getAgentsByProjectId(projectId: string): Promise<Agent[]> {
    // Alias for backward compatibility
    return this.getAgents(projectId);
  }

  async createAgent(projectId: string, data: Omit<CreateAgentInput, 'projectId'>): Promise<Agent> {
    const response = await api.post<Agent>('/api/agents', {
      ...data,
      projectId
    });
    return response.data;
  }

  async getAgent(id: string): Promise<Agent> {
    const response = await api.get<Agent>(`/api/agents/${id}`);
    return response.data;
  }

  async updateAgent(id: string, data: Partial<CreateAgentInput>): Promise<Agent> {
    const response = await api.put<Agent>(`/api/agents/${id}`, data);
    return response.data;
  }

  async deleteAgent(id: string): Promise<void> {
    await api.delete(`/api/agents/${id}`);
  }
}

export const agentService = new AgentService();

// Re-export type for backward compatibility
export type { Agent } from '@/types/agent';