import api from '@/lib/api';
import type { ProjectMember, AddMemberRequest, UpdateMemberRoleRequest } from '@/types/member';

export const memberService = {
  async getProjectMembers(projectId: string): Promise<ProjectMember[]> {
    const response = await api.get(`/api/projects/${projectId}/members`);
    return response.data;
  },

  async addMember(projectId: string, data: AddMemberRequest): Promise<ProjectMember> {
    const response = await api.post(`/api/projects/${projectId}/members`, data);
    return response.data;
  },

  async updateMemberRole(projectId: string, userId: string, data: UpdateMemberRoleRequest): Promise<ProjectMember> {
    const response = await api.put(`/api/projects/${projectId}/members/${userId}`, data);
    return response.data;
  },

  async removeMember(projectId: string, userId: string): Promise<void> {
    await api.delete(`/api/projects/${projectId}/members/${userId}`);
  },
};