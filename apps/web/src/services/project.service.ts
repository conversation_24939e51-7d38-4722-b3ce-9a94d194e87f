import api from '@/lib/api';
import { Project, CreateProjectInput } from '@/types/project';
import { KnowledgeBaseDocument } from '@/types/document';

// Re-export for backward compatibility
export type { Project, CreateProjectInput as CreateProjectDto } from '@/types/project';

class ProjectService {
  async createProject(data: CreateProjectInput): Promise<Project> {
    const response = await api.post<Project>('/api/projects', data);
    return response.data;
  }

  async getProjects(): Promise<Project[]> {
    const response = await api.get<Project[]>('/api/projects');
    return response.data;
  }

  async getProject(id: string): Promise<Project> {
    const response = await api.get<Project>(`/api/projects/${id}`);
    return response.data;
  }

  async getProjectById(id: string): Promise<Project> {
    return this.getProject(id);
  }

  async updateProject(id: string, data: Partial<CreateProjectInput>): Promise<Project> {
    const response = await api.put<Project>(`/api/projects/${id}`, data);
    return response.data;
  }

  async deleteProject(id: string): Promise<void> {
    await api.delete(`/api/projects/${id}`);
  }

  async getProjectDocuments(projectId: string): Promise<KnowledgeBaseDocument[]> {
    const response = await api.get<KnowledgeBaseDocument[]>(`/api/projects/${projectId}/documents`);
    return response.data;
  }
}

export const projectService = new ProjectService();