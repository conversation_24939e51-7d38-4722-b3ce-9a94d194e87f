import api from '@/lib/api';
import { z } from 'zod';

export const WorkflowExecutionResultSchema = z.object({
  success: z.boolean(),
  outputs: z.record(z.any()),
  errors: z.array(z.string()).optional(),
});

export type WorkflowExecutionResult = z.infer<typeof WorkflowExecutionResultSchema>;

export interface ExecuteWorkflowParams {
  agentId: string;
  inputs?: Record<string, any>;
}

class AgentExecutionService {
  async executeWorkflow({ agentId, inputs = {} }: ExecuteWorkflowParams): Promise<WorkflowExecutionResult> {
    const response = await api.post(`/agents/${agentId}/execute`, { inputs });
    return WorkflowExecutionResultSchema.parse(response.data);
  }
}

export const agentExecutionService = new AgentExecutionService();