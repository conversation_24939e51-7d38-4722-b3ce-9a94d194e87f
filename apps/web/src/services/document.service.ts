import api from '@/lib/api';
import type { AxiosProgressEvent } from 'axios';
import type {
  KnowledgeBaseDocument,
  DocumentWithAssociation,
  UpdateProjectDocumentsRequest,
  DocumentsResponse,
  AvailableDocumentsResponse
} from '@/types/document';

export interface UploadProgress {
  fileId: string;
  progress: number;
}

export interface DocumentUploadOptions {
  onProgress?: (fileId: string, progress: number) => void;
}

class DocumentService {

  async uploadDocuments(
    files: File[],
    options?: DocumentUploadOptions
  ): Promise<KnowledgeBaseDocument[]> {
    console.log('DocumentService.uploadDocuments called with:', files.length, 'files');
    
    const formData = new FormData();
    
    files.forEach((file) => {
      formData.append('files', file);
    });

    try {
      const response = await api.post<{ documents: KnowledgeBaseDocument[] }>(
        '/api/documents/upload',
        formData,
        {
          // Let axios set the Content-Type with boundary automatically
          timeout: 60000, // 60 second timeout for file uploads
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            if (options?.onProgress && progressEvent.total) {
              const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100);
              // For multiple files, this shows overall progress
              // Individual file progress would require server-side support
              files.forEach((file, index) => {
                options.onProgress?.(file.name, progress);
              });
            }
          }
        }
      );

      console.log('Upload response:', response.data);
      
      if (!response.data || !response.data.documents) {
        console.error('Invalid response structure:', response.data);
        throw new Error('Invalid response from server');
      }

      return response.data.documents;
    } catch (error: any) {
      console.error('Upload error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        headers: error.response?.headers
      });
      
      // Re-throw with more context
      if (error.response) {
        throw new Error(error.response.data?.error || `Server error: ${error.response.status}`);
      } else if (error.request) {
        throw new Error('No response received from server. Please check your connection.');
      } else {
        throw error;
      }
    }
  }

  async getProjectDocuments(projectId: string, limit = 50, offset = 0): Promise<DocumentsResponse> {
    const response = await api.get<DocumentsResponse>(`/api/projects/${projectId}/documents`, {
      params: { limit, offset }
    });
    return response.data;
  }

  async getAvailableDocuments(projectId: string, limit = 50, offset = 0, search?: string): Promise<AvailableDocumentsResponse> {
    const response = await api.get<AvailableDocumentsResponse>(`/api/projects/${projectId}/available-documents`, {
      params: { limit, offset, ...(search && { search }) }
    });
    return response.data;
  }

  async updateProjectDocuments(projectId: string, documentIds: string[]): Promise<KnowledgeBaseDocument[]> {
    const response = await api.put<DocumentsResponse>(
      `/api/projects/${projectId}/documents`,
      { documentIds } as UpdateProjectDocumentsRequest
    );
    return response.data.documents;
  }

  async getWorkspaceDocuments(limit = 50, offset = 0): Promise<DocumentsResponse> {
    const response = await api.get<DocumentsResponse>('/api/documents', {
      params: { limit, offset }
    });
    return response.data;
  }

  async deleteDocument(documentId: string): Promise<void> {
    await api.delete(`/api/documents/${documentId}`);
  }

  async processDocument(documentId: string): Promise<KnowledgeBaseDocument> {
    const response = await api.post<{ document: KnowledgeBaseDocument }>(
      `/api/documents/${documentId}/process`
    );
    return response.data.document;
  }
}

export const documentService = new DocumentService();