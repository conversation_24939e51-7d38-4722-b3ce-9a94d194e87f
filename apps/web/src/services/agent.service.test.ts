import api from '@/lib/api';
import { agentService } from './agent.service';

jest.mock('@/lib/api');

describe('AgentService', () => {
  const mockApi = api as jest.Mocked<typeof api>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAgentsByProjectId', () => {
    it('should fetch agents for a project', async () => {
      const mockAgents = [
        { id: 'agent-1', title: 'Agent 1', projectId: 'project-123' },
        { id: 'agent-2', title: 'Agent 2', projectId: 'project-123' },
      ];
      
      mockApi.get.mockResolvedValue({ data: mockAgents });
      
      const result = await agentService.getAgentsByProjectId('project-123');
      
      expect(mockApi.get).toHaveBeenCalledWith('/api/agents', { params: { projectId: 'project-123' } });
      expect(result).toEqual(mockAgents);
    });

    it('should handle errors when fetching agents', async () => {
      const error = new Error('Network error');
      mockApi.get.mockRejectedValue(error);
      
      await expect(agentService.getAgentsByProjectId('project-123')).rejects.toThrow('Network error');
    });
  });

  describe('createAgent', () => {
    it('should create an agent', async () => {
      const newAgent = {
        title: 'New Agent',
        workflowJson: {},
        triggerType: 'click',
      };
      
      const createdAgent = {
        id: 'agent-123',
        ...newAgent,
        projectId: 'project-123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      mockApi.post.mockResolvedValue({ data: createdAgent });
      
      const result = await agentService.createAgent('project-123', newAgent);
      
      expect(mockApi.post).toHaveBeenCalledWith('/api/agents', { ...newAgent, projectId: 'project-123' });
      expect(result).toEqual(createdAgent);
    });
  });

  describe('getAgent', () => {
    it('should fetch a single agent', async () => {
      const mockAgent = {
        id: 'agent-123',
        title: 'Test Agent',
        projectId: 'project-123',
      };
      
      mockApi.get.mockResolvedValue({ data: mockAgent });
      
      const result = await agentService.getAgent('agent-123');
      
      expect(mockApi.get).toHaveBeenCalledWith('/api/agents/agent-123');
      expect(result).toEqual(mockAgent);
    });
  });

  describe('updateAgent', () => {
    it('should update an agent', async () => {
      const updateData = {
        title: 'Updated Agent',
      };
      
      const updatedAgent = {
        id: 'agent-123',
        title: 'Updated Agent',
        projectId: 'project-123',
      };
      
      mockApi.put.mockResolvedValue({ data: updatedAgent });
      
      const result = await agentService.updateAgent('agent-123', updateData);
      
      expect(mockApi.put).toHaveBeenCalledWith('/api/agents/agent-123', updateData);
      expect(result).toEqual(updatedAgent);
    });
  });

  describe('deleteAgent', () => {
    it('should delete an agent', async () => {
      mockApi.delete.mockResolvedValue({});
      
      await agentService.deleteAgent('agent-123');
      
      expect(mockApi.delete).toHaveBeenCalledWith('/api/agents/agent-123');
    });

    it('should handle errors when deleting agent', async () => {
      const error = new Error('Delete failed');
      mockApi.delete.mockRejectedValue(error);
      
      await expect(agentService.deleteAgent('agent-123')).rejects.toThrow('Delete failed');
    });
  });
});