import { useState, useCallback } from 'react';
import { BlockNoteEditor } from '@blocknote/core';
import { agentExecutionService } from '@/services/agent-execution.service';
import { toast } from 'sonner';

interface UseAgentExecutionOptions {
  agentId: string;
  editor: BlockNoteEditor | null;
  onComplete?: (result: any) => void;
}

export function useAgentExecution({ agentId, editor, onComplete }: UseAgentExecutionOptions) {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<any>(null);

  const updateGenerationBlock = useCallback(
    (blockId: string, updates: Record<string, any>) => {
      if (!editor) return;

      const block = editor.getBlock(blockId);
      if (block && block.type === 'generation') {
        editor.updateBlock(blockId, {
          type: 'generation',
          props: {
            ...block.props,
            ...updates,
          },
        });
      }
    },
    [editor]
  );

  const executeAgent = useCallback(
    async (inputs?: Record<string, any>) => {
      if (!editor || isExecuting) return;

      setIsExecuting(true);
      setExecutionResult(null);

      try {
        // Find all generation blocks and mark them as generating
        const document = editor.document;
        const generationBlocks: string[] = [];

        const findGenerationBlocks = (blocks: any[]) => {
          for (const block of blocks) {
            if (block.type === 'generation') {
              generationBlocks.push(block.id);
              updateGenerationBlock(block.id, {
                isGenerating: true,
                output: '',
                error: '',
              });
            }
            if (block.children) {
              findGenerationBlocks(block.children);
            }
          }
        };

        findGenerationBlocks(document);

        // Execute the workflow
        const result = await agentExecutionService.executeWorkflow({
          agentId,
          inputs,
        });

        setExecutionResult(result);

        // Update generation blocks with results
        if (result.success) {
          for (const blockId of generationBlocks) {
            const blockOutput = result.outputs[blockId];
            if (blockOutput) {
              updateGenerationBlock(blockId, {
                isGenerating: false,
                output: blockOutput.content || '',
                error: '',
              });
            } else {
              updateGenerationBlock(blockId, {
                isGenerating: false,
                error: 'No output generated for this block',
              });
            }
          }
          toast.success('Agent executed successfully');
        } else {
          // Handle errors
          for (const blockId of generationBlocks) {
            const blockError = result.errors?.find((e) => e.includes(blockId));
            updateGenerationBlock(blockId, {
              isGenerating: false,
              error: blockError || 'Execution failed',
            });
          }
          toast.error('Agent execution failed');
        }

        onComplete?.(result);
      } catch (error) {
        console.error('Agent execution error:', error);
        toast.error('Failed to execute agent');

        // Reset all generation blocks on error
        const document = editor.document;
        const resetGenerationBlocks = (blocks: any[]) => {
          for (const block of blocks) {
            if (block.type === 'generation') {
              updateGenerationBlock(block.id, {
                isGenerating: false,
                error: 'Execution failed',
              });
            }
            if (block.children) {
              resetGenerationBlocks(block.children);
            }
          }
        };
        resetGenerationBlocks(document);
      } finally {
        setIsExecuting(false);
      }
    },
    [agentId, editor, isExecuting, updateGenerationBlock, onComplete]
  );

  const regenerateBlock = useCallback(
    async (blockId: string) => {
      if (!editor || isExecuting) return;

      // For now, regenerating a single block will re-execute the entire workflow
      // In the future, we could implement partial execution
      await executeAgent();
    },
    [editor, isExecuting, executeAgent]
  );

  return {
    isExecuting,
    executionResult,
    executeAgent,
    regenerateBlock,
  };
}