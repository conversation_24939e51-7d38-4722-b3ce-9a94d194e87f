import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  
  // Skip middleware for auth routes, static files, and api routes
  if (
    pathname.startsWith('/api') ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/login') ||
    pathname.startsWith('/favicon') ||
    pathname.startsWith('/test-')  // Allow test pages without auth
  ) {
    return NextResponse.next()
  }

  try {
    // Debug: log all cookies
    console.log('All cookies:', request.cookies.getAll())
    
    // Get session cookie - check both possible names
    const sessionCookie = request.cookies.get('better-auth.session_token') || 
                         request.cookies.get('better-auth.session-token')
    
    if (!sessionCookie?.value) {
      // No session, redirect to login
      console.log('No session cookie found, redirecting to login')
      if (pathname !== '/login') {
        return NextResponse.redirect(new URL('/login', request.url))
      }
      return NextResponse.next()
    }

    // For authenticated users, allow access to onboarding page
    if (pathname === '/onboarding') {
      return NextResponse.next()
    }

    
    return NextResponse.next()
  } catch (error) {
    console.error('Middleware error:', error)
    return NextResponse.next()
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}