{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@blocknote/core": "^0.34.0", "@blocknote/mantine": "^0.34.0", "@blocknote/react": "^0.34.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.10.0", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.263.1", "next": "^15.4.2", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-phone-number-input": "^3.4.12", "react-select": "^5.10.2", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "zod": "^4.0.5", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@types/jest": "^29.5.14", "@types/node": "^20.19.9", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "eslint": "^9.31.0", "eslint-config-next": "^15.4.2", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.4", "postcss-import": "^16.1.1", "tailwindcss": "^4.1.11", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}}