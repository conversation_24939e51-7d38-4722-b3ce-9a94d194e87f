/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ['@sflow/types', '@sflow/config'],
  reactStrictMode: false, // BlockNote.js recommends disabling StrictMode for React 19 / Next.js 15
  webpack: (config, { isServer }) => {
    // Fix for "Yjs was already imported" error
    // This ensures Yjs is only loaded once
    if (!isServer) {
      config.resolve.alias = {
        ...config.resolve.alias,
        yjs: require.resolve('yjs'),
      };
    }
    
    return config;
  },
}

module.exports = nextConfig