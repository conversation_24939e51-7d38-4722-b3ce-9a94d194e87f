import { test, expect } from '@playwright/test';

test.describe('Generation Block', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication by setting session cookie
    await page.context().addCookies([
      {
        name: 'better-auth.session_token',
        value: 'test-session-token',
        domain: 'localhost',
        path: '/',
      },
    ]);

    // Navigate to agent editor page
    await page.goto('/projects/test-project/agents/test-agent/edit');
  });

  test('should add generation block via slash command', async ({ page }) => {
    // Click in the editor to focus it
    await page.locator('.bn-editor').click();

    // Type slash command
    await page.keyboard.type('/generation');

    // Wait for slash menu to appear
    await page.waitForSelector('[role="menu"]');

    // Click on Generation option
    await page.locator('[role="menuitem"]:has-text("Generation")').click();

    // Verify generation block is added
    await expect(page.locator('.generation-block')).toBeVisible();
    await expect(page.locator('.generation-block:has-text("Generation Block")')).toBeVisible();
  });

  test('should open configuration modal when Configure button is clicked', async ({ page }) => {
    // Add a generation block first
    await page.locator('.bn-editor').click();
    await page.keyboard.type('/generation');
    await page.waitForSelector('[role="menu"]');
    await page.locator('[role="menuitem"]:has-text("Generation")').click();

    // Click Configure button
    await page.locator('.generation-block button:has-text("Configure")').click();

    // Verify modal opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('[role="dialog"] h2:has-text("Configure Generation Block")')).toBeVisible();

    // Verify form elements
    await expect(page.locator('[role="dialog"] label:has-text("LLM Provider")')).toBeVisible();
    await expect(page.locator('[role="dialog"] label:has-text("Model")')).toBeVisible();
    await expect(page.locator('[role="dialog"] label:has-text("Temperature")')).toBeVisible();
  });

  test('should save generation block configuration', async ({ page }) => {
    // Add generation block
    await page.locator('.bn-editor').click();
    await page.keyboard.type('/generation');
    await page.waitForSelector('[role="menu"]');
    await page.locator('[role="menuitem"]:has-text("Generation")').click();

    // Open configuration
    await page.locator('.generation-block button:has-text("Configure")').click();

    // Select a different model
    await page.locator('[role="combobox"]').nth(1).click(); // Model dropdown
    await page.locator('[role="option"]:has-text("GPT-4")').click();

    // Adjust temperature
    const slider = page.locator('[role="slider"]');
    const sliderBox = await slider.boundingBox();
    if (sliderBox) {
      // Click at 30% position for 0.3 temperature
      await page.mouse.click(
        sliderBox.x + sliderBox.width * 0.3,
        sliderBox.y + sliderBox.height / 2
      );
    }

    // Save configuration
    await page.locator('[role="dialog"] button:has-text("Save")').click();

    // Verify modal closes
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();

    // Verify configuration is displayed in block
    await expect(page.locator('.generation-block:has-text("Model: gpt-4")')).toBeVisible();
    await expect(page.locator('.generation-block:has-text("Temp: 0.3")')).toBeVisible();
  });

  test('should add content with @KnowledgeBase reference', async ({ page }) => {
    // Add generation block
    await page.locator('.bn-editor').click();
    await page.keyboard.type('/generation');
    await page.waitForSelector('[role="menu"]');
    await page.locator('[role="menuitem"]:has-text("Generation")').click();

    // Click inside the generation block content area
    await page.locator('.generation-block .inline-content').click();

    // Type content with @ mention
    await page.keyboard.type('Summarize the content from @');

    // Wait for @ mention menu
    await page.waitForSelector('[role="menu"]');

    // Select Knowledge Base
    await page.locator('[role="menuitem"]:has-text("Knowledge Base")').click();

    // Mock knowledge base search dialog
    await expect(page.locator('[role="dialog"]:has-text("Search Knowledge Base")')).toBeVisible();

    // Select a document (would be populated in real scenario)
    await page.locator('[role="dialog"] [role="option"]:first-child').click();

    // Verify the reference is added
    await expect(page.locator('.generation-block:has-text("@KnowledgeBase:")')).toBeVisible();
  });

  test('should display generation output after execution', async ({ page }) => {
    // Mock API response for agent execution
    await page.route('**/api/agents/*/execute', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          outputs: {
            'block-1': {
              content: 'This is the generated response from the AI model.',
              model: 'openai/gpt-3.5-turbo',
              usage: {
                prompt_tokens: 20,
                completion_tokens: 15,
                total_tokens: 35,
              },
            },
          },
        }),
      });
    });

    // Add generation block with content
    await page.locator('.bn-editor').click();
    await page.keyboard.type('/generation');
    await page.waitForSelector('[role="menu"]');
    await page.locator('[role="menuitem"]:has-text("Generation")').click();

    // Add some prompt content
    await page.locator('.generation-block .inline-content').click();
    await page.keyboard.type('Generate a test response');

    // Click Run/Execute button (assuming there's one in the UI)
    await page.locator('button:has-text("Run Agent")').click();

    // Wait for loading state
    await expect(page.locator('.generation-block:has-text("Generating response...")')).toBeVisible();

    // Wait for response
    await expect(page.locator('.generation-block:has-text("Response:")')).toBeVisible({
      timeout: 10000,
    });
    await expect(
      page.locator('.generation-block:has-text("This is the generated response from the AI model.")')
    ).toBeVisible();

    // Verify Copy button is present
    await expect(page.locator('.generation-block button:has-text("Copy")')).toBeVisible();
  });

  test('should handle generation errors gracefully', async ({ page }) => {
    // Mock API error response
    await page.route('**/api/agents/*/execute', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          outputs: {},
          errors: ['Block block-1: Failed to generate response: API key invalid'],
        }),
      });
    });

    // Add generation block
    await page.locator('.bn-editor').click();
    await page.keyboard.type('/generation');
    await page.waitForSelector('[role="menu"]');
    await page.locator('[role="menuitem"]:has-text("Generation")').click();

    // Execute agent
    await page.locator('button:has-text("Run Agent")').click();

    // Wait for error display
    await expect(page.locator('.generation-block:has-text("Generation Failed")')).toBeVisible({
      timeout: 10000,
    });
    await expect(
      page.locator('.generation-block:has-text("Failed to generate response: API key invalid")')
    ).toBeVisible();

    // Verify Regenerate button is present
    await expect(page.locator('.generation-block button:has-text("Regenerate")')).toBeVisible();
  });

  test('should copy generated content to clipboard', async ({ page, context }) => {
    // Grant clipboard permissions
    await context.grantPermissions(['clipboard-read', 'clipboard-write']);

    // Mock successful generation
    await page.route('**/api/agents/*/execute', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          outputs: {
            'block-1': {
              content: 'Test content to copy',
              model: 'openai/gpt-3.5-turbo',
            },
          },
        }),
      });
    });

    // Add generation block and execute
    await page.locator('.bn-editor').click();
    await page.keyboard.type('/generation');
    await page.waitForSelector('[role="menu"]');
    await page.locator('[role="menuitem"]:has-text("Generation")').click();
    await page.locator('button:has-text("Run Agent")').click();

    // Wait for response
    await expect(page.locator('.generation-block:has-text("Response:")')).toBeVisible({
      timeout: 10000,
    });

    // Click Copy button
    await page.locator('.generation-block button:has-text("Copy")').click();

    // Verify button changes to "Copied"
    await expect(page.locator('.generation-block button:has-text("Copied")')).toBeVisible();

    // Verify clipboard content
    const clipboardText = await page.evaluate(() => navigator.clipboard.readText());
    expect(clipboardText).toBe('Test content to copy');
  });
});