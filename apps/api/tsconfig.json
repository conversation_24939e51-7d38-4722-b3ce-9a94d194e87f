{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@sflow/types": ["../../packages/types/src/index"], "@sflow/config": ["../../packages/config/src/index"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}