{"name": "api", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint .", "test": "jest", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@better-auth/cli": "^1.2.12", "@fastify/cors": "^10.1.0", "@fastify/helmet": "^12.0.1", "@fastify/multipart": "^9.0.3", "@fastify/rate-limit": "^10.3.0", "@prisma/client": "^6.12.0", "axios": "^1.10.0", "better-auth": "^1.2.12", "fastify": "^5.4.0", "mammoth": "^1.8.0", "pdf-parse": "^1.1.1", "pino": "^9.7.0", "pino-pretty": "^11.3.0", "prisma": "^6.12.0", "resend": "^4.7.0", "zod": "^4.0.5"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^24.0.15", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "jest": "^29.7.0", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}