import prisma from './src/lib/prisma';

async function checkProjectMembers() {
  try {
    // Get all projects
    const projects = await prisma.project.findMany({
      include: {
        members: true,
        user: true
      }
    });
    
    console.log('\nFound', projects.length, 'projects\n');
    
    for (const project of projects) {
      console.log('Project:', project.name);
      console.log('Owner:', project.user.email);
      console.log('Members:', project.members.length);
      
      // Check if owner is a member
      const ownerIsMember = project.members.some(m => m.userId === project.userId);
      
      if (!ownerIsMember) {
        console.log('⚠️  Owner is not a member! Adding as ADMIN...');
        await prisma.projectMember.create({
          data: {
            userId: project.userId,
            projectId: project.id,
            role: 'ADMIN',
            joinedAt: new Date()
          }
        });
        console.log('✅ Added owner as ADMIN member');
      } else {
        const ownerMember = project.members.find(m => m.userId === project.userId);
        console.log('✅ Owner is a member with role:', ownerMember?.role);
      }
      console.log('---');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkProjectMembers();