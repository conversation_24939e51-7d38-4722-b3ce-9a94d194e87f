import { auth } from './src/lib/auth';

async function testMemberAPI() {
  try {
    // Create a mock request with your session
    // This would normally come from cookies in a real request
    const mockHeaders = {
      cookie: process.env.TEST_COOKIE || ''
    };
    
    console.log('Testing member API...');
    console.log('Note: You need to set TEST_COOKIE environment variable with your session cookie');
    
    if (!process.env.TEST_COOKIE) {
      console.log('\nTo get your session cookie:');
      console.log('1. Open browser dev tools (F12)');
      console.log('2. Go to Application/Storage → Cookies');
      console.log('3. Find the better-auth session cookie');
      console.log('4. Run: TEST_COOKIE="your-cookie-value" npx tsx test-member-api.ts');
      return;
    }
    
    // Test getting session
    const session = await auth.api.getSession({ headers: mockHeaders as any });
    console.log('\nSession:', session);
    
    if (!session?.user) {
      console.log('No valid session found');
      return;
    }
    
    // Now test the member API directly
    const projectId = 'cmdeugw0t0001jfgzugdxfmgk';
    console.log(`\nTesting /api/projects/${projectId}/members`);
    
    // Make the request using fetch
    const apiUrl = process.env.API_URL || process.env.BETTER_AUTH_URL || 'http://localhost:3001';
    const response = await fetch(`${apiUrl}/api/projects/${projectId}/members`, {
      headers: {
        'Cookie': process.env.TEST_COOKIE
      }
    });
    
    console.log('Response status:', response.status);
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testMemberAPI();