import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { DeploymentService } from './deployment.service';
import { DeploymentRepository } from '../repositories/deployment.repository';
import { AgentRepository } from '../repositories/agent.repository';
import type { FastifyInstance } from 'fastify';

jest.mock('../repositories/deployment.repository');
jest.mock('../repositories/agent.repository');

describe('DeploymentService', () => {
  let deploymentService: DeploymentService;
  let mockApp: FastifyInstance;
  let mockDeploymentRepository: jest.Mocked<DeploymentRepository>;
  let mockAgentRepository: jest.Mocked<AgentRepository>;

  beforeEach(() => {
    mockApp = {
      prisma: {
        project: {
          findUnique: jest.fn(),
        },
        deployment: {
          update: jest.fn(),
        },
      },
      log: {
        error: jest.fn(),
      },
    } as unknown as FastifyInstance;

    mockDeploymentRepository = new DeploymentRepository(mockApp as FastifyInstance) as jest.Mocked<DeploymentRepository>;
    mockAgentRepository = new AgentRepository((mockApp as any).prisma) as jest.Mocked<AgentRepository>;

    deploymentService = new DeploymentService(mockApp as FastifyInstance);
    (deploymentService as any).deploymentRepository = mockDeploymentRepository;
    (deploymentService as any).agentRepository = mockAgentRepository;
  });

  describe('createDeployment', () => {
    it('should create a deployment successfully', async () => {
      const mockAgent = {
        id: 'agent123',
        projectId: 'project123',
        title: 'Test Agent',
        workflowJson: {},
      };

      const mockProject = {
        id: 'project123',
        workspace: {
          members: [{ userId: 'user123' }],
        },
      };

      const mockDeployment = {
        id: 'deploy123',
        agentId: 'agent123',
        deploymentUrl: 'http://localhost:3001/api/v1/deploy123',
        apiKey: 'sk_test123',
        status: 'ACTIVE',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAgentRepository.findById.mockResolvedValue(mockAgent as never);
      (mockApp.prisma.project.findUnique as jest.MockedFunction<any>).mockResolvedValue(mockProject);
      mockDeploymentRepository.create.mockResolvedValue(mockDeployment as never);
      (mockApp.prisma.deployment.update as jest.MockedFunction<any>).mockResolvedValue(mockDeployment);

      const result = await deploymentService.createDeployment('agent123', 'user123');

      expect(result).toEqual(mockDeployment);
      expect(mockAgentRepository.findById).toHaveBeenCalledWith('agent123');
      expect(mockApp.prisma.project.findUnique).toHaveBeenCalledWith({
        where: { id: 'project123' },
        include: {
          workspace: {
            include: {
              members: true,
            },
          },
        },
      });
    });

    it('should throw error if agent not found', async () => {
      mockAgentRepository.findById.mockResolvedValue(null);

      await expect(
        deploymentService.createDeployment('agent123', 'user123')
      ).rejects.toThrow('Agent not found');
    });

    it('should throw error if user does not have access', async () => {
      const mockAgent = {
        id: 'agent123',
        projectId: 'project123',
      };

      const mockProject = {
        id: 'project123',
        workspace: {
          members: [{ userId: 'otheruser' }],
        },
      };

      mockAgentRepository.findById.mockResolvedValue(mockAgent as never);
      (mockApp.prisma.project.findUnique as jest.MockedFunction<any>).mockResolvedValue(mockProject);

      await expect(
        deploymentService.createDeployment('agent123', 'user123')
      ).rejects.toThrow('Unauthorized: User does not have access to this agent');
    });
  });

  describe('getDeploymentByApiKey', () => {
    it('should return deployment by API key', async () => {
      const mockDeployment = {
        id: 'deploy123',
        apiKey: 'sk_test123',
      };

      mockDeploymentRepository.findByApiKey.mockResolvedValue(mockDeployment as never);

      const result = await deploymentService.getDeploymentByApiKey('sk_test123');

      expect(result).toEqual(mockDeployment);
      expect(mockDeploymentRepository.findByApiKey).toHaveBeenCalledWith('sk_test123');
    });
  });

  describe('updateDeploymentStatus', () => {
    it('should update deployment status successfully', async () => {
      const mockDeployment = {
        id: 'deploy123',
        agent: {
          projectId: 'project123',
        },
      };

      const mockProject = {
        id: 'project123',
        workspace: {
          members: [{ userId: 'user123' }],
        },
      };

      const updatedDeployment = {
        ...mockDeployment,
        status: 'INACTIVE',
      };

      mockDeploymentRepository.findById.mockResolvedValue(mockDeployment as never);
      (mockApp.prisma.project.findUnique as jest.MockedFunction<any>).mockResolvedValue(mockProject);
      mockDeploymentRepository.updateStatus.mockResolvedValue(updatedDeployment as never);

      const result = await deploymentService.updateDeploymentStatus(
        'deploy123',
        'INACTIVE',
        'user123'
      );

      expect(result).toEqual(updatedDeployment);
      expect(mockDeploymentRepository.updateStatus).toHaveBeenCalledWith(
        'deploy123',
        'INACTIVE'
      );
    });

    it('should throw error if deployment not found', async () => {
      mockDeploymentRepository.findById.mockResolvedValue(null);

      await expect(
        deploymentService.updateDeploymentStatus('deploy123', 'INACTIVE', 'user123')
      ).rejects.toThrow('Deployment not found');
    });
  });

  describe('deleteDeployment', () => {
    it('should delete deployment successfully', async () => {
      const mockDeployment = {
        id: 'deploy123',
        agent: {
          projectId: 'project123',
        },
      };

      const mockProject = {
        id: 'project123',
        workspace: {
          members: [{ userId: 'user123' }],
        },
      };

      mockDeploymentRepository.findById.mockResolvedValue(mockDeployment as never);
      (mockApp.prisma.project.findUnique as jest.MockedFunction<any>).mockResolvedValue(mockProject);
      mockDeploymentRepository.delete.mockResolvedValue(mockDeployment as never);

      await deploymentService.deleteDeployment('deploy123', 'user123');

      expect(mockDeploymentRepository.delete).toHaveBeenCalledWith('deploy123');
    });

    it('should throw error if user does not have access', async () => {
      const mockDeployment = {
        id: 'deploy123',
        agent: {
          projectId: 'project123',
        },
      };

      const mockProject = {
        id: 'project123',
        workspace: {
          members: [{ userId: 'otheruser' }],
        },
      };

      mockDeploymentRepository.findById.mockResolvedValue(mockDeployment as never);
      (mockApp.prisma.project.findUnique as jest.MockedFunction<any>).mockResolvedValue(mockProject);

      await expect(
        deploymentService.deleteDeployment('deploy123', 'user123')
      ).rejects.toThrow('Unauthorized: User does not have access to this deployment');
    });
  });
});