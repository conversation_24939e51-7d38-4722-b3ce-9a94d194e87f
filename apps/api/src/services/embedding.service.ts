
export interface EmbeddingService {
  generateEmbeddings(texts: string[]): Promise<number[][]>
}

export class OpenAIEmbeddingService implements EmbeddingService {
  private apiKey: string
  private model: string = 'text-embedding-3-small'
  private dimensions: number = 384

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('OpenAI API key is required')
    }
    this.apiKey = apiKey
  }

  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    if (texts.length === 0) {
      return []
    }

    try {
      const response = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: texts,
          model: this.model,
          dimensions: this.dimensions,
        }),
      })

      if (!response.ok) {
        const error = await response.text()
        throw new Error(`OpenAI API error: ${response.status} - ${error}`)
      }

      const data = await response.json()
      
      return data.data.map((item: { embedding: number[] }) => item.embedding)
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to generate embeddings: ${error.message}`)
      }
      throw new Error('Failed to generate embeddings')
    }
  }
}

export class MockEmbeddingService implements EmbeddingService {
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    return texts.map(() => {
      const embedding = new Array(384).fill(0).map(() => Math.random() * 2 - 1)
      const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0))
      return embedding.map(val => val / magnitude)
    })
  }
}