import { OpenAIEmbeddingService, MockEmbeddingService } from './embedding.service'

global.fetch = jest.fn()

describe('OpenAIEmbeddingService', () => {
  let service: OpenAIEmbeddingService

  beforeEach(() => {
    service = new OpenAIEmbeddingService('test-api-key')
    jest.clearAllMocks()
  })

  it('should throw error if API key is not provided', () => {
    expect(() => new OpenAIEmbeddingService('')).toThrow('OpenAI API key is required')
  })

  it('should generate embeddings successfully', async () => {
    const mockResponse = {
      data: [
        { embedding: new Array(384).fill(0.1) },
        { embedding: new Array(384).fill(0.2) }
      ]
    }

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    })

    const texts = ['text1', 'text2']
    const embeddings = await service.generateEmbeddings(texts)

    expect(embeddings).toHaveLength(2)
    expect(embeddings[0]).toHaveLength(384)
    expect(embeddings[1]).toHaveLength(384)
    expect(global.fetch).toHaveBeenCalledWith(
      'https://api.openai.com/v1/embeddings',
      expect.objectContaining({
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: texts,
          model: 'text-embedding-3-small',
          dimensions: 384,
        }),
      })
    )
  })

  it('should return empty array for empty input', async () => {
    const embeddings = await service.generateEmbeddings([])
    expect(embeddings).toEqual([])
    expect(global.fetch).not.toHaveBeenCalled()
  })

  it('should handle API errors', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 401,
      text: async () => 'Unauthorized'
    })

    await expect(service.generateEmbeddings(['text'])).rejects.toThrow(
      'Failed to generate embeddings: OpenAI API error: 401 - Unauthorized'
    )
  })

  it('should handle network errors', async () => {
    ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

    await expect(service.generateEmbeddings(['text'])).rejects.toThrow(
      'Failed to generate embeddings: Network error'
    )
  })
})

describe('MockEmbeddingService', () => {
  let service: MockEmbeddingService

  beforeEach(() => {
    service = new MockEmbeddingService()
  })

  it('should generate mock embeddings with correct dimensions', async () => {
    const texts = ['text1', 'text2', 'text3']
    const embeddings = await service.generateEmbeddings(texts)

    expect(embeddings).toHaveLength(3)
    embeddings.forEach(embedding => {
      expect(embedding).toHaveLength(384)
      
      const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0))
      expect(magnitude).toBeCloseTo(1, 5)
    })
  })

  it('should return empty array for empty input', async () => {
    const embeddings = await service.generateEmbeddings([])
    expect(embeddings).toEqual([])
  })
})