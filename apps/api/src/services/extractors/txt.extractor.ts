import { TextExtractor } from '../document-processor.service'
import fs from 'fs/promises'

export class TxtExtractor implements TextExtractor {
  async extract(filePath: string): Promise<string> {
    try {
      const text = await fs.readFile(filePath, 'utf-8')
      
      if (!text || text.trim().length === 0) {
        throw new Error('No text content found in file')
      }
      
      return text
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to extract text from TXT file: ${error.message}`)
      }
      throw new Error('Failed to extract text from TXT file')
    }
  }
}