import { TextExtractor } from '../document-processor.service'
import pdf from 'pdf-parse'
import fs from 'fs/promises'

export class PdfExtractor implements TextExtractor {
  async extract(filePath: string): Promise<string> {
    try {
      const dataBuffer = await fs.readFile(filePath)
      const data = await pdf(dataBuffer)
      
      if (!data.text || data.text.trim().length === 0) {
        throw new Error('No text content found in PDF')
      }
      
      return data.text
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to extract text from PDF: ${error.message}`)
      }
      throw new Error('Failed to extract text from PDF')
    }
  }
}