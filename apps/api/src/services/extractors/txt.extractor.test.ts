import { TxtExtractor } from './txt.extractor'
import fs from 'fs/promises'

jest.mock('fs/promises')

describe('TxtExtractor', () => {
  let extractor: TxtExtractor

  beforeEach(() => {
    extractor = new TxtExtractor()
    jest.clearAllMocks()
  })

  it('should extract text from TXT file successfully', async () => {
    const mockText = 'This is plain text content'
    ;(fs.readFile as jest.Mock).mockResolvedValue(mockText)

    const result = await extractor.extract('/path/to/file.txt')

    expect(result).toBe('This is plain text content')
    expect(fs.readFile).toHaveBeenCalledWith('/path/to/file.txt', 'utf-8')
  })

  it('should throw error when file is empty', async () => {
    ;(fs.readFile as jest.Mock).mockResolvedValue('   ')

    await expect(extractor.extract('/path/to/file.txt')).rejects.toThrow(
      'Failed to extract text from TXT file: No text content found in file'
    )
  })

  it('should handle file read errors', async () => {
    ;(fs.readFile as jest.Mock).mockRejectedValue(new Error('File not found'))

    await expect(extractor.extract('/path/to/file.txt')).rejects.toThrow(
      'Failed to extract text from TXT file: File not found'
    )
  })
})