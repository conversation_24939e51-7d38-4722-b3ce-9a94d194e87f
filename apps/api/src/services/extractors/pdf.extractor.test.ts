// Mock pdf-parse before any imports
jest.mock('pdf-parse', () => {
  return jest.fn()
})

import { PdfExtractor } from './pdf.extractor'
import fs from 'fs/promises'
import pdf from 'pdf-parse'

jest.mock('fs/promises')

describe('PdfExtractor', () => {
  let extractor: PdfExtractor

  beforeEach(() => {
    extractor = new PdfExtractor()
    jest.clearAllMocks()
  })

  it('should extract text from PDF successfully', async () => {
    const mockBuffer = Buffer.from('mock pdf content')
    const mockPdfData = {
      text: 'This is extracted text from PDF'
    }

    ;(fs.readFile as jest.Mock).mockResolvedValue(mockBuffer)
    ;(pdf as jest.Mock).mockResolvedValue(mockPdfData)

    const result = await extractor.extract('/path/to/file.pdf')

    expect(result).toBe('This is extracted text from PDF')
    expect(fs.readFile).toHaveBeenCalledWith('/path/to/file.pdf')
    expect(pdf).toHaveBeenCalledWith(mockBuffer)
  })

  it('should throw error when PDF has no text', async () => {
    const mockBuffer = Buffer.from('mock pdf content')
    const mockPdfData = {
      text: '   '
    }

    ;(fs.readFile as jest.Mock).mockResolvedValue(mockBuffer)
    ;(pdf as jest.Mock).mockResolvedValue(mockPdfData)

    await expect(extractor.extract('/path/to/file.pdf')).rejects.toThrow(
      'Failed to extract text from PDF: No text content found in PDF'
    )
  })

  it('should handle file read errors', async () => {
    ;(fs.readFile as jest.Mock).mockRejectedValue(new Error('File not found'))

    await expect(extractor.extract('/path/to/file.pdf')).rejects.toThrow(
      'Failed to extract text from PDF: File not found'
    )
  })

  it('should handle PDF parsing errors', async () => {
    const mockBuffer = Buffer.from('mock pdf content')
    ;(fs.readFile as jest.Mock).mockResolvedValue(mockBuffer)
    ;(pdf as jest.Mock).mockRejectedValue(new Error('Invalid PDF'))

    await expect(extractor.extract('/path/to/file.pdf')).rejects.toThrow(
      'Failed to extract text from PDF: Invalid PDF'
    )
  })
})