import { TextExtractor } from '../document-processor.service'
import mammoth from 'mammoth'

export class DocxExtractor implements TextExtractor {
  async extract(filePath: string): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ path: filePath })
      
      if (result.messages && result.messages.length > 0) {
        console.warn('DOCX extraction warnings:', result.messages)
      }
      
      if (!result.value || result.value.trim().length === 0) {
        throw new Error('No text content found in DOCX')
      }
      
      return result.value
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to extract text from DOCX: ${error.message}`)
      }
      throw new Error('Failed to extract text from DOCX')
    }
  }
}