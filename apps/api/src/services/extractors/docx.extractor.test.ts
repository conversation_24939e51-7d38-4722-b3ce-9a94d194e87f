import { DocxExtractor } from './docx.extractor'
import mammoth from 'mammoth'

jest.mock('mammoth')

describe('DocxExtractor', () => {
  let extractor: DocxExtractor
  let consoleWarnSpy: jest.SpyInstance

  beforeEach(() => {
    extractor = new DocxExtractor()
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation()
    jest.clearAllMocks()
  })

  afterEach(() => {
    consoleWarnSpy.mockRestore()
  })

  it('should extract text from DOCX successfully', async () => {
    const mockResult = {
      value: 'This is extracted text from DOCX',
      messages: []
    }

    ;(mammoth.extractRawText as jest.Mock).mockResolvedValue(mockResult)

    const result = await extractor.extract('/path/to/file.docx')

    expect(result).toBe('This is extracted text from DOCX')
    expect(mammoth.extractRawText).toHaveBeenCalledWith({ path: '/path/to/file.docx' })
  })

  it('should log warnings if present', async () => {
    const mockResult = {
      value: 'This is extracted text from DOCX',
      messages: ['Warning: Some formatting lost']
    }

    ;(mammoth.extractRawText as jest.Mock).mockResolvedValue(mockResult)

    const result = await extractor.extract('/path/to/file.docx')

    expect(result).toBe('This is extracted text from DOCX')
    expect(consoleWarnSpy).toHaveBeenCalledWith('DOCX extraction warnings:', ['Warning: Some formatting lost'])
  })

  it('should throw error when DOCX has no text', async () => {
    const mockResult = {
      value: '   ',
      messages: []
    }

    ;(mammoth.extractRawText as jest.Mock).mockResolvedValue(mockResult)

    await expect(extractor.extract('/path/to/file.docx')).rejects.toThrow(
      'Failed to extract text from DOCX: No text content found in DOCX'
    )
  })

  it('should handle extraction errors', async () => {
    ;(mammoth.extractRawText as jest.Mock).mockRejectedValue(new Error('Invalid DOCX'))

    await expect(extractor.extract('/path/to/file.docx')).rejects.toThrow(
      'Failed to extract text from DOCX: Invalid DOCX'
    )
  })
})