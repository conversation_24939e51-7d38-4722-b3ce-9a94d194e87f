import { FastifyInstance } from 'fastify';
import pino from 'pino';

export interface SearchResult {
  id: string;
  documentId: string;
  content: string;
  similarity?: number;
  metadata?: any;
}

export class DocumentSearchService {
  private logger: pino.Logger;
  private prisma: any;

  constructor(app: FastifyInstance, logger: pino.Logger) {
    this.logger = logger.child({ service: 'DocumentSearchService' });
    this.prisma = app.prisma || (app as any).db;
  }

  /**
   * Search within a specific document using vector similarity
   */
  async searchWithinDocument(
    query: string,
    documentId: string,
    projectId: string,
    limit: number = 5
  ): Promise<SearchResult[]> {
    try {
      // For now, use text search as a placeholder
      // TODO: Implement actual vector search when embeddings are available
      const chunks = await this.prisma.documentChunk.findMany({
        where: {
          documentId,
          document: {
            projectDocuments: {
              some: {
                projectId
              }
            }
          },
          content: {
            contains: query,
            mode: 'insensitive'
          }
        },
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      });

      return chunks.map((chunk: any) => ({
        id: chunk.id,
        documentId: chunk.documentId,
        content: chunk.content,
        metadata: chunk.metadata
      }));
    } catch (error) {
      this.logger.error({ error, documentId, query }, 'Failed to search within document');
      throw error;
    }
  }

  /**
   * Search across all documents in a project
   */
  async searchAcrossProject(
    query: string,
    projectId: string,
    limit: number = 10
  ): Promise<SearchResult[]> {
    try {
      // Get all document IDs for the project
      const projectDocs = await this.prisma.projectDocuments.findMany({
        where: { projectId },
        select: { documentId: true }
      });

      const documentIds = projectDocs.map((pd: any) => pd.documentId);

      if (documentIds.length === 0) {
        return [];
      }

      // Search chunks across all project documents
      const chunks = await this.prisma.documentChunk.findMany({
        where: {
          documentId: {
            in: documentIds
          },
          content: {
            contains: query,
            mode: 'insensitive'
          }
        },
        include: {
          document: {
            select: {
              id: true,
              fileName: true
            }
          }
        },
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      });

      return chunks.map((chunk: any) => ({
        id: chunk.id,
        documentId: chunk.documentId,
        content: chunk.content,
        metadata: {
          ...chunk.metadata,
          fileName: chunk.document.fileName
        }
      }));
    } catch (error) {
      this.logger.error({ error, projectId, query }, 'Failed to search across project');
      throw error;
    }
  }
}