import { AgentRepository } from '../repositories/agent.repository';
import { PrismaClient } from '@prisma/client';
import { FastifyInstance } from 'fastify';
import pino from 'pino';
import { GenerationService } from './generation.service';
import { DocumentSearchService } from './document-search.service';
import { GenerationRequest } from '../types/generation.types';

export interface CreateAgentDto {
  title: string;
  workflowJson: any;
  projectId: string;
  triggerType?: string;
}

export interface UpdateAgentDto {
  title?: string;
  workflowJson?: any;
  triggerType?: string;
}

export interface WorkflowExecutionContext {
  agentId: string;
  projectId: string;
  inputs?: Record<string, any>;
}

export interface WorkflowExecutionResult {
  success: boolean;
  outputs: Record<string, any>;
  errors?: string[];
}

export class AgentService {
  private agentRepository: AgentRepository;
  private generationService?: GenerationService;
  private logger?: pino.Logger;
  private app?: FastifyInstance;

  constructor(prisma: PrismaClient, app?: FastifyInstance, logger?: pino.Logger) {
    this.agentRepository = new AgentRepository(prisma);
    this.app = app;
    this.logger = logger;
    
    // Initialize generation service if we have the required dependencies
    if (app && logger) {
      const documentSearchService = new DocumentSearchService(app, logger);
      this.generationService = new GenerationService(app, logger, documentSearchService);
    }
  }

  async createAgent(data: CreateAgentDto) {
    return this.agentRepository.create({
      title: data.title,
      workflowJson: data.workflowJson,
      project: {
        connect: { id: data.projectId }
      },
      triggerType: data.triggerType,
    });
  }

  async getAgent(id: string) {
    const agent = await this.agentRepository.findById(id);
    if (!agent) {
      throw new Error('Agent not found');
    }
    return agent;
  }

  async getAgentsByProject(projectId: string) {
    return this.agentRepository.findByProjectId(projectId);
  }

  async updateAgent(id: string, data: UpdateAgentDto) {
    const exists = await this.agentRepository.exists(id);
    if (!exists) {
      throw new Error('Agent not found');
    }
    
    return this.agentRepository.update(id, data);
  }

  async deleteAgent(id: string) {
    const exists = await this.agentRepository.exists(id);
    if (!exists) {
      throw new Error('Agent not found');
    }
    
    return this.agentRepository.delete(id);
  }

  async checkAgentAccess(agentId: string, projectId: string): Promise<boolean> {
    return this.agentRepository.belongsToProject(agentId, projectId);
  }

  /**
   * Execute an agent's workflow
   */
  async executeWorkflow(context: WorkflowExecutionContext): Promise<WorkflowExecutionResult> {
    if (!this.generationService || !this.logger) {
      throw new Error('Agent service not properly initialized for workflow execution');
    }

    const { agentId, projectId, inputs = {} } = context;
    const errors: string[] = [];
    const outputs: Record<string, any> = {};

    try {
      // Get the agent
      const agent = await this.getAgent(agentId);
      
      // Verify agent belongs to project
      const hasAccess = await this.checkAgentAccess(agentId, projectId);
      if (!hasAccess) {
        throw new Error('Agent does not belong to the specified project');
      }

      // Process workflow blocks
      const blocks = Array.isArray(agent.workflowJson) ? agent.workflowJson : [];
      
      for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        if (!block || typeof block !== 'object' || Array.isArray(block)) {
          continue;
        }
        
        const blockObj = block as any;
        if (!blockObj.id) {
          continue;
        }

        try {
          const blockResult = await this.processBlock(blockObj, {
            agentId,
            projectId,
            inputs,
            previousOutputs: outputs,
            allBlocks: blocks,
            currentBlockIndex: i,
          });
          
          if (blockResult) {
            outputs[blockObj.id] = blockResult;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Block ${blockObj.id}: ${errorMessage}`);
          this.logger.error({ error, blockId: blockObj.id }, 'Failed to process block');
        }
      }

      return {
        success: errors.length === 0,
        outputs,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      this.logger.error({ error, context }, 'Failed to execute workflow');
      throw error;
    }
  }

  /**
   * Process a single block in the workflow
   */
  private async processBlock(
    block: any,
    context: {
      agentId: string;
      projectId: string;
      inputs: Record<string, any>;
      previousOutputs: Record<string, any>;
      allBlocks?: any[];
      currentBlockIndex?: number;
    }
  ): Promise<any> {
    if (!this.generationService || !this.logger) {
      throw new Error('Required services not initialized');
    }

    this.logger.debug({ blockType: block.type, blockId: block.id }, 'Processing block');

    // Handle Generation blocks
    if (block.type === 'generation') {
      return this.processGenerationBlock(block, context);
    }

    // Handle other block types (placeholder for future expansion)
    // For now, just return the block content
    return block.content || null;
  }

  /**
   * Process a Generation block
   */
  private async processGenerationBlock(
    block: any,
    context: {
      agentId: string;
      projectId: string;
      inputs: Record<string, any>;
      previousOutputs: Record<string, any>;
      allBlocks?: any[]; // Add this to pass all blocks
      currentBlockIndex?: number; // Add this to know current position
    }
  ): Promise<any> {
    if (!this.generationService) {
      throw new Error('Generation service not initialized');
    }

    // Get prompt from blocks above this generation block
    let promptContent = '';
    
    // If we have the block context, gather content from blocks above
    if (context.allBlocks && context.currentBlockIndex !== undefined) {
      const blocksAbove = context.allBlocks.slice(0, context.currentBlockIndex);
      
      // Combine content from all blocks above
      promptContent = blocksAbove
        .map(b => this.extractTextFromBlock(b, context))
        .filter(text => text)
        .join('\n\n');
    }
    
    // If no content from above blocks, check if there's a prompt in the block itself (for backward compatibility)
    if (!promptContent && block.props?.prompt) {
      promptContent = block.props.prompt;
    }
    
    if (!promptContent) {
      throw new Error('No prompt content found for generation block. Add content in blocks above the generation block.');
    }

    // Create generation request
    const request: GenerationRequest = {
      blockId: block.id,
      agentId: context.agentId,
      projectId: context.projectId,
      config: block.props || {
        provider: 'openrouter',
        model: 'openai/gpt-3.5-turbo',
        temperature: 0.7,
      },
      prompt: promptContent,
      context: context.inputs,
    };

    // Execute generation
    const response = await this.generationService.executeGeneration(request);
    
    return {
      content: response.content,
      model: response.model,
      usage: response.usage,
    };
  }

  /**
   * Extract text content from any block
   */
  private extractTextFromBlock(
    block: any,
    context: {
      inputs: Record<string, any>;
      previousOutputs: Record<string, any>;
    }
  ): string {
    // Skip generation blocks themselves
    if (block.type === 'generation') {
      return '';
    }

    let text = '';
    
    // Handle different content formats
    if (block.content && Array.isArray(block.content)) {
      // BlockNote stores content as an array of inline content
      text = block.content
        .map((item: any) => {
          if (typeof item === 'string') return item;
          if (item.type === 'text') return item.text;
          return '';
        })
        .join('');
    } else if (typeof block.content === 'string') {
      text = block.content;
    }

    // Replace input variables (e.g., @input_name with actual values)
    const inputPattern = /@(\w+)/g;
    text = text.replace(inputPattern, (match, inputName) => {
      // Check if it's a Knowledge Base reference
      if (match.startsWith('@KnowledgeBase:')) {
        return match; // Keep Knowledge Base references as-is
      }
      
      // Replace with input value
      const value = context.inputs[inputName] || context.previousOutputs[inputName];
      return value !== undefined ? String(value) : match;
    });

    return text.trim();
  }

  /**
   * Extract prompt content from a block, replacing input variables
   */
  private extractPromptFromBlock(
    block: any,
    context: {
      inputs: Record<string, any>;
      previousOutputs: Record<string, any>;
    }
  ): string {
    return this.extractTextFromBlock(block, context);
  }
}