import type { Agent } from '@prisma/client';

export interface InputField {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  required: boolean;
  default?: unknown;
  enum?: string[];
  properties?: Record<string, InputField>;
  items?: InputField;
}

export interface ApiDocumentation {
  title: string;
  description: string;
  version: string;
  endpoint: {
    url: string;
    method: string;
    headers: Record<string, string>;
  };
  inputs: InputField[];
  responses: {
    success: {
      status: number;
      description: string;
      schema: unknown;
    };
    errors: Array<{
      status: number;
      description: string;
      schema: unknown;
    }>;
  };
  examples: {
    curl: string;
    javascript: string;
    python: string;
  };
}

export class DocumentationService {
  generateApiDocumentation(
    agent: Agent,
    deploymentUrl: string
  ): ApiDocumentation {
    const workflowJson = agent.workflowJson as Record<string, unknown>;
    const inputs = this.extractInputFields(workflowJson);
    const exampleInputs = this.generateExampleInputs(inputs);

    return {
      title: agent.title,
      description: this.extractDescription(workflowJson) || `API for ${agent.title}`,
      version: '1.0.0',
      endpoint: {
        url: `${deploymentUrl}/execute`,
        method: 'POST',
        headers: {
          'Authorization': 'Bearer YOUR_API_KEY',
          'Content-Type': 'application/json',
        },
      },
      inputs,
      responses: {
        success: {
          status: 200,
          description: 'Workflow executed successfully',
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              result: { type: 'object' },
            },
          },
        },
        errors: [
          {
            status: 400,
            description: 'Bad Request - Invalid input',
            schema: {
              type: 'object',
              properties: {
                error: { type: 'string' },
                message: { type: 'string' },
                details: { type: 'object' },
              },
            },
          },
          {
            status: 401,
            description: 'Unauthorized - Invalid or missing API key',
            schema: {
              type: 'object',
              properties: {
                error: { type: 'string' },
                message: { type: 'string' },
              },
            },
          },
          {
            status: 403,
            description: 'Forbidden - Deployment is not active',
            schema: {
              type: 'object',
              properties: {
                error: { type: 'string' },
                message: { type: 'string' },
              },
            },
          },
          {
            status: 500,
            description: 'Internal Server Error',
            schema: {
              type: 'object',
              properties: {
                error: { type: 'string' },
                message: { type: 'string' },
              },
            },
          },
        ],
      },
      examples: this.generateExamples(deploymentUrl, exampleInputs),
    };
  }

  private extractInputFields(workflowJson: Record<string, unknown>): InputField[] {
    const inputs: InputField[] = [];

    try {
      if (workflowJson.blocks && Array.isArray(workflowJson.blocks)) {
        for (const block of workflowJson.blocks as Array<Record<string, unknown>>) {
          if (block.type === 'input' && block.config) {
            const config = block.config as Record<string, unknown>;
            const field: InputField = {
              name: (config.name as string) || (block.id as string),
              type: this.mapBlockTypeToApiType(config.type as string),
              description: (config.description as string) || '',
              required: config.required !== false,
            };

            if (config.default !== undefined) {
              field.default = config.default;
            }

            if (config.enum && Array.isArray(config.enum)) {
              field.enum = config.enum as string[];
            }

            if (config.type === 'object' && config.properties) {
              field.properties = this.extractObjectProperties(config.properties as Record<string, unknown>);
            }

            if (config.type === 'array' && config.items) {
              field.items = this.extractArrayItems(config.items);
            }

            inputs.push(field);
          }
        }
      }
    } catch (error) {
      console.error('Error extracting input fields:', error);
    }

    return inputs;
  }

  private extractDescription(workflowJson: Record<string, unknown>): string | null {
    try {
      if (workflowJson.description) {
        return workflowJson.description as string;
      }
      if (workflowJson.blocks && Array.isArray(workflowJson.blocks)) {
        const descriptionBlock = (workflowJson.blocks as Array<Record<string, unknown>>).find(
          (block) => block.type === 'description' || block.type === 'text'
        );
        if (descriptionBlock?.content) {
          return descriptionBlock.content as string;
        }
      }
    } catch (error) {
      console.error('Error extracting description:', error);
    }
    return null;
  }

  private mapBlockTypeToApiType(
    blockType: string
  ): 'string' | 'number' | 'boolean' | 'object' | 'array' {
    const typeMap: Record<string, InputField['type']> = {
      text: 'string',
      string: 'string',
      number: 'number',
      integer: 'number',
      boolean: 'boolean',
      bool: 'boolean',
      object: 'object',
      array: 'array',
      list: 'array',
    };

    return typeMap[blockType?.toLowerCase()] || 'string';
  }

  private extractObjectProperties(properties: Record<string, unknown>): Record<string, InputField> {
    const result: Record<string, InputField> = {};

    try {
      for (const [key, value] of Object.entries(properties)) {
        if (typeof value === 'object' && value !== null) {
          const prop = value as Record<string, unknown>;
          result[key] = {
            name: key,
            type: this.mapBlockTypeToApiType(prop.type as string),
            description: (prop.description as string) || '',
            required: prop.required !== false,
          };
        }
      }
    } catch (error) {
      console.error('Error extracting object properties:', error);
    }

    return result;
  }

  private extractArrayItems(items: unknown): InputField {
    try {
      if (typeof items === 'object' && items !== null) {
        return {
          name: 'item',
          type: this.mapBlockTypeToApiType((items as Record<string, unknown>).type as string),
          description: ((items as Record<string, unknown>).description as string) || '',
          required: true,
        };
      }
    } catch (error) {
      console.error('Error extracting array items:', error);
    }

    return {
      name: 'item',
      type: 'string',
      description: '',
      required: true,
    };
  }

  private generateExampleInputs(inputs: InputField[]): Record<string, unknown> {
    const example: Record<string, unknown> = {};

    for (const input of inputs) {
      example[input.name] = this.generateExampleValue(input);
    }

    return { inputs: example };
  }

  private generateExampleValue(field: InputField): unknown {
    if (field.default !== undefined) {
      return field.default;
    }

    if (field.enum && field.enum.length > 0) {
      return field.enum[0];
    }

    switch (field.type) {
      case 'number':
        return 123;
      case 'boolean':
        return true;
      case 'object':
        if (field.properties) {
          const obj: Record<string, unknown> = {};
          for (const [key, prop] of Object.entries(field.properties)) {
            obj[key] = this.generateExampleValue(prop);
          }
          return obj;
        }
        return {};
      case 'array':
        if (field.items) {
          return [this.generateExampleValue(field.items)];
        }
        return ['example'];
      case 'string':
      default:
        return 'example value';
    }
  }

  private generateExamples(deploymentUrl: string, exampleInputs: Record<string, unknown>): Record<string, string> {
    const jsonBody = JSON.stringify(exampleInputs, null, 2);

    return {
      curl: `curl -X POST "${deploymentUrl}/execute" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '${jsonBody}'`,
      
      javascript: `const response = await fetch("${deploymentUrl}/execute", {
  method: "POST",
  headers: {
    "Authorization": "Bearer YOUR_API_KEY",
    "Content-Type": "application/json"
  },
  body: JSON.stringify(${JSON.stringify(exampleInputs, null, 4).replace(/\n/g, '\n  ')})
});

const result = await response.json();
console.log(result);`,
      
      python: `import requests

response = requests.post(
    "${deploymentUrl}/execute",
    headers={
        "Authorization": "Bearer YOUR_API_KEY",
        "Content-Type": "application/json"
    },
    json=${JSON.stringify(exampleInputs, null, 4).replace(/\n/g, '\n    ')}
)

result = response.json()
print(result)`,
    };
  }
}