import { FastifyInstance } from 'fastify'
import { DocumentProcessorService } from './document-processor.service'
import { PdfExtractor } from './extractors/pdf.extractor'
import { DocxExtractor } from './extractors/docx.extractor'
import { TxtExtractor } from './extractors/txt.extractor'
import prisma from '../lib/prisma'
import fs from 'fs/promises'
import path from 'path'

// This is an integration test that tests the complete document processing flow
describe('DocumentProcessorService Integration Tests', () => {
  let service: DocumentProcessorService
  let mockServer: FastifyInstance
  let testWorkspaceId: string
  let testUserId: string
  let testDocumentId: string

  beforeAll(async () => {
    // Create test data
    const testWorkspace = await prisma.workspace.create({
      data: {
        name: 'Test Workspace',
        slug: 'test-workspace-' + Date.now()
      }
    })
    testWorkspaceId = testWorkspace.id

    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User'
      }
    })
    testUserId = testUser.id

    await prisma.workspaceMember.create({
      data: {
        workspaceId: testWorkspaceId,
        userId: testUserId,
        role: 'ADMIN'
      }
    })
  })

  afterAll(async () => {
    // Clean up test data
    await prisma.documentChunk.deleteMany({
      where: {
        document: {
          workspaceId: testWorkspaceId
        }
      }
    })
    await prisma.knowledgeBaseDocument.deleteMany({
      where: { workspaceId: testWorkspaceId }
    })
    await prisma.workspaceMember.deleteMany({
      where: { userId: testUserId }
    })
    await prisma.user.delete({
      where: { id: testUserId }
    })
    await prisma.workspace.delete({
      where: { id: testWorkspaceId }
    })
  })

  beforeEach(async () => {
    mockServer = {
      log: {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn()
      },
      prisma
    } as any

    service = new DocumentProcessorService(mockServer)
    
    // Register extractors
    service.registerExtractor('application/pdf', new PdfExtractor())
    service.registerExtractor('application/vnd.openxmlformats-officedocument.wordprocessingml.document', new DocxExtractor())
    service.registerExtractor('text/plain', new TxtExtractor())
  })

  describe('Complete Processing Flow', () => {
    it('should process a text file end-to-end', async () => {
      // Create test file
      const uploadDir = path.join(process.cwd(), 'uploads', testWorkspaceId)
      await fs.mkdir(uploadDir, { recursive: true })
      
      const testFileName = 'test-file.txt'
      const testFilePath = path.join(uploadDir, testFileName)
      const testContent = `This is the first sentence of the test document. 
      This is the second sentence with more content. 
      Here is a third sentence to ensure we have enough text for chunking.
      And a fourth sentence to make the document longer.
      Finally, a fifth sentence to complete our test content.`
      
      await fs.writeFile(testFilePath, testContent)

      // Create document record
      const document = await prisma.knowledgeBaseDocument.create({
        data: {
          workspaceId: testWorkspaceId,
          fileName: 'test.txt',
          storedFileName: testFileName,
          fileType: 'text/plain',
          fileSize: testContent.length,
          status: 'PENDING'
        }
      })
      testDocumentId = document.id

      // Process the document
      const result = await service.processDocument(testDocumentId, testUserId)

      // Verify results
      expect(result.success).toBe(true)
      expect(result.chunks).toBeDefined()
      expect(result.chunks!.length).toBeGreaterThan(0)

      // Verify document status was updated
      const updatedDocument = await prisma.knowledgeBaseDocument.findUnique({
        where: { id: testDocumentId }
      })
      expect(updatedDocument?.status).toBe('completed')

      // Verify chunks were created
      const chunks = await prisma.documentChunk.findMany({
        where: { documentId: testDocumentId },
        orderBy: { createdAt: 'asc' }
      })
      expect(chunks.length).toBeGreaterThan(0)
      expect(chunks[0].content).toBeTruthy()
      expect(chunks[0].metadata).toBeTruthy()

      // Clean up test file
      await fs.unlink(testFilePath)
    })

    it('should handle processing failures gracefully', async () => {
      // Create document record without actual file
      const document = await prisma.knowledgeBaseDocument.create({
        data: {
          workspaceId: testWorkspaceId,
          fileName: 'missing.txt',
          storedFileName: 'missing-file.txt',
          fileType: 'text/plain',
          fileSize: 100,
          status: 'PENDING'
        }
      })

      // Process the document
      const result = await service.processDocument(document.id, testUserId)

      // Verify failure
      expect(result.success).toBe(false)
      expect(result.error).toBe('File not found on disk')

      // Verify document status was updated to failed
      const updatedDocument = await prisma.knowledgeBaseDocument.findUnique({
        where: { id: document.id }
      })
      expect(updatedDocument?.status).toBe('failed')

      // Clean up
      await prisma.knowledgeBaseDocument.delete({
        where: { id: document.id }
      })
    })

    it('should respect workspace isolation', async () => {
      // Create document in a different workspace
      const otherWorkspace = await prisma.workspace.create({
        data: {
          name: 'Other Workspace',
          slug: 'other-workspace-' + Date.now()
        }
      })

      const document = await prisma.knowledgeBaseDocument.create({
        data: {
          workspaceId: otherWorkspace.id,
          fileName: 'other.txt',
          storedFileName: 'other-file.txt',
          fileType: 'text/plain',
          fileSize: 100,
          status: 'PENDING'
        }
      })

      // Try to process with user from different workspace
      const result = await service.processDocument(document.id, testUserId)

      // Verify access denied
      expect(result.success).toBe(false)
      expect(result.error).toBe('Document not found')

      // Clean up
      await prisma.knowledgeBaseDocument.delete({
        where: { id: document.id }
      })
      await prisma.workspace.delete({
        where: { id: otherWorkspace.id }
      })
    })
  })
})