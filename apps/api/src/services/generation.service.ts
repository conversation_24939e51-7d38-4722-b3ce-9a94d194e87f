import { FastifyInstance } from 'fastify';
import pino from 'pino';
import { 
  GenerationRequest, 
  GenerationResponse, 
  KnowledgeBaseReference 
} from '../types/generation.types';
import { DocumentSearchService } from './document-search.service';
import { OpenRouterService } from './openrouter.service';
import { DocumentRepository } from '../repositories/document.repository';

export class GenerationService {
  private logger: pino.Logger;
  private documentSearchService: DocumentSearchService;
  private openRouterService: OpenRouterService;
  private documentRepository: DocumentRepository;

  constructor(
    app: FastifyInstance,
    logger: pino.Logger,
    documentSearchService: DocumentSearchService
  ) {
    this.logger = logger.child({ service: 'GenerationService' });
    this.documentSearchService = documentSearchService;
    this.openRouterService = new OpenRouterService(logger);
    this.documentRepository = new DocumentRepository(app.prisma || (app as any).db);
  }

  /**
   * Execute generation with RAG support
   */
  async executeGeneration(request: GenerationRequest): Promise<GenerationResponse> {
    try {
      const { config, prompt, projectId } = request;
      
      // Parse @KnowledgeBase references from the prompt
      const references = this.parseKnowledgeBaseReferences(prompt);
      
      // Build the final prompt with RAG context
      const finalPrompt = await this.buildPromptWithContext(
        prompt,
        references,
        projectId
      );

      // Generate completion using the configured LLM
      const response = await this.openRouterService.generateCompletion(
        finalPrompt,
        config.model,
        config.temperature
      );

      return response;
    } catch (error) {
      this.logger.error({ error, request }, 'Failed to execute generation');
      throw error;
    }
  }

  /**
   * Parse @KnowledgeBase references from the prompt
   */
  parseKnowledgeBaseReferences(prompt: string): KnowledgeBaseReference[] {
    const references: KnowledgeBaseReference[] = [];
    
    // Regular expression to match @KnowledgeBase:filename patterns
    const regex = /@KnowledgeBase:([^\s]+)/g;
    let match;

    while ((match = regex.exec(prompt)) !== null) {
      references.push({
        documentName: match[1],
      });
    }

    this.logger.debug({ references }, 'Parsed knowledge base references');
    return references;
  }

  /**
   * Build final prompt with RAG context
   */
  private async buildPromptWithContext(
    originalPrompt: string,
    references: KnowledgeBaseReference[],
    projectId: string
  ): Promise<string> {
    if (references.length === 0) {
      return originalPrompt;
    }

    const contextChunks: string[] = [];

    for (const reference of references) {
      try {
        // Find the document in the project
        const document = await this.documentRepository.findByFileName(
          reference.documentName,
          projectId
        );

        if (!document) {
          this.logger.warn(
            { documentName: reference.documentName, projectId },
            'Referenced document not found in project'
          );
          continue;
        }

        // Extract relevant text from the prompt for search
        const searchQuery = this.extractSearchQuery(originalPrompt, reference.documentName);

        // Perform vector search to get relevant chunks
        const searchResults = await this.documentSearchService.searchWithinDocument(
          searchQuery,
          document.id,
          projectId,
          5 // Get top 5 chunks
        );

        // Add chunks to context
        const chunks = searchResults.map(result => result.content);
        if (chunks.length > 0) {
          contextChunks.push(
            `\n--- Context from ${reference.documentName} ---\n${chunks.join('\n\n')}`
          );
        }
      } catch (error) {
        this.logger.error(
          { error, reference, projectId },
          'Failed to retrieve context for reference'
        );
      }
    }

    // Build the final prompt with context
    if (contextChunks.length > 0) {
      const contextSection = contextChunks.join('\n');
      
      // Remove the @KnowledgeBase references from the original prompt
      let cleanPrompt = originalPrompt;
      for (const reference of references) {
        cleanPrompt = cleanPrompt.replace(
          `@KnowledgeBase:${reference.documentName}`,
          `[${reference.documentName}]`
        );
      }

      return `Based on the following context from the knowledge base:
${contextSection}

Please answer the following:
${cleanPrompt}`;
    }

    return originalPrompt;
  }

  /**
   * Extract a search query from the prompt based on the document reference
   */
  private extractSearchQuery(prompt: string, documentName: string): string {
    // Remove the @KnowledgeBase reference
    const cleanPrompt = prompt.replace(`@KnowledgeBase:${documentName}`, '');
    
    // Simple heuristic: use the sentence containing the reference
    // or the entire prompt if it's short
    const sentences = cleanPrompt.split(/[.!?]+/);
    
    if (sentences.length === 1 || cleanPrompt.length < 200) {
      return cleanPrompt.trim();
    }

    // Find the sentence that was likely referring to the document
    // (usually the one right before or containing the reference)
    const referenceIndex = prompt.indexOf(`@KnowledgeBase:${documentName}`);
    let relevantSentence = '';
    let currentPos = 0;

    for (const sentence of sentences) {
      const sentenceEnd = currentPos + sentence.length;
      if (referenceIndex >= currentPos && referenceIndex <= sentenceEnd + 1) {
        relevantSentence = sentence.trim();
        break;
      }
      currentPos = sentenceEnd + 1;
    }

    return relevantSentence || sentences[0].trim();
  }
}