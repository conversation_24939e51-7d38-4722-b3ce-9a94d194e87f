import { describe, it, expect, beforeEach } from '@jest/globals';
import { DocumentationService } from './documentation.service';
import type { Agent } from '@prisma/client';

describe('DocumentationService', () => {
  let documentationService: DocumentationService;

  beforeEach(() => {
    documentationService = new DocumentationService();
  });

  describe('generateApiDocumentation', () => {
    it('should generate documentation for agent with input fields', () => {
      const mockAgent: Agent = {
        id: 'agent123',
        title: 'Test Agent',
        workflowJson: {
          description: 'This is a test agent',
          blocks: [
            {
              id: 'input1',
              type: 'input',
              config: {
                name: 'userName',
                type: 'string',
                description: 'The user name',
                required: true,
              },
            },
            {
              id: 'input2',
              type: 'input',
              config: {
                name: 'age',
                type: 'number',
                description: 'The user age',
                required: false,
                default: 18,
              },
            },
            {
              id: 'input3',
              type: 'input',
              config: {
                name: 'isActive',
                type: 'boolean',
                required: true,
              },
            },
          ],
        },
        triggerType: 'api',
        projectId: 'project123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const deploymentUrl = 'http://localhost:3001/api/v1/deploy123';

      const documentation = documentationService.generateApiDocumentation(
        mockAgent,
        deploymentUrl
      );

      expect(documentation.title).toBe('Test Agent');
      expect(documentation.description).toBe('This is a test agent');
      expect(documentation.version).toBe('1.0.0');
      expect(documentation.endpoint.url).toBe(
        'http://localhost:3001/api/v1/deploy123/execute'
      );
      expect(documentation.endpoint.method).toBe('POST');
      expect(documentation.inputs).toHaveLength(3);

      expect(documentation.inputs[0]).toEqual({
        name: 'userName',
        type: 'string',
        description: 'The user name',
        required: true,
      });

      expect(documentation.inputs[1]).toEqual({
        name: 'age',
        type: 'number',
        description: 'The user age',
        required: false,
        default: 18,
      });

      expect(documentation.inputs[2]).toEqual({
        name: 'isActive',
        type: 'boolean',
        description: '',
        required: true,
      });

      expect(documentation.examples.curl).toContain('curl -X POST');
      expect(documentation.examples.javascript).toContain('fetch(');
      expect(documentation.examples.python).toContain('requests.post(');
    });

    it('should handle agent with no input fields', () => {
      const mockAgent: Agent = {
        id: 'agent123',
        title: 'Simple Agent',
        workflowJson: {
          blocks: [
            {
              type: 'text',
              content: 'Hello world',
            },
          ],
        },
        triggerType: null,
        projectId: 'project123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const documentation = documentationService.generateApiDocumentation(
        mockAgent,
        'http://localhost:3001/api/v1/deploy123'
      );

      expect(documentation.inputs).toHaveLength(0);
      expect(documentation.examples.curl).toContain('"inputs": {}');
    });

    it('should handle complex input types', () => {
      const mockAgent: Agent = {
        id: 'agent123',
        title: 'Complex Agent',
        workflowJson: {
          blocks: [
            {
              type: 'input',
              config: {
                name: 'config',
                type: 'object',
                properties: {
                  apiKey: {
                    type: 'string',
                    description: 'API key',
                    required: true,
                  },
                  timeout: {
                    type: 'number',
                    description: 'Timeout in seconds',
                    required: false,
                  },
                },
              },
            },
            {
              type: 'input',
              config: {
                name: 'tags',
                type: 'array',
                items: {
                  type: 'string',
                  description: 'Tag name',
                },
              },
            },
            {
              type: 'input',
              config: {
                name: 'status',
                type: 'string',
                enum: ['draft', 'published', 'archived'],
              },
            },
          ],
        },
        triggerType: null,
        projectId: 'project123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const documentation = documentationService.generateApiDocumentation(
        mockAgent,
        'http://localhost:3001/api/v1/deploy123'
      );

      expect(documentation.inputs).toHaveLength(3);

      const configInput = documentation.inputs[0];
      expect(configInput.name).toBe('config');
      expect(configInput.type).toBe('object');
      expect(configInput.properties).toBeDefined();
      expect(configInput.properties?.apiKey).toEqual({
        name: 'apiKey',
        type: 'string',
        description: 'API key',
        required: true,
      });

      const tagsInput = documentation.inputs[1];
      expect(tagsInput.name).toBe('tags');
      expect(tagsInput.type).toBe('array');
      expect(tagsInput.items).toEqual({
        name: 'item',
        type: 'string',
        description: 'Tag name',
        required: true,
      });

      const statusInput = documentation.inputs[2];
      expect(statusInput.name).toBe('status');
      expect(statusInput.enum).toEqual(['draft', 'published', 'archived']);
    });

    it('should generate correct example values', () => {
      const mockAgent: Agent = {
        id: 'agent123',
        title: 'Example Agent',
        workflowJson: {
          blocks: [
            {
              type: 'input',
              config: {
                name: 'count',
                type: 'number',
                default: 10,
              },
            },
            {
              type: 'input',
              config: {
                name: 'enabled',
                type: 'boolean',
              },
            },
            {
              type: 'input',
              config: {
                name: 'priority',
                type: 'string',
                enum: ['low', 'medium', 'high'],
              },
            },
          ],
        },
        triggerType: null,
        projectId: 'project123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const documentation = documentationService.generateApiDocumentation(
        mockAgent,
        'http://localhost:3001/api/v1/deploy123'
      );

      expect(documentation.examples.curl).toContain('"count": 10');
      expect(documentation.examples.curl).toContain('"enabled": true');
      expect(documentation.examples.curl).toContain('"priority": "low"');
    });

    it('should handle malformed workflow JSON gracefully', () => {
      const mockAgent: Agent = {
        id: 'agent123',
        title: 'Malformed Agent',
        workflowJson: {
          blocks: null, // Malformed
        },
        triggerType: null,
        projectId: 'project123',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const documentation = documentationService.generateApiDocumentation(
        mockAgent,
        'http://localhost:3001/api/v1/deploy123'
      );

      expect(documentation.inputs).toHaveLength(0);
      expect(documentation.description).toBe('API for Malformed Agent');
    });
  });
});