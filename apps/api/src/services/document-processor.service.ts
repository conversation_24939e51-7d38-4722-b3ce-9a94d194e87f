import { FastifyInstance } from 'fastify'
import { z } from 'zod'
import path from 'path'
import fs from 'fs/promises'
import { DocumentRepository } from '../repositories/document.repository'
import { EmbeddingService, OpenAIEmbeddingService, MockEmbeddingService } from './embedding.service'

export interface ProcessingResult {
  success: boolean
  chunks?: Array<{
    content: string
    embedding?: number[]
    metadata?: Record<string, unknown>
  }>
  error?: string
}

export interface TextExtractor {
  extract(filePath: string): Promise<string>
}

export interface DocumentProcessorConfig {
  chunkSize?: number
  chunkOverlap?: number
  batchSize?: number
}

export class DocumentProcessorService {
  private documentRepository: DocumentRepository
  private extractors: Map<string, TextExtractor> = new Map()
  private embeddingService: EmbeddingService
  private readonly CHUNK_SIZE: number
  private readonly CHUNK_OVERLAP: number
  private readonly BATCH_SIZE: number

  constructor(private server: FastifyInstance, config?: DocumentProcessorConfig) {
    this.documentRepository = new DocumentRepository(server.prisma)
    
    // Initialize configurable parameters with defaults
    this.CHUNK_SIZE = config?.chunkSize ?? 1000
    this.CHUNK_OVERLAP = config?.chunkOverlap ?? 200
    this.BATCH_SIZE = config?.batchSize ?? 20
    
    const openaiApiKey = process.env.OPENAI_API_KEY
    if (openaiApiKey) {
      this.embeddingService = new OpenAIEmbeddingService(openaiApiKey)
    } else {
      this.server.log.warn('OPENAI_API_KEY not found, using mock embedding service')
      this.embeddingService = new MockEmbeddingService()
    }
  }

  async processDocument(documentId: string, userId: string): Promise<ProcessingResult> {
    try {
      const document = await this.documentRepository.findByIdAndUser(documentId, userId)
      if (!document) {
        return { success: false, error: 'Document not found' }
      }

      if (document.status !== 'PENDING') {
        return { success: false, error: 'Document is not in pending status' }
      }

      await this.documentRepository.updateStatus(documentId, 'PROCESSING')

      if (!document.storedFileName) {
        await this.documentRepository.updateStatus(documentId, 'FAILED')
        return { success: false, error: 'Document has no stored file name' }
      }

      const filePath = path.join(
        process.cwd(),
        'uploads',
        document.workspaceId,
        document.storedFileName
      )

      const fileExists = await this.checkFileExists(filePath)
      if (!fileExists) {
        await this.documentRepository.updateStatus(documentId, 'FAILED')
        return { success: false, error: 'File not found on disk' }
      }

      const text = await this.extractText(filePath, document.fileType)
      if (!text) {
        await this.documentRepository.updateStatus(documentId, 'FAILED')
        return { success: false, error: 'Failed to extract text from document' }
      }

      const chunks = this.createChunks(text)
      
      const processedChunks = await this.generateEmbeddings(chunks)

      await this.storeChunks(documentId, processedChunks)

      await this.documentRepository.updateStatus(documentId, 'COMPLETED')

      return {
        success: true,
        chunks: processedChunks
      }
    } catch (error) {
      this.server.log.error({ error, documentId }, 'Document processing failed')
      await this.documentRepository.updateStatus(documentId, 'FAILED').catch(() => {})
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  registerExtractor(fileType: string, extractor: TextExtractor): void {
    this.extractors.set(fileType.toLowerCase(), extractor)
  }

  private async checkFileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  private async extractText(filePath: string, fileType: string): Promise<string> {
    const extractor = this.extractors.get(fileType.toLowerCase())
    if (!extractor) {
      throw new Error(`No extractor registered for file type: ${fileType}`)
    }

    return extractor.extract(filePath)
  }

  private createChunks(text: string): Array<{ content: string; metadata: Record<string, unknown> }> {
    const chunks: Array<{ content: string; metadata: Record<string, unknown> }> = []
    const sentences = this.splitIntoSentences(text)
    
    let currentChunk = ''
    let currentPosition = 0
    let chunkIndex = 0

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > this.CHUNK_SIZE && currentChunk.length > 0) {
        chunks.push({
          content: currentChunk.trim(),
          metadata: {
            position: currentPosition,
            index: chunkIndex,
            length: currentChunk.trim().length
          }
        })
        
        const overlapText = this.getOverlapText(currentChunk)
        currentChunk = overlapText + sentence
        currentPosition += currentChunk.length - overlapText.length
        chunkIndex++
      } else {
        currentChunk += sentence
      }
    }

    if (currentChunk.trim().length > 0) {
      chunks.push({
        content: currentChunk.trim(),
        metadata: {
          position: currentPosition,
          index: chunkIndex,
          length: currentChunk.trim().length
        }
      })
    }

    return chunks
  }

  private splitIntoSentences(text: string): string[] {
    const sentenceRegex = /[.!?]+\s+|[.!?]+$/g
    const sentences: string[] = []
    let lastIndex = 0
    let match

    while ((match = sentenceRegex.exec(text)) !== null) {
      sentences.push(text.slice(lastIndex, match.index + match[0].length))
      lastIndex = match.index + match[0].length
    }

    if (lastIndex < text.length) {
      sentences.push(text.slice(lastIndex))
    }

    return sentences.filter(s => s.trim().length > 0)
  }

  private getOverlapText(chunk: string): string {
    const words = chunk.split(/\s+/)
    const overlapWords = Math.ceil(this.CHUNK_OVERLAP / 5)
    return words.slice(-overlapWords).join(' ') + ' '
  }

  private async generateEmbeddings(
    chunks: Array<{ content: string; metadata: Record<string, unknown> }>
  ): Promise<Array<{ content: string; embedding?: number[]; metadata?: Record<string, unknown> }>> {
    const results: Array<{ content: string; embedding?: number[]; metadata?: Record<string, unknown> }> = []
    
    for (let i = 0; i < chunks.length; i += this.BATCH_SIZE) {
      const batch = chunks.slice(i, i + this.BATCH_SIZE)
      const texts = batch.map(chunk => chunk.content)
      
      try {
        const embeddings = await this.embeddingService.generateEmbeddings(texts)
        
        batch.forEach((chunk, index) => {
          results.push({
            content: chunk.content,
            embedding: embeddings[index],
            metadata: chunk.metadata
          })
        })
      } catch (error) {
        this.server.log.error({ error, batch: i }, 'Failed to generate embeddings for batch')
        
        batch.forEach(chunk => {
          results.push({
            content: chunk.content,
            metadata: chunk.metadata
          })
        })
      }
    }
    
    return results
  }

  private async storeChunks(
    documentId: string,
    chunks: Array<{ content: string; embedding?: number[]; metadata?: Record<string, unknown> }>
  ): Promise<void> {
    const chunkData = chunks.map(chunk => ({
      documentId,
      content: chunk.content,
      embedding: chunk.embedding ? chunk.embedding : undefined,
      metadata: chunk.metadata
    }))

    await this.documentRepository.createChunks(chunkData)
  }
}

export const documentProcessingSchema = z.object({
  documentId: z.string().cuid()
})

export type DocumentProcessingInput = z.infer<typeof documentProcessingSchema>