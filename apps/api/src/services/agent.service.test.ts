import { PrismaClient } from '@prisma/client';
import { FastifyInstance } from 'fastify';
import pino from 'pino';
import { AgentService } from './agent.service';
import { AgentRepository } from '../repositories/agent.repository';
import { GenerationService } from './generation.service';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';

jest.mock('../repositories/agent.repository');
jest.mock('./generation.service');
jest.mock('./document-search.service');

describe('AgentService', () => {
  let prisma: DeepMockProxy<PrismaClient>;
  let agentService: AgentService;
  let mockAgentRepository: jest.Mocked<AgentRepository>;

  beforeEach(() => {
    prisma = mockDeep<PrismaClient>();
    agentService = new AgentService(prisma);
    
    // Get the mocked repository instance
    mockAgentRepository = (agentService as any).agentRepository;
  });

  describe('createAgent', () => {
    it('should create an agent', async () => {
      const createData = {
        title: 'Test Agent',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
      };

      const expectedAgent = {
        id: 'agent-123',
        ...createData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAgentRepository.create.mockResolvedValue(expectedAgent);

      const result = await agentService.createAgent(createData);

      expect(mockAgentRepository.create).toHaveBeenCalledWith({
        title: createData.title,
        workflowJson: createData.workflowJson,
        project: {
          connect: { id: createData.projectId }
        },
        triggerType: createData.triggerType,
      });
      expect(result).toEqual(expectedAgent);
    });
  });

  describe('getAgent', () => {
    it('should get an agent by id', async () => {
      const agentId = 'agent-123';
      const expectedAgent = {
        id: agentId,
        title: 'Test Agent',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAgentRepository.findById.mockResolvedValue(expectedAgent);

      const result = await agentService.getAgent(agentId);

      expect(mockAgentRepository.findById).toHaveBeenCalledWith(agentId);
      expect(result).toEqual(expectedAgent);
    });

    it('should throw error if agent not found', async () => {
      mockAgentRepository.findById.mockResolvedValue(null);

      await expect(agentService.getAgent('non-existent')).rejects.toThrow('Agent not found');
    });
  });

  describe('getAgentsByProject', () => {
    it('should get agents by project id', async () => {
      const projectId = 'project-123';
      const expectedAgents = [
        {
          id: 'agent-1',
          title: 'Agent 1',
          workflowJson: { blocks: [] },
          projectId,
          triggerType: 'api',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockAgentRepository.findByProjectId.mockResolvedValue(expectedAgents);

      const result = await agentService.getAgentsByProject(projectId);

      expect(mockAgentRepository.findByProjectId).toHaveBeenCalledWith(projectId);
      expect(result).toEqual(expectedAgents);
    });
  });

  describe('updateAgent', () => {
    it('should update an agent', async () => {
      const agentId = 'agent-123';
      const updateData = {
        title: 'Updated Title',
      };

      const expectedAgent = {
        id: agentId,
        title: 'Updated Title',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAgentRepository.exists.mockResolvedValue(true);
      mockAgentRepository.update.mockResolvedValue(expectedAgent);

      const result = await agentService.updateAgent(agentId, updateData);

      expect(mockAgentRepository.exists).toHaveBeenCalledWith(agentId);
      expect(mockAgentRepository.update).toHaveBeenCalledWith(agentId, updateData);
      expect(result).toEqual(expectedAgent);
    });

    it('should throw error if agent not found', async () => {
      mockAgentRepository.exists.mockResolvedValue(false);

      await expect(agentService.updateAgent('non-existent', {})).rejects.toThrow('Agent not found');
    });
  });

  describe('deleteAgent', () => {
    it('should delete an agent', async () => {
      const agentId = 'agent-123';
      const deletedAgent = {
        id: agentId,
        title: 'Deleted Agent',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAgentRepository.exists.mockResolvedValue(true);
      mockAgentRepository.delete.mockResolvedValue(deletedAgent);

      const result = await agentService.deleteAgent(agentId);

      expect(mockAgentRepository.exists).toHaveBeenCalledWith(agentId);
      expect(mockAgentRepository.delete).toHaveBeenCalledWith(agentId);
      expect(result).toEqual(deletedAgent);
    });

    it('should throw error if agent not found', async () => {
      mockAgentRepository.exists.mockResolvedValue(false);

      await expect(agentService.deleteAgent('non-existent')).rejects.toThrow('Agent not found');
    });
  });

  describe('checkAgentAccess', () => {
    it('should check if agent belongs to project', async () => {
      mockAgentRepository.belongsToProject.mockResolvedValue(true);

      const result = await agentService.checkAgentAccess('agent-123', 'project-123');

      expect(mockAgentRepository.belongsToProject).toHaveBeenCalledWith('agent-123', 'project-123');
      expect(result).toBe(true);
    });
  });

  describe('executeWorkflow', () => {
    let agentServiceWithDeps: AgentService;
    let mockApp: Partial<FastifyInstance>;
    let mockLogger: pino.Logger;
    let mockGenerationService: jest.Mocked<GenerationService>;

    beforeEach(() => {
      // Create mocks for workflow execution
      mockLogger = pino({ level: 'silent' });
      mockApp = {
        db: prisma,
      } as Partial<FastifyInstance>;

      // Create service with dependencies for workflow execution
      agentServiceWithDeps = new AgentService(prisma, mockApp as FastifyInstance, mockLogger);
      
      // Get mocked generation service
      mockGenerationService = (agentServiceWithDeps as any).generationService;
      mockAgentRepository = (agentServiceWithDeps as any).agentRepository;
    });

    const mockContext = {
      agentId: 'agent-123',
      projectId: 'project-123',
      inputs: {
        name: 'Test User',
        topic: 'AI Workflows',
      },
    };

    const mockAgent = {
      id: 'agent-123',
      projectId: 'project-123',
      title: 'Test Agent',
      workflowJson: [
        {
          id: 'block-1',
          type: 'paragraph',
          content: 'Introduction',
        },
        {
          id: 'block-2',
          type: 'generation',
          props: {
            provider: 'openrouter',
            model: 'openai/gpt-3.5-turbo',
            temperature: 0.7,
          },
          content: [
            { type: 'text', text: 'Generate a summary about @topic for @name' },
          ],
        },
      ],
      triggerType: 'api',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should execute workflow successfully', async () => {
      mockAgentRepository.findById.mockResolvedValue(mockAgent);
      mockAgentRepository.belongsToProject.mockResolvedValue(true);

      const mockGenerationResponse = {
        content: 'Generated summary about AI Workflows for Test User',
        model: 'openai/gpt-3.5-turbo',
        usage: { prompt_tokens: 20, completion_tokens: 30, total_tokens: 50 },
      };

      mockGenerationService.executeGeneration.mockResolvedValue(mockGenerationResponse);

      const result = await agentServiceWithDeps.executeWorkflow(mockContext);

      expect(result).toEqual({
        success: true,
        outputs: {
          'block-1': 'Introduction',
          'block-2': mockGenerationResponse,
        },
      });

      expect(mockGenerationService.executeGeneration).toHaveBeenCalledWith({
        blockId: 'block-2',
        agentId: 'agent-123',
        projectId: 'project-123',
        config: {
          provider: 'openrouter',
          model: 'openai/gpt-3.5-turbo',
          temperature: 0.7,
        },
        prompt: 'Introduction', // Now uses content from blocks above
        context: mockContext.inputs,
      });
    });

    it('should handle unauthorized access', async () => {
      mockAgentRepository.findById.mockResolvedValue(mockAgent);
      mockAgentRepository.belongsToProject.mockResolvedValue(false);

      await expect(agentServiceWithDeps.executeWorkflow(mockContext)).rejects.toThrow(
        'Agent does not belong to the specified project'
      );
    });

    it('should handle block processing errors gracefully', async () => {
      const agentWithMultipleBlocks = {
        ...mockAgent,
        workflowJson: [
          {
            id: 'block-1',
            type: 'paragraph',
            content: 'First prompt',
          },
          {
            id: 'block-2',
            type: 'generation',
            props: { model: 'openai/gpt-3.5-turbo' },
          },
          {
            id: 'block-3',
            type: 'paragraph',
            content: 'Second prompt',
          },
          {
            id: 'block-4',
            type: 'generation',
            props: { model: 'openai/gpt-3.5-turbo' },
          },
        ],
      };

      mockAgentRepository.findById.mockResolvedValue(agentWithMultipleBlocks as any);
      mockAgentRepository.belongsToProject.mockResolvedValue(true);

      // First block succeeds
      mockGenerationService.executeGeneration
        .mockResolvedValueOnce({ content: 'Success', model: 'gpt-3.5-turbo' })
        // Second block fails
        .mockRejectedValueOnce(new Error('Generation failed'));

      const result = await agentServiceWithDeps.executeWorkflow(mockContext);

      expect(result).toEqual({
        success: false,
        outputs: {
          'block-1': 'First prompt',
          'block-2': { content: 'Success', model: 'gpt-3.5-turbo' },
          'block-3': 'Second prompt',
        },
        errors: ['Block block-4: Generation failed'],
      });
    });

    it('should handle missing generation service', async () => {
      // Create service without app and logger
      const basicService = new AgentService(prisma);

      await expect(basicService.executeWorkflow(mockContext)).rejects.toThrow(
        'Agent service not properly initialized for workflow execution'
      );
    });

    it('should replace input variables in prompts', async () => {
      const agentWithVariables = {
        ...mockAgent,
        workflowJson: [
          {
            id: 'block-1',
            type: 'paragraph',
            content: [
              { type: 'text', text: 'Hello @name, tell me about @topic and @KnowledgeBase:guide.pdf' },
            ],
          },
          {
            id: 'block-2',
            type: 'generation',
            props: { model: 'openai/gpt-3.5-turbo' },
          },
        ],
      };

      mockAgentRepository.findById.mockResolvedValue(agentWithVariables as any);
      mockAgentRepository.belongsToProject.mockResolvedValue(true);
      mockGenerationService.executeGeneration.mockResolvedValue({
        content: 'Generated',
        model: 'gpt-3.5-turbo',
      });

      await agentServiceWithDeps.executeWorkflow(mockContext);

      expect(mockGenerationService.executeGeneration).toHaveBeenCalledWith(
        expect.objectContaining({
          prompt: 'Hello Test User, tell me about AI Workflows and @KnowledgeBase:guide.pdf',
        })
      );
    });
  });
});