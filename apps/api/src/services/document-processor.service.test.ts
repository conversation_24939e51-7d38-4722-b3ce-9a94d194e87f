import { FastifyInstance } from 'fastify'
import { DocumentProcessorService, TextExtractor } from './document-processor.service'
import { DocumentRepository } from '../repositories/document.repository'
import fs from 'fs/promises'
import { MockEmbeddingService } from './embedding.service'

jest.mock('fs/promises')
jest.mock('../repositories/document.repository')
jest.mock('./embedding.service')

describe('DocumentProcessorService', () => {
  let service: DocumentProcessorService
  let mockServer: FastifyInstance
  let mockDocumentRepository: jest.Mocked<DocumentRepository>

  beforeEach(() => {
    mockDocumentRepository = {
      findByIdAndUser: jest.fn(),
      updateStatus: jest.fn(),
      createChunks: jest.fn(),
    } as any

    mockServer = {
      log: {
        error: jest.fn(),
        warn: jest.fn(),
      },
      prisma: {},
    } as any

    jest.spyOn(DocumentRepository.prototype, 'findByIdAndUser').mockImplementation(mockDocumentRepository.findByIdAndUser)
    jest.spyOn(DocumentRepository.prototype, 'updateStatus').mockImplementation(mockDocumentRepository.updateStatus)
    jest.spyOn(DocumentRepository.prototype, 'createChunks').mockImplementation(mockDocumentRepository.createChunks)

    ;(MockEmbeddingService as jest.Mock).mockImplementation(() => ({
      generateEmbeddings: jest.fn().mockResolvedValue([
        new Array(384).fill(0.1),
        new Array(384).fill(0.2),
      ])
    }))

    service = new DocumentProcessorService(mockServer)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('processDocument', () => {
    const mockDocument = {
      id: 'doc123',
      workspaceId: 'ws123',
      storedFileName: 'file123.pdf',
      fileType: 'pdf',
      status: 'PENDING',
    }

    it('should successfully process a document', async () => {
      mockDocumentRepository.findByIdAndUser.mockResolvedValue(mockDocument as any)
      mockDocumentRepository.updateStatus.mockResolvedValue({} as any)
      mockDocumentRepository.createChunks.mockResolvedValue()
      
      const mockExtractor: TextExtractor = {
        extract: jest.fn().mockResolvedValue('Sample document text for testing'),
      }
      service.registerExtractor('pdf', mockExtractor)

      ;(fs.access as jest.Mock).mockResolvedValue(undefined)

      const result = await service.processDocument('doc123', 'user123')

      expect(result.success).toBe(true)
      expect(result.chunks).toBeDefined()
      expect(result.chunks?.length).toBeGreaterThan(0)
      expect(mockDocumentRepository.findByIdAndUser).toHaveBeenCalledWith('doc123', 'user123')
      expect(mockDocumentRepository.updateStatus).toHaveBeenCalledWith('doc123', 'PROCESSING')
      expect(mockDocumentRepository.updateStatus).toHaveBeenCalledWith('doc123', 'COMPLETED')
      expect(mockDocumentRepository.createChunks).toHaveBeenCalled()
    })

    it('should return error if document not found', async () => {
      mockDocumentRepository.findByIdAndUser.mockResolvedValue(null)

      const result = await service.processDocument('doc123', 'user123')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Document not found')
      expect(mockDocumentRepository.updateStatus).not.toHaveBeenCalled()
    })

    it('should return error if document not in pending status', async () => {
      mockDocumentRepository.findByIdAndUser.mockResolvedValue({
        ...mockDocument,
        status: 'COMPLETED',
      } as any)

      const result = await service.processDocument('doc123', 'user123')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Document is not in pending status')
    })

    it('should handle file not found on disk', async () => {
      mockDocumentRepository.findByIdAndUser.mockResolvedValue(mockDocument as any)
      mockDocumentRepository.updateStatus.mockResolvedValue({} as any)
      ;(fs.access as jest.Mock).mockRejectedValue(new Error('File not found'))

      const result = await service.processDocument('doc123', 'user123')

      expect(result.success).toBe(false)
      expect(result.error).toBe('File not found on disk')
      expect(mockDocumentRepository.updateStatus).toHaveBeenCalledWith('doc123', 'FAILED')
    })

    it('should handle text extraction failure', async () => {
      mockDocumentRepository.findByIdAndUser.mockResolvedValue(mockDocument as any)
      mockDocumentRepository.updateStatus.mockResolvedValue({} as any)
      ;(fs.access as jest.Mock).mockResolvedValue(undefined)

      const mockExtractor: TextExtractor = {
        extract: jest.fn().mockResolvedValue(''),
      }
      service.registerExtractor('pdf', mockExtractor)

      const result = await service.processDocument('doc123', 'user123')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to extract text from document')
      expect(mockDocumentRepository.updateStatus).toHaveBeenCalledWith('doc123', 'FAILED')
    })

    it('should handle missing extractor for file type', async () => {
      mockDocumentRepository.findByIdAndUser.mockResolvedValue({
        ...mockDocument,
        fileType: 'unknown',
      } as any)
      mockDocumentRepository.updateStatus.mockResolvedValue({} as any)
      ;(fs.access as jest.Mock).mockResolvedValue(undefined)

      const result = await service.processDocument('doc123', 'user123')

      expect(result.success).toBe(false)
      expect(result.error).toBe('No extractor registered for file type: unknown')
      expect(mockDocumentRepository.updateStatus).toHaveBeenCalledWith('doc123', 'FAILED')
    })
  })

  describe('createChunks', () => {
    it('should create chunks with proper overlap', () => {
      const service = new DocumentProcessorService(mockServer)
      const text = 'This is sentence one. This is sentence two. This is sentence three. This is sentence four. This is sentence five.'
      
      const chunks = (service as any).createChunks(text)
      
      expect(chunks.length).toBeGreaterThan(0)
      chunks.forEach((chunk: any, index: number) => {
        expect(chunk.content).toBeTruthy()
        expect(chunk.metadata.index).toBe(index)
        expect(chunk.metadata.position).toBeGreaterThanOrEqual(0)
      })
    })

    it('should handle empty text', () => {
      const service = new DocumentProcessorService(mockServer)
      const chunks = (service as any).createChunks('')
      
      expect(chunks).toEqual([])
    })

    it('should handle text without sentence boundaries', () => {
      const service = new DocumentProcessorService(mockServer)
      const text = 'This is a very long text without any sentence boundaries that should still be chunked properly based on size limits'
      
      const chunks = (service as any).createChunks(text)
      
      expect(chunks.length).toBe(1)
      expect(chunks[0].content).toBe(text)
    })
  })

  describe('splitIntoSentences', () => {
    it('should split text into sentences correctly', () => {
      const service = new DocumentProcessorService(mockServer)
      const text = 'First sentence. Second sentence! Third sentence? Fourth.'
      
      const sentences = (service as any).splitIntoSentences(text)
      
      expect(sentences).toHaveLength(4)
      expect(sentences[0]).toBe('First sentence. ')
      expect(sentences[1]).toBe('Second sentence! ')
      expect(sentences[2]).toBe('Third sentence? ')
      expect(sentences[3]).toBe('Fourth.')
    })

    it('should handle text with no sentence endings', () => {
      const service = new DocumentProcessorService(mockServer)
      const text = 'This is text without sentence endings'
      
      const sentences = (service as any).splitIntoSentences(text)
      
      expect(sentences).toHaveLength(1)
      expect(sentences[0]).toBe(text)
    })
  })
})