import axios, { AxiosInstance } from 'axios';
import pino from 'pino';
import { 
  LLMProvider, 
  OpenRouterRequest, 
  OpenRouterResponse, 
  GenerationResponse 
} from '../types/generation.types';

export class OpenRouterService implements LLMProvider {
  private client: AxiosInstance;
  private logger: pino.Logger;
  private apiKey: string;

  constructor(logger: pino.Logger) {
    this.logger = logger.child({ service: 'OpenRouterService' });
    
    this.apiKey = process.env.OPENROUTER_API_KEY || '';
    if (!this.apiKey) {
      this.logger.warn('OPENROUTER_API_KEY not set');
    }

    this.client = axios.create({
      baseURL: 'https://openrouter.ai/api/v1',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.BETTER_AUTH_URL || 'http://localhost:3000',
        'X-Title': 'sflow AI Workflow Builder',
      },
    });
  }

  async generateCompletion(
    prompt: string,
    model: string,
    temperature: number,
    systemPrompt?: string
  ): Promise<GenerationResponse> {
    try {
      const messages: OpenRouterRequest['messages'] = [];
      
      if (systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt,
        });
      }
      
      messages.push({
        role: 'user',
        content: prompt,
      });

      const request: OpenRouterRequest = {
        model,
        messages,
        temperature,
        stream: false,
      };

      this.logger.info({ model, temperature }, 'Sending request to OpenRouter');

      const response = await this.client.post<OpenRouterResponse>('/chat/completions', request);
      
      const { data } = response;
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No completion choices returned from OpenRouter');
      }

      const result: GenerationResponse = {
        content: data.choices[0].message.content,
        model: data.model,
        usage: data.usage,
      };

      this.logger.info(
        { 
          model: result.model, 
          tokens: result.usage?.total_tokens 
        }, 
        'Successfully generated completion'
      );

      return result;
    } catch (error) {
      this.logger.error({ error, model }, 'Failed to generate completion');
      
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const message = error.response?.data?.error?.message || error.message;
        
        if (status === 401) {
          throw new Error('Invalid OpenRouter API key');
        } else if (status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        } else if (status === 400) {
          throw new Error(`Invalid request: ${message}`);
        }
        
        throw new Error(`OpenRouter API error: ${message}`);
      }
      
      throw error;
    }
  }

  /**
   * Get available models from OpenRouter
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      return response.data.data.map((model: any) => model.id);
    } catch (error) {
      this.logger.error({ error }, 'Failed to fetch available models');
      // Return default models if API call fails
      return [
        'openai/gpt-3.5-turbo',
        'openai/gpt-4',
        'openai/gpt-4-turbo',
        'anthropic/claude-2',
        'anthropic/claude-instant-1',
        'google/gemini-pro',
        'meta-llama/llama-2-70b-chat',
        'mistralai/mistral-7b-instruct',
      ];
    }
  }
}