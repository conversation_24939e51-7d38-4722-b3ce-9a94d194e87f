import axios from 'axios';
import pino from 'pino';
import { OpenRouterService } from './openrouter.service';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('OpenRouterService', () => {
  let openRouterService: OpenRouterService;
  let mockLogger: pino.Logger;
  let mockAxiosInstance: any;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    
    // Set up environment variable
    process.env.OPENROUTER_API_KEY = 'test-api-key';
    process.env.BETTER_AUTH_URL = 'http://localhost:3001';

    // Create mock logger
    mockLogger = pino({ level: 'silent' });

    // Create mock axios instance
    mockAxiosInstance = {
      post: jest.fn(),
      get: jest.fn(),
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    // Create service
    openRouterService = new OpenRouterService(mockLogger);
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.OPENROUTER_API_KEY;
    delete process.env.BETTER_AUTH_URL;
  });

  describe('constructor', () => {
    it('should initialize with API key from environment', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'https://openrouter.ai/api/v1',
        headers: {
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json',
          'HTTP-Referer': 'http://localhost:3001',
          'X-Title': 'sflow AI Workflow Builder',
        },
      });
    });

    it('should log warning when API key is not set', () => {
      delete process.env.OPENROUTER_API_KEY;
      const childLogger = { warn: jest.fn() };
      mockLogger.child = jest.fn().mockReturnValue(childLogger);
      
      new OpenRouterService(mockLogger);
      
      expect(childLogger.warn).toHaveBeenCalledWith('OPENROUTER_API_KEY not set');
    });
  });

  describe('generateCompletion', () => {
    const mockPrompt = 'Generate a test response';
    const mockModel = 'openai/gpt-3.5-turbo';
    const mockTemperature = 0.7;

    it('should successfully generate completion', async () => {
      const mockApiResponse = {
        data: {
          id: 'completion-123',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: 'Generated test response',
              },
              finish_reason: 'stop',
            },
          ],
          model: mockModel,
          usage: {
            prompt_tokens: 10,
            completion_tokens: 5,
            total_tokens: 15,
          },
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockApiResponse);

      const result = await openRouterService.generateCompletion(
        mockPrompt,
        mockModel,
        mockTemperature
      );

      expect(result).toEqual({
        content: 'Generated test response',
        model: mockModel,
        usage: {
          prompt_tokens: 10,
          completion_tokens: 5,
          total_tokens: 15,
        },
      });

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/chat/completions', {
        model: mockModel,
        messages: [
          {
            role: 'user',
            content: mockPrompt,
          },
        ],
        temperature: mockTemperature,
        stream: false,
      });
    });

    it('should include system prompt when provided', async () => {
      const mockSystemPrompt = 'You are a helpful assistant';
      const mockApiResponse = {
        data: {
          choices: [
            {
              message: {
                content: 'Response with system prompt',
              },
            },
          ],
          model: mockModel,
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockApiResponse);

      await openRouterService.generateCompletion(
        mockPrompt,
        mockModel,
        mockTemperature,
        mockSystemPrompt
      );

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/chat/completions', {
        model: mockModel,
        messages: [
          {
            role: 'system',
            content: mockSystemPrompt,
          },
          {
            role: 'user',
            content: mockPrompt,
          },
        ],
        temperature: mockTemperature,
        stream: false,
      });
    });

    it('should handle empty choices array', async () => {
      const mockApiResponse = {
        data: {
          choices: [],
          model: mockModel,
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockApiResponse);

      await expect(
        openRouterService.generateCompletion(mockPrompt, mockModel, mockTemperature)
      ).rejects.toThrow('No completion choices returned from OpenRouter');
    });

    it('should handle 401 unauthorized error', async () => {
      const mockError = {
        response: {
          status: 401,
          data: {
            error: {
              message: 'Invalid API key',
            },
          },
        },
        isAxiosError: true,
      };

      mockAxiosInstance.post.mockRejectedValue(mockError);
      jest.spyOn(axios, 'isAxiosError').mockReturnValue(true);

      await expect(
        openRouterService.generateCompletion(mockPrompt, mockModel, mockTemperature)
      ).rejects.toThrow('Invalid OpenRouter API key');
    });

    it('should handle 429 rate limit error', async () => {
      const mockError = {
        response: {
          status: 429,
        },
        isAxiosError: true,
      };

      mockAxiosInstance.post.mockRejectedValue(mockError);
      jest.spyOn(axios, 'isAxiosError').mockReturnValue(true);

      await expect(
        openRouterService.generateCompletion(mockPrompt, mockModel, mockTemperature)
      ).rejects.toThrow('Rate limit exceeded. Please try again later.');
    });

    it('should handle 400 bad request error', async () => {
      const mockError = {
        response: {
          status: 400,
          data: {
            error: {
              message: 'Invalid model specified',
            },
          },
        },
        isAxiosError: true,
      };

      mockAxiosInstance.post.mockRejectedValue(mockError);
      jest.spyOn(axios, 'isAxiosError').mockReturnValue(true);

      await expect(
        openRouterService.generateCompletion(mockPrompt, mockModel, mockTemperature)
      ).rejects.toThrow('Invalid request: Invalid model specified');
    });

    it('should handle generic axios errors', async () => {
      const mockError = {
        message: 'Network error',
        isAxiosError: true,
      };

      mockAxiosInstance.post.mockRejectedValue(mockError);
      jest.spyOn(axios, 'isAxiosError').mockReturnValue(true);

      await expect(
        openRouterService.generateCompletion(mockPrompt, mockModel, mockTemperature)
      ).rejects.toThrow('OpenRouter API error: Network error');
    });

    it('should handle non-axios errors', async () => {
      const mockError = new Error('Unexpected error');

      mockAxiosInstance.post.mockRejectedValue(mockError);
      jest.spyOn(axios, 'isAxiosError').mockReturnValue(false);

      await expect(
        openRouterService.generateCompletion(mockPrompt, mockModel, mockTemperature)
      ).rejects.toThrow('Unexpected error');
    });
  });

  describe('getAvailableModels', () => {
    it('should fetch available models from API', async () => {
      const mockApiResponse = {
        data: {
          data: [
            { id: 'openai/gpt-4' },
            { id: 'anthropic/claude-2' },
            { id: 'google/gemini-pro' },
          ],
        },
      };

      mockAxiosInstance.get.mockResolvedValue(mockApiResponse);

      const models = await openRouterService.getAvailableModels();

      expect(models).toEqual([
        'openai/gpt-4',
        'anthropic/claude-2',
        'google/gemini-pro',
      ]);

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/models');
    });

    it('should return default models on API error', async () => {
      mockAxiosInstance.get.mockRejectedValue(new Error('API error'));

      const models = await openRouterService.getAvailableModels();

      expect(models).toEqual([
        'openai/gpt-3.5-turbo',
        'openai/gpt-4',
        'openai/gpt-4-turbo',
        'anthropic/claude-2',
        'anthropic/claude-instant-1',
        'google/gemini-pro',
        'meta-llama/llama-2-70b-chat',
        'mistralai/mistral-7b-instruct',
      ]);
    });
  });
});