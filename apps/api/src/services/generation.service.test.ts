import { FastifyInstance } from 'fastify';
import { PrismaClient } from '@prisma/client';
import pino from 'pino';
import { GenerationService } from './generation.service';
import { DocumentSearchService } from './document-search.service';
import { OpenRouterService } from './openrouter.service';
import { DocumentRepository } from '../repositories/document.repository';
import { GenerationRequest } from '../types/generation.types';

// Mock dependencies
jest.mock('./document-search.service');
jest.mock('./openrouter.service');
jest.mock('../repositories/document.repository');

describe('GenerationService', () => {
  let generationService: GenerationService;
  let mockApp: Partial<FastifyInstance>;
  let mockLogger: pino.Logger;
  let mockDocumentSearchService: jest.Mocked<DocumentSearchService>;
  let mockOpenRouterService: jest.Mocked<OpenRouterService>;
  let mockDocumentRepository: jest.Mocked<DocumentRepository>;

  beforeEach(() => {
    // Create mock logger
    mockLogger = pino({ level: 'silent' });

    // Create mock Fastify app
    mockApp = {
      prisma: {} as PrismaClient,
    };

    // Create mocked services
    mockDocumentSearchService = new DocumentSearchService(mockApp as FastifyInstance, mockLogger) as jest.Mocked<DocumentSearchService>;
    
    // Create the generation service
    generationService = new GenerationService(
      mockApp as FastifyInstance,
      mockLogger,
      mockDocumentSearchService
    );

    // Access private properties for mocking
    mockOpenRouterService = (generationService as any).openRouterService;
    mockDocumentRepository = (generationService as any).documentRepository;
  });

  describe('parseKnowledgeBaseReferences', () => {
    it('should parse single knowledge base reference', () => {
      const prompt = 'Tell me about @KnowledgeBase:guidelines.pdf';
      const references = generationService.parseKnowledgeBaseReferences(prompt);

      expect(references).toHaveLength(1);
      expect(references[0]).toEqual({
        documentName: 'guidelines.pdf',
      });
    });

    it('should parse multiple knowledge base references', () => {
      const prompt = 'Compare @KnowledgeBase:doc1.pdf and @KnowledgeBase:doc2.txt';
      const references = generationService.parseKnowledgeBaseReferences(prompt);

      expect(references).toHaveLength(2);
      expect(references[0]).toEqual({ documentName: 'doc1.pdf' });
      expect(references[1]).toEqual({ documentName: 'doc2.txt' });
    });

    it('should return empty array when no references found', () => {
      const prompt = 'Tell me about AI workflows';
      const references = generationService.parseKnowledgeBaseReferences(prompt);

      expect(references).toHaveLength(0);
    });

    it('should handle references with special characters', () => {
      const prompt = 'Check @KnowledgeBase:my-file_2023.v1.pdf';
      const references = generationService.parseKnowledgeBaseReferences(prompt);

      expect(references).toHaveLength(1);
      expect(references[0]).toEqual({
        documentName: 'my-file_2023.v1.pdf',
      });
    });
  });

  describe('executeGeneration', () => {
    const mockRequest: GenerationRequest = {
      blockId: 'block-123',
      projectId: 'project-123',
      agentId: 'agent-123',
      config: {
        provider: 'openrouter',
        model: 'openai/gpt-3.5-turbo',
        temperature: 0.7,
      },
      prompt: 'Generate a summary',
    };

    it('should execute generation without knowledge base references', async () => {
      const mockResponse = {
        content: 'Generated summary content',
        model: 'openai/gpt-3.5-turbo',
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30,
        },
      };

      mockOpenRouterService.generateCompletion.mockResolvedValue(mockResponse);

      const result = await generationService.executeGeneration(mockRequest);

      expect(result).toEqual(mockResponse);
      expect(mockOpenRouterService.generateCompletion).toHaveBeenCalledWith(
        'Generate a summary',
        'openai/gpt-3.5-turbo',
        0.7
      );
    });

    it('should execute generation with knowledge base references', async () => {
      const requestWithKB: GenerationRequest = {
        ...mockRequest,
        prompt: 'Summarize @KnowledgeBase:guidelines.pdf',
      };

      const mockDocument = {
        id: 'doc-123',
        fileName: 'guidelines.pdf',
        workspaceId: 'workspace-123',
        status: 'COMPLETED',
      };

      const mockSearchResults = [
        {
          id: 'chunk-1',
          documentId: 'doc-123',
          content: 'Guideline content 1',
          similarity: 0.9,
        },
        {
          id: 'chunk-2',
          documentId: 'doc-123',
          content: 'Guideline content 2',
          similarity: 0.85,
        },
      ];

      mockDocumentRepository.findByFileName.mockResolvedValue(mockDocument as any);
      mockDocumentSearchService.searchWithinDocument.mockResolvedValue(mockSearchResults as any);

      const mockResponse = {
        content: 'Generated summary with context',
        model: 'openai/gpt-3.5-turbo',
        usage: {
          prompt_tokens: 50,
          completion_tokens: 30,
          total_tokens: 80,
        },
      };

      mockOpenRouterService.generateCompletion.mockResolvedValue(mockResponse);

      const result = await generationService.executeGeneration(requestWithKB);

      expect(result).toEqual(mockResponse);
      expect(mockDocumentRepository.findByFileName).toHaveBeenCalledWith('guidelines.pdf', 'project-123');
      expect(mockDocumentSearchService.searchWithinDocument).toHaveBeenCalled();
      
      // Check that the prompt was modified to include context
      const actualPrompt = mockOpenRouterService.generateCompletion.mock.calls[0][0];
      expect(actualPrompt).toContain('Based on the following context');
      expect(actualPrompt).toContain('Guideline content 1');
      expect(actualPrompt).toContain('Guideline content 2');
    });

    it('should handle missing referenced documents gracefully', async () => {
      const requestWithKB: GenerationRequest = {
        ...mockRequest,
        prompt: 'Summarize @KnowledgeBase:missing.pdf',
      };

      mockDocumentRepository.findByFileName.mockResolvedValue(null);

      const mockResponse = {
        content: 'Generated without context',
        model: 'openai/gpt-3.5-turbo',
      };

      mockOpenRouterService.generateCompletion.mockResolvedValue(mockResponse);

      const result = await generationService.executeGeneration(requestWithKB);

      expect(result).toEqual(mockResponse);
      expect(mockDocumentSearchService.searchWithinDocument).not.toHaveBeenCalled();
    });

    it('should handle generation errors', async () => {
      mockOpenRouterService.generateCompletion.mockRejectedValue(new Error('API error'));

      await expect(generationService.executeGeneration(mockRequest)).rejects.toThrow('API error');
    });
  });
});