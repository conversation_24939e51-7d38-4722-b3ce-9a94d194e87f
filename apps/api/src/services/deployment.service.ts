import type { FastifyInstance } from 'fastify';
import { DeploymentRepository } from '../repositories/deployment.repository.js';
import { AgentRepository } from '../repositories/agent.repository.js';
import { randomBytes } from 'crypto';
import type { Deployment } from '@prisma/client';
import type { DeploymentWithRelations } from '../repositories/deployment.repository.js';

export class DeploymentService {
  private deploymentRepository: DeploymentRepository;
  private agentRepository: AgentRepository;

  constructor(private app: FastifyInstance) {
    this.deploymentRepository = new DeploymentRepository(app);
    this.agentRepository = new AgentRepository(app.prisma);
  }

  /**
   * Verify user has access to a project through workspace membership
   */
  private async verifyUserAccess(projectId: string, userId: string): Promise<void> {
    const project = await this.app.prisma.project.findUnique({
      where: { id: projectId },
      include: {
        workspace: {
          include: {
            members: true,
          },
        },
      },
    });

    if (!project) {
      throw new Error('Project not found');
    }

    const hasAccess = project.workspace.members.some(
      (member) => member.userId === userId
    );

    if (!hasAccess) {
      throw new Error('Unauthorized: User does not have access to this resource');
    }
  }

  private generateApiKey(): string {
    return `sk_${randomBytes(32).toString('base64url')}`;
  }

  private generateDeploymentUrl(deploymentId: string): string {
    const baseUrl = process.env.API_URL || 'http://localhost:3001';
    return `${baseUrl}/api/v1/${deploymentId}`;
  }

  async createDeployment(agentId: string, userId: string): Promise<Deployment> {
    const agent = await this.agentRepository.findById(agentId);
    if (!agent) {
      throw new Error('Agent not found');
    }

    await this.verifyUserAccess(agent.projectId, userId);

    const apiKey = this.generateApiKey();
    
    const deployment = await this.deploymentRepository.create({
      agent: {
        connect: { id: agentId },
      },
      deploymentUrl: '', // Temporary, will update after creation
      apiKey,
      status: 'ACTIVE',
    });

    const deploymentUrl = this.generateDeploymentUrl(deployment.id);
    
    return this.app.prisma.deployment.update({
      where: { id: deployment.id },
      data: { deploymentUrl },
    });
  }

  async getDeployment(id: string): Promise<DeploymentWithRelations | null> {
    return this.deploymentRepository.findById(id);
  }

  async getDeploymentByApiKey(apiKey: string): Promise<DeploymentWithRelations | null> {
    return this.deploymentRepository.findByApiKey(apiKey);
  }

  async getDeploymentsByAgent(agentId: string): Promise<Deployment[]> {
    return this.deploymentRepository.findByAgentId(agentId);
  }

  async updateDeploymentStatus(
    id: string,
    status: 'ACTIVE' | 'INACTIVE',
    userId: string
  ): Promise<Deployment> {
    const deployment = await this.deploymentRepository.findById(id);
    if (!deployment) {
      throw new Error('Deployment not found');
    }

    await this.verifyUserAccess(deployment.agent.project.id, userId);

    return this.deploymentRepository.updateStatus(id, status);
  }

  async deleteDeployment(id: string, userId: string): Promise<void> {
    const deployment = await this.deploymentRepository.findById(id);
    if (!deployment) {
      throw new Error('Deployment not found');
    }

    await this.verifyUserAccess(deployment.agent.project.id, userId);

    await this.deploymentRepository.delete(id);
  }
}