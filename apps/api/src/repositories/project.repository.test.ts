import { describe, it, expect, beforeEach, vi } from 'vitest'
import { PrismaClient } from '@prisma/client'
import { ProjectRepository } from './project.repository'
import { mockDeep, DeepMockProxy } from 'jest-mock-extended'

describe('ProjectRepository', () => {
  let prisma: DeepMockProxy<PrismaClient>
  let repository: ProjectRepository

  beforeEach(() => {
    prisma = mockDeep<PrismaClient>()
    repository = new ProjectRepository(prisma)
  })

  describe('findByUserId', () => {
    it('should return projects for a given user ID', async () => {
      const userId = 'user123'
      const mockProjects = [
        {
          id: 'proj1',
          name: 'Project 1',
          description: 'Description 1',
          accessType: 'PERSONAL',
          userId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'proj2',
          name: 'Project 2',
          description: null,
          accessType: 'COMPANY_WIDE',
          userId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]

      prisma.project.findMany.mockResolvedValue(mockProjects)

      const result = await repository.findByUserId(userId)

      expect(prisma.project.findMany).toHaveBeenCalledWith({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      })
      expect(result).toEqual(mockProjects)
    })
  })

  describe('findByIdAndUserId', () => {
    it('should return a project when found', async () => {
      const projectId = 'proj1'
      const userId = 'user123'
      const mockProject = {
        id: projectId,
        name: 'Project 1',
        description: 'Description 1',
        accessType: 'PERSONAL' as const,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      prisma.project.findFirst.mockResolvedValue(mockProject)

      const result = await repository.findByIdAndUserId(projectId, userId)

      expect(prisma.project.findFirst).toHaveBeenCalledWith({
        where: {
          id: projectId,
          userId,
        },
      })
      expect(result).toEqual(mockProject)
    })

    it('should return null when project not found', async () => {
      const projectId = 'proj1'
      const userId = 'user123'

      prisma.project.findFirst.mockResolvedValue(null)

      const result = await repository.findByIdAndUserId(projectId, userId)

      expect(result).toBeNull()
    })
  })

  describe('create', () => {
    it('should create a project with default accessType', async () => {
      const input = {
        name: 'New Project',
        description: 'New Description',
        userId: 'user123',
      }
      const mockProject = {
        id: 'proj1',
        ...input,
        accessType: 'PERSONAL' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      prisma.project.create.mockResolvedValue(mockProject)

      const result = await repository.create(input)

      expect(prisma.project.create).toHaveBeenCalledWith({
        data: {
          name: input.name,
          description: input.description,
          accessType: 'PERSONAL',
          userId: input.userId,
        },
      })
      expect(result).toEqual(mockProject)
    })

    it('should create a project with specified accessType', async () => {
      const input = {
        name: 'New Project',
        userId: 'user123',
        accessType: 'COMPANY_WIDE' as const,
      }
      const mockProject = {
        id: 'proj1',
        ...input,
        description: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      prisma.project.create.mockResolvedValue(mockProject)

      const result = await repository.create(input)

      expect(prisma.project.create).toHaveBeenCalledWith({
        data: {
          name: input.name,
          description: undefined,
          accessType: 'COMPANY_WIDE',
          userId: input.userId,
        },
      })
      expect(result).toEqual(mockProject)
    })
  })

  describe('updateByIdAndUserId', () => {
    it('should update a project successfully', async () => {
      const projectId = 'proj1'
      const userId = 'user123'
      const updateData = {
        name: 'Updated Name',
        description: 'Updated Description',
      }
      const mockUpdatedProject = {
        id: projectId,
        ...updateData,
        accessType: 'PERSONAL' as const,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      prisma.project.updateMany.mockResolvedValue({ count: 1 })
      prisma.project.findUnique.mockResolvedValue(mockUpdatedProject)

      const result = await repository.updateByIdAndUserId(projectId, userId, updateData)

      expect(prisma.project.updateMany).toHaveBeenCalledWith({
        where: {
          id: projectId,
          userId,
        },
        data: updateData,
      })
      expect(prisma.project.findUnique).toHaveBeenCalledWith({
        where: { id: projectId },
      })
      expect(result).toEqual(mockUpdatedProject)
    })

    it('should return null when project not found', async () => {
      const projectId = 'proj1'
      const userId = 'user123'
      const updateData = {
        name: 'Updated Name',
      }

      prisma.project.updateMany.mockResolvedValue({ count: 0 })

      const result = await repository.updateByIdAndUserId(projectId, userId, updateData)

      expect(prisma.project.updateMany).toHaveBeenCalledWith({
        where: {
          id: projectId,
          userId,
        },
        data: updateData,
      })
      expect(prisma.project.findUnique).not.toHaveBeenCalled()
      expect(result).toBeNull()
    })
  })

  describe('deleteByIdAndUserId', () => {
    it('should delete a project and related agents in a transaction', async () => {
      const projectId = 'proj1'
      const userId = 'user123'
      const mockTx = {
        agent: {
          deleteMany: vi.fn().mockResolvedValue({ count: 2 }),
        },
        project: {
          deleteMany: vi.fn().mockResolvedValue({ count: 1 }),
        },
      }

      prisma.$transaction.mockImplementation(async (callback) => {
        return callback(mockTx)
      })

      const result = await repository.deleteByIdAndUserId(projectId, userId)

      expect(prisma.$transaction).toHaveBeenCalled()
      expect(mockTx.agent.deleteMany).toHaveBeenCalledWith({
        where: { projectId },
      })
      expect(mockTx.project.deleteMany).toHaveBeenCalledWith({
        where: {
          id: projectId,
          userId,
        },
      })
      expect(result).toBe(true)
    })

    it('should return false when project not found in transaction', async () => {
      const projectId = 'proj1'
      const userId = 'user123'
      const mockTx = {
        agent: {
          deleteMany: vi.fn().mockResolvedValue({ count: 0 }),
        },
        project: {
          deleteMany: vi.fn().mockResolvedValue({ count: 0 }),
        },
      }

      prisma.$transaction.mockImplementation(async (callback) => {
        return callback(mockTx)
      })

      const result = await repository.deleteByIdAndUserId(projectId, userId)

      expect(result).toBe(false)
    })
  })

  describe('exists', () => {
    it('should return true when project exists', async () => {
      const projectId = 'proj1'
      const userId = 'user123'

      prisma.project.count.mockResolvedValue(1)

      const result = await repository.exists(projectId, userId)

      expect(prisma.project.count).toHaveBeenCalledWith({
        where: {
          id: projectId,
          userId,
        },
      })
      expect(result).toBe(true)
    })

    it('should return false when project does not exist', async () => {
      const projectId = 'proj1'
      const userId = 'user123'

      prisma.project.count.mockResolvedValue(0)

      const result = await repository.exists(projectId, userId)

      expect(result).toBe(false)
    })
  })
})