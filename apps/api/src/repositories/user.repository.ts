import { User } from '@prisma/client'
import { BaseRepository } from './base.repository'

export class UserRepository extends BaseRepository {
  async findById(id: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id }
    })
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email }
    })
  }

  async create(data: { email: string; name?: string }): Promise<User> {
    return this.prisma.user.create({
      data
    })
  }

  async update(id: string, data: Partial<User>): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data
    })
  }

  async updateProfile(id: string, data: { name?: string; phone?: string; country?: string }): Promise<User> {
    // Sanitize input data - only allow specific fields to be updated
    const sanitizedData = {
      ...(data.name !== undefined && { name: data.name }),
      ...(data.phone !== undefined && { phone: data.phone }),
      ...(data.country !== undefined && { country: data.country }),
      // Mark onboarding as completed when all required fields are provided
      ...(data.name && data.phone && data.country && { onboardingCompleted: true })
    }
    
    return this.prisma.user.update({
      where: { id },
      data: sanitizedData
    })
  }

  async delete(id: string): Promise<User> {
    return this.prisma.user.delete({
      where: { id }
    })
  }
}