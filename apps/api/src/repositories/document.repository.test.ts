import { documentRepository } from './document.repository';

// Mock prisma at the module level
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    knowledgeBaseDocument: {
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    },
    projectDocuments: {
      findMany: jest.fn(),
      count: jest.fn(),
      deleteMany: jest.fn(),
      createMany: jest.fn()
    },
    $transaction: jest.fn(),
    $queryRawUnsafe: jest.fn()
  }
}));

// Import mocked prisma after mocking
import prisma from '../lib/prisma';

describe('documentRepository', () => {
  const testWorkspaceId = 'test-workspace-1';
  const testProjectId = 'test-project-1';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('findByWorkspace', () => {
    it('should return paginated completed documents', async () => {
      const mockDocs = [
        {
          id: 'doc1',
          workspaceId: testWorkspaceId,
          fileName: 'test1.pdf',
          fileType: 'application/pdf',
          fileSize: 1024,
          status: 'COMPLETED',
          uploadedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'doc2',
          workspaceId: testWorkspaceId,
          fileName: 'test2.md',
          fileType: 'text/markdown',
          fileSize: 512,
          status: 'COMPLETED',
          uploadedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      (prisma.$transaction as jest.Mock).mockResolvedValue([mockDocs, 2]);

      const result = await documentRepository.findByWorkspace(testWorkspaceId, 10, 0);
      
      expect(result.documents).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.limit).toBe(10);
      expect(result.offset).toBe(0);
      expect(result.documents.every(d => d.status === 'COMPLETED')).toBe(true);
    });

    it('should handle pagination without params', async () => {
      const mockDocs = [{ id: 'doc1', status: 'COMPLETED' }];
      (prisma.$transaction as jest.Mock).mockResolvedValue([mockDocs, 1]);

      const result = await documentRepository.findByWorkspace(testWorkspaceId);
      
      expect(result.limit).toBe(1);
      expect(result.offset).toBe(0);
    });
  });

  describe('findByProject', () => {
    it('should return empty result for project with no documents', async () => {
      (prisma.$transaction as jest.Mock).mockResolvedValue([[], 0]);

      const result = await documentRepository.findByProject(testProjectId);
      
      expect(result.documents).toEqual([]);
      expect(result.total).toBe(0);
    });

    it('should return paginated associated documents', async () => {
      const mockProjectDocs = [
        {
          projectId: testProjectId,
          documentId: 'doc1',
          document: {
            id: 'doc1',
            workspaceId: testWorkspaceId,
            fileName: 'test1.pdf',
            fileType: 'application/pdf',
            fileSize: 1024,
            status: 'COMPLETED',
            uploadedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          }
        },
        {
          projectId: testProjectId,
          documentId: 'doc2',
          document: {
            id: 'doc2',
            workspaceId: testWorkspaceId,
            fileName: 'test2.md',
            fileType: 'text/markdown',
            fileSize: 512,
            status: 'COMPLETED',
            uploadedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          }
        }
      ];

      (prisma.$transaction as jest.Mock).mockResolvedValue([mockProjectDocs, 2]);

      const result = await documentRepository.findByProject(testProjectId, 10, 0);
      
      expect(result.documents).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.limit).toBe(10);
      expect(result.offset).toBe(0);
      expect(result.documents[0].id).toBe('doc1');
      expect(result.documents[1].id).toBe('doc2');
    });
  });

  describe('findAvailableForProject', () => {
    it('should return paginated documents with association status using optimized query', async () => {
      const mockRows = [
        {
          id: 'doc1',
          workspaceId: testWorkspaceId,
          fileName: 'test1.pdf',
          fileType: 'application/pdf',
          fileSize: 1024,
          status: 'COMPLETED',
          uploadedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          isAssociated: true
        },
        {
          id: 'doc2',
          workspaceId: testWorkspaceId,
          fileName: 'test2.md',
          fileType: 'text/markdown',
          fileSize: 512,
          status: 'COMPLETED',
          uploadedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          isAssociated: false
        }
      ];

      const countResult = [{ total: 2n }];

      (prisma.$transaction as jest.Mock).mockResolvedValue([mockRows, countResult]);
      (prisma.$queryRawUnsafe as jest.Mock) = jest.fn();

      const result = await documentRepository.findAvailableForProject(testProjectId, testWorkspaceId, 10, 0);
      
      expect(result.documents).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.limit).toBe(10);
      expect(result.offset).toBe(0);
      
      const associated = result.documents.find(r => r.document.id === 'doc1');
      expect(associated?.isAssociated).toBe(true);
      
      const notAssociated = result.documents.find(r => r.document.id === 'doc2');
      expect(notAssociated?.isAssociated).toBe(false);
    });
  });

  describe('updateProjectDocuments', () => {
    it('should replace all associations', async () => {
      const newDocIds = ['doc2', 'doc3'];
      const updatedProjectDocs = newDocIds.map(id => ({
        projectId: testProjectId,
        documentId: id,
        document: {
          id,
          workspaceId: testWorkspaceId,
          fileName: `test${id}.pdf`,
          fileType: 'application/pdf',
          fileSize: 1024,
          status: 'COMPLETED',
          uploadedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      }));

      (prisma.$transaction as jest.Mock).mockImplementation(async (callback: any) => {
        const tx = {
          projectDocuments: {
            deleteMany: jest.fn(),
            createMany: jest.fn(),
            findMany: jest.fn().mockResolvedValue(updatedProjectDocs)
          }
        };
        return callback(tx);
      });

      const updated = await documentRepository.updateProjectDocuments(testProjectId, newDocIds);
      
      expect(updated).toHaveLength(2);
      expect(updated[0].id).toBe('doc2');
      expect(updated[1].id).toBe('doc3');
    });

    it('should handle empty document list', async () => {
      (prisma.$transaction as jest.Mock).mockImplementation(async (callback: any) => {
        const tx = {
          projectDocuments: {
            deleteMany: jest.fn(),
            createMany: jest.fn(),
            findMany: jest.fn().mockResolvedValue([])
          }
        };
        return callback(tx);
      });

      const updated = await documentRepository.updateProjectDocuments(testProjectId, []);
      
      expect(updated).toEqual([]);
    });
  });

  describe('create', () => {
    it('should create a new document', async () => {
      const documentData = {
        workspaceId: 'workspace-123',
        fileName: 'test.pdf',
        storedFileName: 'unique-123.pdf',
        fileType: 'application/pdf',
        fileSize: 1024,
        status: 'PENDING' as const
      };

      const expectedDocument = {
        id: 'doc-123',
        ...documentData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (prisma.knowledgeBaseDocument.create as jest.Mock).mockResolvedValue(expectedDocument);

      const result = await documentRepository.create(documentData);

      expect(prisma.knowledgeBaseDocument.create).toHaveBeenCalledWith({
        data: documentData
      });
      expect(result).toEqual(expectedDocument);
    });
  });

  describe('update', () => {
    it('should update a document', async () => {
      const documentId = 'doc-123';
      const updateData = {
        status: 'COMPLETED' as const
      };

      const expectedDocument = {
        id: documentId,
        status: 'COMPLETED',
        updatedAt: new Date()
      };

      (prisma.knowledgeBaseDocument.update as jest.Mock).mockResolvedValue(expectedDocument);

      const result = await documentRepository.update(documentId, updateData);

      expect(prisma.knowledgeBaseDocument.update).toHaveBeenCalledWith({
        where: { id: documentId },
        data: updateData
      });
      expect(result).toEqual(expectedDocument);
    });
  });

  describe('delete', () => {
    it('should delete a document', async () => {
      const documentId = 'doc-123';
      const deletedDocument = {
        id: documentId,
        fileName: 'test.pdf'
      };

      (prisma.knowledgeBaseDocument.delete as jest.Mock).mockResolvedValue(deletedDocument);

      const result = await documentRepository.delete(documentId);

      expect(prisma.knowledgeBaseDocument.delete).toHaveBeenCalledWith({
        where: { id: documentId }
      });
      expect(result).toEqual(deletedDocument);
    });
  });
});