import { Prisma, KnowledgeBaseDocument, DocumentChunk, DocumentStatus } from '@prisma/client';
import { BaseRepository } from './base.repository';
import prisma from '../lib/prisma';

export class DocumentRepository extends BaseRepository {
  async findByWorkspace(workspaceId: string, limit?: number, offset?: number) {
    const [documents, total] = await this.prisma.$transaction([
      this.prisma.knowledgeBaseDocument.findMany({
        where: {
          workspaceId,
          status: 'COMPLETED'
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        skip: offset
      }),
      this.prisma.knowledgeBaseDocument.count({
        where: {
          workspaceId,
          status: 'COMPLETED'
        }
      })
    ]);

    return {
      documents,
      total,
      limit: limit || documents.length,
      offset: offset || 0
    };
  }

  async findByProject(projectId: string, limit?: number, offset?: number) {
    const [projectDocs, total] = await this.prisma.$transaction([
      this.prisma.projectDocuments.findMany({
        where: {
          projectId
        },
        include: {
          document: true
        },
        orderBy: {
          document: {
            createdAt: 'desc'
          }
        },
        take: limit,
        skip: offset
      }),
      this.prisma.projectDocuments.count({
        where: {
          projectId
        }
      })
    ]);

    return {
      documents: projectDocs.map(pd => pd.document),
      total,
      limit: limit || projectDocs.length,
      offset: offset || 0
    };
  }

  async findAvailableForProject(projectId: string, workspaceId: string, limit?: number, offset?: number, search?: string) {
    // Use raw SQL for optimized LEFT JOIN query
    const searchCondition = search ? `AND d."fileName" ILIKE $4` : '';
    const searchParams = search ? [`%${search}%`] : [];
    
    const query = `
      SELECT 
        d.*,
        CASE WHEN pd."documentId" IS NOT NULL THEN true ELSE false END as "isAssociated"
      FROM "knowledge_base_document" d
      LEFT JOIN "project_documents" pd ON d.id = pd."documentId" AND pd."projectId" = $1
      WHERE d."workspaceId" = $2 
        AND d.status = 'COMPLETED'
        ${searchCondition}
      ORDER BY d."createdAt" DESC
      LIMIT $${3 + (search ? 1 : 0)}
      OFFSET $${4 + (search ? 1 : 0)}
    `;
    
    const countQuery = `
      SELECT COUNT(*) as total
      FROM "knowledge_base_document" d
      WHERE d."workspaceId" = $1 
        AND d.status = 'COMPLETED'
        ${searchCondition}
    `;

    const limitValue = limit || 50;
    const offsetValue = offset || 0;
    
    const [documents, countResult] = await this.prisma.$transaction([
      this.prisma.$queryRawUnsafe(query, projectId, workspaceId, ...searchParams, limitValue, offsetValue),
      this.prisma.$queryRawUnsafe(countQuery, workspaceId, ...searchParams)
    ]);

    const total = Number((countResult as Array<{ total: bigint }>)[0].total);
    
    return {
      documents: (documents as Array<{
        id: string;
        fileName: string;
        fileType: string;
        fileSize: number;
        status: string;
        uploadedAt: Date;
        storedFileName: string | null;
        projectName: string;
        projectId: string;
        workspaceId: string;
        createdAt: Date;
        updatedAt: Date;
        isAssociated: boolean;
      }>).map(row => ({
        document: {
          id: row.id,
          workspaceId: row.workspaceId,
          fileName: row.fileName,
          fileType: row.fileType,
          fileSize: row.fileSize,
          status: row.status,
          uploadedAt: row.uploadedAt,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt
        },
        isAssociated: row.isAssociated
      })),
      total,
      limit: limitValue,
      offset: offsetValue
    };
  }

  async updateProjectDocuments(projectId: string, documentIds: string[]) {
    return this.prisma.$transaction(async (tx) => {
      // Delete all existing associations
      await tx.projectDocuments.deleteMany({
        where: {
          projectId
        }
      });

      // Create new associations
      if (documentIds.length > 0) {
        await tx.projectDocuments.createMany({
          data: documentIds.map(documentId => ({
            projectId,
            documentId
          }))
        });
      }

      // Return updated list
      const updated = await tx.projectDocuments.findMany({
        where: {
          projectId
        },
        include: {
          document: true
        }
      });

      return updated.map(pd => pd.document);
    });
  }

  async create(data: Prisma.KnowledgeBaseDocumentCreateInput) {
    return this.prisma.knowledgeBaseDocument.create({
      data
    });
  }

  async update(id: string, data: Prisma.KnowledgeBaseDocumentUpdateInput) {
    return this.prisma.knowledgeBaseDocument.update({
      where: { id },
      data
    });
  }

  async delete(id: string) {
    return this.prisma.knowledgeBaseDocument.delete({
      where: { id }
    });
  }

  async findByIdAndUser(id: string, userId: string): Promise<KnowledgeBaseDocument | null> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        workspaceMembers: {
          select: {
            workspaceId: true
          }
        }
      }
    });

    if (!user) {
      return null;
    }

    const workspaceIds = user.workspaceMembers.map(m => m.workspaceId);

    return this.prisma.knowledgeBaseDocument.findFirst({
      where: {
        id,
        workspaceId: {
          in: workspaceIds
        }
      }
    });
  }

  async updateStatus(id: string, status: Prisma.EnumDocumentStatusFieldUpdateOperationsInput | DocumentStatus): Promise<KnowledgeBaseDocument> {
    return this.prisma.knowledgeBaseDocument.update({
      where: { id },
      data: { status }
    });
  }

  async createChunks(chunks: Array<{
    documentId: string;
    content: string;
    embedding?: number[];
    metadata?: Record<string, unknown>;
  }>): Promise<void> {
    await this.prisma.documentChunk.createMany({
      data: chunks.map(chunk => ({
        documentId: chunk.documentId,
        content: chunk.content,
        embedding: chunk.embedding ? chunk.embedding : undefined,
        metadata: chunk.metadata as Prisma.InputJsonObject | undefined
      }))
    });
  }

  async getChunksByDocumentId(documentId: string): Promise<DocumentChunk[]> {
    return this.prisma.documentChunk.findMany({
      where: { documentId },
      orderBy: { createdAt: 'asc' }
    });
  }

  async deleteChunksByDocumentId(documentId: string): Promise<void> {
    await this.prisma.documentChunk.deleteMany({
      where: { documentId }
    });
  }

  async findByFileName(fileName: string, projectId: string): Promise<KnowledgeBaseDocument | null> {
    const projectDoc = await this.prisma.projectDocuments.findFirst({
      where: {
        projectId,
        document: {
          fileName,
          status: 'COMPLETED'
        }
      },
      include: {
        document: true
      }
    });

    return projectDoc?.document || null;
  }
}

// Singleton instance for backward compatibility
export const documentRepository = new DocumentRepository(prisma);