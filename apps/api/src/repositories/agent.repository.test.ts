import { PrismaClient } from '@prisma/client';
import { AgentRepository } from './agent.repository';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';

describe('AgentRepository', () => {
  let prisma: DeepMockProxy<PrismaClient>;
  let agentRepository: AgentRepository;

  beforeEach(() => {
    prisma = mockDeep<PrismaClient>();
    agentRepository = new AgentRepository(prisma);
  });

  describe('create', () => {
    it('should create an agent', async () => {
      const agentData = {
        title: 'Test Agent',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
      };

      const expectedAgent = {
        id: 'agent-123',
        ...agentData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.agent.create.mockResolvedValue(expectedAgent);

      const result = await agentRepository.create(agentData);

      expect(prisma.agent.create).toHaveBeenCalledWith({
        data: agentData,
      });
      expect(result).toEqual(expectedAgent);
    });
  });

  describe('findById', () => {
    it('should find an agent by id', async () => {
      const agentId = 'agent-123';
      const expectedAgent = {
        id: agentId,
        title: 'Test Agent',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.agent.findUnique.mockResolvedValue(expectedAgent);

      const result = await agentRepository.findById(agentId);

      expect(prisma.agent.findUnique).toHaveBeenCalledWith({
        where: { id: agentId },
      });
      expect(result).toEqual(expectedAgent);
    });

    it('should return null if agent not found', async () => {
      prisma.agent.findUnique.mockResolvedValue(null);

      const result = await agentRepository.findById('non-existent');

      expect(result).toBeNull();
    });
  });

  describe('findByProjectId', () => {
    it('should find agents by project id', async () => {
      const projectId = 'project-123';
      const expectedAgents = [
        {
          id: 'agent-1',
          title: 'Agent 1',
          workflowJson: { blocks: [] },
          projectId,
          triggerType: 'api',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'agent-2',
          title: 'Agent 2',
          workflowJson: { blocks: [] },
          projectId,
          triggerType: 'web_app',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      prisma.agent.findMany.mockResolvedValue(expectedAgents);

      const result = await agentRepository.findByProjectId(projectId);

      expect(prisma.agent.findMany).toHaveBeenCalledWith({
        where: { projectId },
        orderBy: { createdAt: 'desc' },
      });
      expect(result).toEqual(expectedAgents);
    });
  });

  describe('update', () => {
    it('should update an agent', async () => {
      const agentId = 'agent-123';
      const updateData = {
        title: 'Updated Title',
      };

      const expectedAgent = {
        id: agentId,
        title: 'Updated Title',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.agent.update.mockResolvedValue(expectedAgent);

      const result = await agentRepository.update(agentId, updateData);

      expect(prisma.agent.update).toHaveBeenCalledWith({
        where: { id: agentId },
        data: updateData,
      });
      expect(result).toEqual(expectedAgent);
    });
  });

  describe('delete', () => {
    it('should delete an agent', async () => {
      const agentId = 'agent-123';
      const deletedAgent = {
        id: agentId,
        title: 'Deleted Agent',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prisma.agent.delete.mockResolvedValue(deletedAgent);

      const result = await agentRepository.delete(agentId);

      expect(prisma.agent.delete).toHaveBeenCalledWith({
        where: { id: agentId },
      });
      expect(result).toEqual(deletedAgent);
    });
  });

  describe('exists', () => {
    it('should return true if agent exists', async () => {
      prisma.agent.count.mockResolvedValue(1);

      const result = await agentRepository.exists('agent-123');

      expect(prisma.agent.count).toHaveBeenCalledWith({
        where: { id: 'agent-123' },
      });
      expect(result).toBe(true);
    });

    it('should return false if agent does not exist', async () => {
      prisma.agent.count.mockResolvedValue(0);

      const result = await agentRepository.exists('non-existent');

      expect(result).toBe(false);
    });
  });

  describe('belongsToProject', () => {
    it('should return true if agent belongs to project', async () => {
      prisma.agent.count.mockResolvedValue(1);

      const result = await agentRepository.belongsToProject('agent-123', 'project-123');

      expect(prisma.agent.count).toHaveBeenCalledWith({
        where: {
          id: 'agent-123',
          projectId: 'project-123',
        },
      });
      expect(result).toBe(true);
    });

    it('should return false if agent does not belong to project', async () => {
      prisma.agent.count.mockResolvedValue(0);

      const result = await agentRepository.belongsToProject('agent-123', 'wrong-project');

      expect(result).toBe(false);
    });
  });
});