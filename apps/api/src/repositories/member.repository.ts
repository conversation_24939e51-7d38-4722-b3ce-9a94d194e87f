import { PrismaClient, Prisma, MemberRole } from '@prisma/client';

export interface MemberRepository {
  findByProject(projectId: string): Promise<MemberWithUser[]>;
  findByUserAndProject(userId: string, projectId: string): Promise<MemberWithUser | null>;
  create(data: CreateMemberData): Promise<MemberWithUser>;
  update(userId: string, projectId: string, role: MemberRole): Promise<MemberWithUser>;
  delete(userId: string, projectId: string): Promise<void>;
  countAdmins(projectId: string): Promise<number>;
}

export interface CreateMemberData {
  userId: string;
  projectId: string;
  role?: MemberRole;
}

export type MemberWithUser = Prisma.ProjectMemberGetPayload<{
  include: { user: true };
}>;

export function createMemberRepository(prisma: PrismaClient): MemberRepository {
  return {
    async findByProject(projectId: string): Promise<MemberWithUser[]> {
      return prisma.projectMember.findMany({
        where: { projectId },
        include: { user: true },
        orderBy: { invitedAt: 'desc' },
      });
    },

    async findByUserAndProject(userId: string, projectId: string): Promise<MemberWithUser | null> {
      return prisma.projectMember.findUnique({
        where: {
          userId_projectId: {
            userId,
            projectId,
          },
        },
        include: { user: true },
      });
    },

    async create(data: CreateMemberData): Promise<MemberWithUser> {
      return prisma.projectMember.create({
        data: {
          userId: data.userId,
          projectId: data.projectId,
          role: data.role || 'MEMBER',
          joinedAt: new Date(),
        },
        include: { user: true },
      });
    },

    async update(userId: string, projectId: string, role: MemberRole): Promise<MemberWithUser> {
      return prisma.projectMember.update({
        where: {
          userId_projectId: {
            userId,
            projectId,
          },
        },
        data: { role },
        include: { user: true },
      });
    },

    async delete(userId: string, projectId: string): Promise<void> {
      await prisma.projectMember.delete({
        where: {
          userId_projectId: {
            userId,
            projectId,
          },
        },
      });
    },

    async countAdmins(projectId: string): Promise<number> {
      return prisma.projectMember.count({
        where: {
          projectId,
          role: 'ADMIN',
        },
      });
    },
  };
}