import type { FastifyInstance } from 'fastify';
import type { Deployment, Prisma, Agent, Project } from '@prisma/client';

export type DeploymentWithRelations = Deployment & {
  agent: Agent & {
    project: Project;
  };
};

export class DeploymentRepository {
  constructor(private app: FastifyInstance) {}

  async create(data: Prisma.DeploymentCreateInput): Promise<Deployment> {
    return this.app.prisma.deployment.create({
      data,
    });
  }

  async findById(id: string): Promise<DeploymentWithRelations | null> {
    return this.app.prisma.deployment.findUnique({
      where: { id },
      include: {
        agent: {
          include: {
            project: true,
          },
        },
      },
    });
  }

  async findByApiKey(apiKey: string): Promise<DeploymentWithRelations | null> {
    return this.app.prisma.deployment.findUnique({
      where: { apiKey },
      include: {
        agent: {
          include: {
            project: true,
          },
        },
      },
    });
  }

  async findByAgentId(agentId: string): Promise<Deployment[]> {
    return this.app.prisma.deployment.findMany({
      where: { agentId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async updateStatus(
    id: string,
    status: 'ACTIVE' | 'INACTIVE'
  ): Promise<Deployment> {
    return this.app.prisma.deployment.update({
      where: { id },
      data: { status },
    });
  }

  async delete(id: string): Promise<Deployment> {
    return this.app.prisma.deployment.delete({
      where: { id },
    });
  }
}