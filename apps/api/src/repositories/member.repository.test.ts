import { PrismaClient } from '@prisma/client';
import { createMemberRepository } from './member.repository';
import { mockDeep } from 'jest-mock-extended';

describe('MemberRepository', () => {
  let prisma: any;
  let memberRepository: ReturnType<typeof createMemberRepository>;

  beforeEach(() => {
    prisma = mockDeep<PrismaClient>() as any;
    memberRepository = createMemberRepository(prisma);
  });

  describe('findByProject', () => {
    it('should return all members for a project', async () => {
      const projectId = 'project-123';
      const mockMembers = [
        {
          userId: 'user-1',
          projectId,
          role: 'ADMIN',
          invitedAt: new Date(),
          joinedAt: new Date(),
          user: {
            id: 'user-1',
            email: '<EMAIL>',
            name: 'Admin User',
            emailVerified: true,
            image: null,
            phone: null,
            country: null,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        },
        {
          userId: 'user-2',
          projectId,
          role: 'MEMBER',
          invitedAt: new Date(),
          joinedAt: new Date(),
          user: {
            id: 'user-2',
            email: '<EMAIL>',
            name: 'Member User',
            emailVerified: true,
            image: null,
            phone: null,
            country: null,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        },
      ];

      prisma.projectMember.findMany.mockResolvedValue(mockMembers as any);

      const result = await memberRepository.findByProject(projectId);

      expect(result).toEqual(mockMembers);
      expect(prisma.projectMember.findMany).toHaveBeenCalledWith({
        where: { projectId },
        include: { user: true },
        orderBy: { invitedAt: 'desc' },
      });
    });
  });

  describe('findByUserAndProject', () => {
    it('should return member if exists', async () => {
      const userId = 'user-1';
      const projectId = 'project-123';
      const mockMember = {
        userId,
        projectId,
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: userId,
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };

      prisma.projectMember.findUnique.mockResolvedValue(mockMember as any);

      const result = await memberRepository.findByUserAndProject(userId, projectId);

      expect(result).toEqual(mockMember);
      expect(prisma.projectMember.findUnique).toHaveBeenCalledWith({
        where: {
          userId_projectId: {
            userId,
            projectId,
          },
        },
        include: { user: true },
      });
    });

    it('should return null if member does not exist', async () => {
      prisma.projectMember.findUnique.mockResolvedValue(null);

      const result = await memberRepository.findByUserAndProject('user-1', 'project-123');

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should create a new member with default role', async () => {
      const data = {
        userId: 'user-1',
        projectId: 'project-123',
      };
      const mockMember = {
        ...data,
        role: 'MEMBER',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: data.userId,
          email: '<EMAIL>',
          name: 'Test User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };

      prisma.projectMember.create.mockResolvedValue(mockMember as any);

      const result = await memberRepository.create(data);

      expect(result).toEqual(mockMember);
      expect(prisma.projectMember.create).toHaveBeenCalledWith({
        data: {
          userId: data.userId,
          projectId: data.projectId,
          role: 'MEMBER',
          joinedAt: expect.any(Date),
        },
        include: { user: true },
      });
    });

    it('should create a new member with specified role', async () => {
      const data = {
        userId: 'user-1',
        projectId: 'project-123',
        role: 'ADMIN' as const,
      };
      const mockMember = {
        ...data,
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: data.userId,
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };

      prisma.projectMember.create.mockResolvedValue(mockMember as any);

      const result = await memberRepository.create(data);

      expect(result).toEqual(mockMember);
      expect(prisma.projectMember.create).toHaveBeenCalledWith({
        data: {
          userId: data.userId,
          projectId: data.projectId,
          role: 'ADMIN',
          joinedAt: expect.any(Date),
        },
        include: { user: true },
      });
    });
  });

  describe('update', () => {
    it('should update member role', async () => {
      const userId = 'user-1';
      const projectId = 'project-123';
      const newRole = 'ADMIN' as const;
      const mockMember = {
        userId,
        projectId,
        role: newRole,
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: userId,
          email: '<EMAIL>',
          name: 'Test User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };

      prisma.projectMember.update.mockResolvedValue(mockMember as any);

      const result = await memberRepository.update(userId, projectId, newRole);

      expect(result).toEqual(mockMember);
      expect(prisma.projectMember.update).toHaveBeenCalledWith({
        where: {
          userId_projectId: {
            userId,
            projectId,
          },
        },
        data: { role: newRole },
        include: { user: true },
      });
    });
  });

  describe('delete', () => {
    it('should delete a member', async () => {
      const userId = 'user-1';
      const projectId = 'project-123';

      await memberRepository.delete(userId, projectId);

      expect(prisma.projectMember.delete).toHaveBeenCalledWith({
        where: {
          userId_projectId: {
            userId,
            projectId,
          },
        },
      });
    });
  });

  describe('countAdmins', () => {
    it('should count admin members', async () => {
      const projectId = 'project-123';
      const adminCount = 2;

      prisma.projectMember.count.mockResolvedValue(adminCount);

      const result = await memberRepository.countAdmins(projectId);

      expect(result).toBe(adminCount);
      expect(prisma.projectMember.count).toHaveBeenCalledWith({
        where: {
          projectId,
          role: 'ADMIN',
        },
      });
    });
  });
});