import { PrismaClient, Agent, Prisma } from '@prisma/client';

export class AgentRepository {
  constructor(private prisma: PrismaClient) {}

  async create(data: Prisma.AgentCreateInput): Promise<Agent> {
    return this.prisma.agent.create({
      data,
    });
  }

  async findById(id: string): Promise<Agent | null> {
    return this.prisma.agent.findUnique({
      where: { id },
    });
  }

  async findByProjectId(projectId: string): Promise<Agent[]> {
    return this.prisma.agent.findMany({
      where: { projectId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async update(id: string, data: Prisma.AgentUpdateInput): Promise<Agent> {
    return this.prisma.agent.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<Agent> {
    return this.prisma.agent.delete({
      where: { id },
    });
  }

  async exists(id: string): Promise<boolean> {
    const count = await this.prisma.agent.count({
      where: { id },
    });
    return count > 0;
  }

  async belongsToProject(agentId: string, projectId: string): Promise<boolean> {
    const count = await this.prisma.agent.count({
      where: {
        id: agentId,
        projectId: projectId,
      },
    });
    return count > 0;
  }
}