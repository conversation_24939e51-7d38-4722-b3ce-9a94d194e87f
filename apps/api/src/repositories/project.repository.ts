import { PrismaClient, Project } from '@prisma/client'
import { BaseRepository } from './base.repository'

export interface CreateProjectInput {
  name: string
  description?: string
  accessType?: 'PERSONAL' | 'COMPANY_WIDE'
  userId: string
  workspaceId?: string
}

export interface UpdateProjectInput {
  name?: string
  description?: string
}

export class ProjectRepository extends BaseRepository {
  constructor(prisma: PrismaClient) {
    super(prisma)
  }

  async findByUserId(userId: string): Promise<Project[]> {
    return this.prisma.project.findMany({
      where: {
        OR: [
          { userId },
          {
            members: {
              some: {
                userId,
              },
            },
          },
        ],
      },
      orderBy: { createdAt: 'desc' },
    })
  }

  async findByIdAndUserId(id: string, userId: string): Promise<Project | null> {
    return this.prisma.project.findFirst({
      where: {
        id,
        OR: [
          { userId },
          {
            members: {
              some: {
                userId,
              },
            },
          },
        ],
      },
    })
  }

  async create(data: CreateProjectInput): Promise<Project> {
    return this.prisma.$transaction(async (tx) => {
      const project = await tx.project.create({
        data: {
          name: data.name,
          description: data.description,
          accessType: data.accessType || 'PERSONAL',
          userId: data.userId,
          workspaceId: data.workspaceId!,
        },
      })

      // Automatically add the creator as an admin member
      await tx.projectMember.create({
        data: {
          userId: data.userId,
          projectId: project.id,
          role: 'ADMIN',
          joinedAt: new Date(),
        },
      })

      return project
    })
  }

  async updateByIdAndUserId(
    id: string,
    userId: string,
    data: UpdateProjectInput
  ): Promise<Project | null> {
    const result = await this.prisma.project.updateMany({
      where: {
        id,
        userId,
      },
      data,
    })

    if (result.count === 0) {
      return null
    }

    return this.prisma.project.findUnique({
      where: { id },
    })
  }

  async deleteByIdAndUserId(id: string, userId: string): Promise<boolean> {
    return await this.prisma.$transaction(async (tx) => {
      // First delete all related agents
      await tx.agent.deleteMany({
        where: { projectId: id },
      })

      // Then delete the project
      const result = await tx.project.deleteMany({
        where: {
          id,
          userId,
        },
      })

      return result.count > 0
    })
  }

  async exists(id: string, userId: string): Promise<boolean> {
    const count = await this.prisma.project.count({
      where: {
        id,
        userId,
      },
    })

    return count > 0
  }
}