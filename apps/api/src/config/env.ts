// Environment variable configuration with validation
export const env = {
  // Database
  DATABASE_URL: process.env.DATABASE_URL || '',
  
  // Server
  PORT: process.env.PORT || '3001',
  HOST: process.env.HOST || '0.0.0.0',
  
  // Authentication
  AUTH_URL: process.env.AUTH_URL || '',
  AUTH_SECRET: process.env.AUTH_SECRET || '',
  ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS || '',
  
  // Email
  RESEND_API_KEY: process.env.RESEND_API_KEY || '',
  RESEND_FROM_EMAIL: process.env.RESEND_FROM_EMAIL || '',
  
  // Frontend
  FRONTEND_URL: process.env.FRONTEND_URL || '',
  
  // Development
  NODE_ENV: process.env.NODE_ENV || 'development',
} as const;

// Validate required environment variables
export function validateEnv() {
  const required = [
    'DATABASE_URL',
    'AUTH_URL',
    'AUTH_SECRET',
    'ALLOWED_ORIGINS',
    'FRONTEND_URL',
    'RESEND_API_KEY',
    'RESEND_FROM_EMAIL'
  ];
  
  const missing = required.filter(key => !env[key as keyof typeof env]);
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}. ` +
      'Please check your .env file.'
    );
  }
}

// Parse allowed origins into an array
export function getAllowedOrigins(): string[] {
  return env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim()).filter(Boolean);
}