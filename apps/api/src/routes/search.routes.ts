import type { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { documentRepository } from '../repositories';
import { auth } from '../lib/auth';
import prisma from '../lib/prisma';

const SearchQuerySchema = z.object({
  query: z.string().min(1),
  projectId: z.string()
});

export default async function searchRoutes(fastify: FastifyInstance) {
  // Ensure prisma is available
  if (!fastify.prisma) {
    fastify.decorate('prisma', prisma);
  }

  // Search documents for agent file selection
  fastify.post('/search/documents', async (request, reply) => {
    try {
      // Validate request body
      const body = SearchQuerySchema.safeParse(request.body);
      if (!body.success) {
        return reply.code(400).send({ error: 'Invalid request body', details: body.error });
      }
      
      const { query, projectId } = body.data;

      // Check authentication
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Check if user is a member of the project
      const projectMember = await fastify.prisma.projectMember.findUnique({
        where: {
          userId_projectId: {
            userId: session.user.id,
            projectId: projectId
          }
        }
      });

      if (!projectMember) {
        return reply.code(403).send({ error: 'Access denied' });
      }
      
      // Get project's associated documents
      const projectDocs = await documentRepository.findByProject(projectId);
      
      // Filter documents by search query
      const filteredDocs = projectDocs.filter(doc => 
        doc.fileName.toLowerCase().includes(query.toLowerCase())
      );

      // Return formatted response for agent file selector
      const results = filteredDocs.map(doc => ({
        id: doc.id,
        fileName: doc.fileName,
        fileType: doc.fileType,
        fileSize: doc.fileSize,
        uploadedAt: doc.uploadedAt
      }));

      return reply.send({ 
        documents: results,
        total: results.length 
      });
    } catch (error) {
      fastify.log.error({ error }, 'Failed to search documents');
      return reply.code(500).send({ error: 'Failed to search documents' });
    }
  });

  // Get document chunks for RAG search (used by agents)
  fastify.post('/search/chunks', async (request, reply) => {
    try {
      // Validate request body
      const ChunkSearchSchema = z.object({
        query: z.string().min(1),
        projectId: z.string(),
        limit: z.number().min(1).max(100).optional().default(10)
      });
      
      const body = ChunkSearchSchema.safeParse(request.body);
      if (!body.success) {
        return reply.code(400).send({ error: 'Invalid request body', details: body.error });
      }
      
      const { query, projectId, limit } = body.data;

      // Check authentication
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Check if user has access to the project
      const projectMember = await fastify.prisma.projectMember.findUnique({
        where: {
          userId_projectId: {
            userId: session.user.id,
            projectId: projectId
          }
        }
      });

      if (!projectMember) {
        return reply.code(403).send({ error: 'Access denied' });
      }
      
      // Get project's associated document IDs
      const projectDocs = await documentRepository.findByProject(projectId);
      const documentIds = projectDocs.map(doc => doc.id);

      if (documentIds.length === 0) {
        return reply.send({ chunks: [], total: 0 });
      }

      // Search chunks only from project's documents
      // TODO: Implement vector search when Knowledge Base Service is integrated
      const chunks = await fastify.prisma.documentChunk.findMany({
        where: {
          documentId: {
            in: documentIds
          },
          content: {
            contains: query,
            mode: 'insensitive'
          }
        },
        include: {
          document: {
            select: {
              id: true,
              fileName: true,
              fileType: true
            }
          }
        },
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      });

      return reply.send({ 
        chunks: chunks.map(chunk => ({
          id: chunk.id,
          content: chunk.content,
          documentId: chunk.documentId,
          document: chunk.document,
          metadata: chunk.metadata
        })),
        total: chunks.length 
      });
    } catch (error) {
      fastify.log.error({ error }, 'Failed to search document chunks');
      return reply.code(500).send({ error: 'Failed to search document chunks' });
    }
  });
}