import { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { auth } from '../lib/auth';
import prisma from '../lib/prisma';
import { ProjectRepository } from '../repositories/project.repository';
import { DocumentRepository } from '../repositories/document.repository';
import { logAuditEvent, AuditActions } from '../lib/audit-log';

// Schema definitions
const createProjectSchema = z.object({
  name: z.string().min(1, "Name is required").max(255),
  description: z.string().optional(),
  accessType: z.enum(['PERSONAL', 'COMPANY_WIDE']).optional(),
});

const updateProjectSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
});

const paramsSchema = z.object({
  id: z.string().cuid(),
});

export async function projectRoutes(fastify: FastifyInstance) {
  const projectRepository = new ProjectRepository(prisma);
  const documentRepository = new DocumentRepository(prisma);
  
  // Get all projects for authenticated user
  fastify.get('/projects', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const projects = await projectRepository.findByUserId(session.user.id);

      return reply.send(projects);
    } catch (error) {
      fastify.log.error('Get projects error:', error);
      return reply.status(500).send({ error: 'Failed to get projects' });
    }
  });

  // Create project
  fastify.post('/projects', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = createProjectSchema.safeParse(request.body);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: validation.error.flatten(),
        });
      }

      // Get or create default workspace for the user
      let workspace = await prisma.workspace.findFirst({
        where: {
          members: {
            some: {
              userId: session.user.id
            }
          }
        }
      });
      
      if (!workspace) {
        // Create a personal workspace for the user
        workspace = await prisma.workspace.create({
          data: {
            name: `${session.user.name || session.user.email}'s Workspace`,
            description: 'Personal workspace',
            members: {
              create: {
                userId: session.user.id,
                role: 'ADMIN'
              }
            }
          }
        });
      }
      
      const project = await projectRepository.create({
        ...validation.data,
        userId: session.user.id,
        workspaceId: workspace.id,
      });

      // Audit log the creation
      await logAuditEvent(
        request,
        AuditActions.PROJECT_CREATED,
        'project',
        project.id,
        { name: project.name }
      );

      return reply.status(201).send(project);
    } catch (error) {
      fastify.log.error('Create project error:', error);
      return reply.status(500).send({ error: 'Failed to create project' });
    }
  });

  // Get single project
  fastify.get('/projects/:id', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = paramsSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid project ID',
          details: validation.error.flatten(),
        });
      }

      const project = await projectRepository.findByIdAndUserId(
        validation.data.id,
        session.user.id
      );

      if (!project) {
        return reply.status(404).send({ error: 'Project not found' });
      }

      return reply.send(project);
    } catch (error) {
      fastify.log.error('Get project error:', error);
      return reply.status(500).send({ error: 'Failed to get project' });
    }
  });

  // Update project
  fastify.put('/projects/:id', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const paramsValidation = paramsSchema.safeParse(request.params);
      if (!paramsValidation.success) {
        return reply.status(400).send({
          error: 'Invalid project ID',
          details: paramsValidation.error.flatten(),
        });
      }

      const bodyValidation = updateProjectSchema.safeParse(request.body);
      if (!bodyValidation.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: bodyValidation.error.flatten(),
        });
      }

      const updatedProject = await projectRepository.updateByIdAndUserId(
        paramsValidation.data.id,
        session.user.id,
        bodyValidation.data
      );

      if (!updatedProject) {
        return reply.status(404).send({ error: 'Project not found' });
      }

      return reply.send(updatedProject);
    } catch (error) {
      fastify.log.error('Update project error:', error);
      return reply.status(500).send({ error: 'Failed to update project' });
    }
  });

  // Delete project
  fastify.delete('/projects/:id', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = paramsSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid project ID',
          details: validation.error.flatten(),
        });
      }

      const deleted = await projectRepository.deleteByIdAndUserId(
        validation.data.id,
        session.user.id
      );

      if (!deleted) {
        return reply.status(404).send({ error: 'Project not found' });
      }

      // Audit log the deletion
      await logAuditEvent(
        request,
        AuditActions.PROJECT_DELETED,
        'project',
        validation.data.id
      );

      return reply.status(204).send();
    } catch (error) {
      fastify.log.error('Delete project error:', error);
      return reply.status(500).send({ error: 'Failed to delete project' });
    }
  });

  // Get documents associated with a project
  fastify.get('/projects/:id/documents', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = paramsSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid project ID',
          details: validation.error.flatten(),
        });
      }

      // Verify user has access to the project
      const project = await projectRepository.findByIdAndUserId(
        validation.data.id,
        session.user.id
      );

      if (!project) {
        return reply.status(404).send({ error: 'Project not found' });
      }

      // Get documents associated with the project
      const result = await documentRepository.findByProject(validation.data.id);
      
      // Return only the documents array, not the pagination wrapper
      return reply.send(result.documents);
    } catch (error) {
      fastify.log.error('Get project documents error:', error);
      return reply.status(500).send({ error: 'Failed to get project documents' });
    }
  });
}