import { FastifyInstance, FastifyPluginOptions } from 'fastify'
import { createMemberRepository, MemberRepository } from '../repositories'
import { PrismaClient } from '@prisma/client'
import { logAuditEvent, AuditActions } from '../lib/audit-log'
import { auth } from '../lib/auth'

interface MemberRouteOptions extends FastifyPluginOptions {
  prisma: PrismaClient
}

interface AddMemberBody {
  userId: string
  role?: 'ADMIN' | 'MEMBER'
}

interface UpdateMemberBody {
  role: 'ADMIN' | 'MEMBER'
}

export async function memberRoutes(
  fastify: FastifyInstance,
  options: MemberRouteOptions
) {
  const memberRepository: MemberRepository = createMemberRepository(options.prisma)

  // Get all members for a project
  fastify.get<{
    Params: { projectId: string }
  }>('/projects/:projectId/members', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any })
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' })
      }

      const { projectId } = request.params
      const userId = session.user.id

      // Check if user has access to the project
      const project = await options.prisma.project.findFirst({
        where: {
          id: projectId,
          OR: [
            { userId },
            {
              members: {
                some: {
                  userId,
                },
              },
            },
          ],
        },
      })

      if (!project) {
        return reply.code(404).send({ error: 'Project not found' })
      }

      const members = await memberRepository.findByProject(projectId)
      return reply.send(members)
    } catch (error) {
      fastify.log.error('Get members error:', error)
      return reply.status(500).send({ error: 'Failed to get members' })
    }
  })

  // Add a member to a project
  fastify.post<{
    Params: { projectId: string }
    Body: AddMemberBody
  }>('/projects/:projectId/members', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any })
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' })
      }

      const { projectId } = request.params
      const { userId: newMemberUserId, role = 'MEMBER' } = request.body
      const userId = session.user.id

      // Check if user is an admin of the project
      const currentMember = await memberRepository.findByUserAndProject(userId, projectId)
      if (!currentMember || currentMember.role !== 'ADMIN') {
        return reply.status(403).send({ error: 'Only project admins can add members' })
      }

      // Check if the new user exists
      const newUser = await options.prisma.user.findUnique({
        where: { id: newMemberUserId },
      })

      if (!newUser) {
        return reply.status(404).send({ error: 'User not found' })
      }

      // Check if member already exists
      const existingMember = await memberRepository.findByUserAndProject(newMemberUserId, projectId)
      if (existingMember) {
        return reply.status(409).send({ error: 'User is already a member of this project' })
      }

      const member = await memberRepository.create({
        userId: newMemberUserId,
        projectId,
        role,
      })

      await logAuditEvent({
        userId,
        action: AuditActions.MEMBER_ADDED,
        resourceType: 'project',
        resourceId: projectId,
        metadata: {
          newMemberUserId,
          role,
        },
      })

      return reply.status(201).send(member)
    } catch (error) {
      fastify.log.error('Add member error:', error)
      return reply.status(500).send({ error: 'Failed to add member' })
    }
  })

  // Update a member's role
  fastify.put<{
    Params: { projectId: string; userId: string }
    Body: UpdateMemberBody
  }>('/projects/:projectId/members/:userId', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any })
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' })
      }

      const { projectId, userId: targetUserId } = request.params
      const { role } = request.body
      const userId = session.user.id

      // Check if user is an admin of the project
      const currentMember = await memberRepository.findByUserAndProject(userId, projectId)
      if (!currentMember || currentMember.role !== 'ADMIN') {
        return reply.status(403).send({ error: 'Only project admins can update member roles' })
      }

      // Check if target member exists
      const targetMember = await memberRepository.findByUserAndProject(targetUserId, projectId)
      if (!targetMember) {
        return reply.status(404).send({ error: 'Member not found' })
      }

      // Prevent demoting self if last admin
      if (userId === targetUserId && role === 'MEMBER') {
        const adminCount = await memberRepository.countAdmins(projectId)
        if (adminCount <= 1) {
          return reply.status(400).send({ error: 'Cannot demote the last admin' })
        }
      }

      const updatedMember = await memberRepository.update(targetUserId, projectId, role)

      await logAuditEvent({
        userId,
        action: AuditActions.MEMBER_ROLE_UPDATED,
        resourceType: 'project',
        resourceId: projectId,
        metadata: {
          targetUserId,
          oldRole: targetMember.role,
          newRole: role,
        },
      })

      return reply.send(updatedMember)
    } catch (error) {
      fastify.log.error('Update member error:', error)
      return reply.status(500).send({ error: 'Failed to update member' })
    }
  })

  // Remove a member from a project
  fastify.delete<{
    Params: { projectId: string; userId: string }
  }>('/projects/:projectId/members/:userId', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any })
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' })
      }

      const { projectId, userId: targetUserId } = request.params
      const userId = session.user.id

      // Check if user is an admin of the project
      const currentMember = await memberRepository.findByUserAndProject(userId, projectId)
      if (!currentMember || currentMember.role !== 'ADMIN') {
        return reply.status(403).send({ error: 'Only project admins can remove members' })
      }

      // Check if target member exists
      const targetMember = await memberRepository.findByUserAndProject(targetUserId, projectId)
      if (!targetMember) {
        return reply.status(404).send({ error: 'Member not found' })
      }

      // Prevent removing self if last admin
      if (userId === targetUserId && targetMember.role === 'ADMIN') {
        const adminCount = await memberRepository.countAdmins(projectId)
        if (adminCount <= 1) {
          return reply.status(400).send({ error: 'Cannot remove the last admin' })
        }
      }

      await memberRepository.delete(targetUserId, projectId)

      await logAuditEvent({
        userId,
        action: AuditActions.MEMBER_REMOVED,
        resourceType: 'project',
        resourceId: projectId,
        metadata: {
          removedUserId: targetUserId,
          removedUserRole: targetMember.role,
        },
      })

      return reply.status(204).send()
    } catch (error) {
      fastify.log.error('Remove member error:', error)
      return reply.status(500).send({ error: 'Failed to remove member' })
    }
  })
}