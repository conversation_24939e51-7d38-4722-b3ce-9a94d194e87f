import { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { DeploymentService } from '../services/deployment.service.js';
import { auth } from '../lib/auth.js';

const deployAgentSchema = z.object({
  id: z.string().cuid(),
});

const updateDeploymentSchema = z.object({
  id: z.string().cuid(),
});

const updateDeploymentBodySchema = z.object({
  status: z.enum(['ACTIVE', 'INACTIVE']),
});

export default async function deploymentRoutes(fastify: FastifyInstance) {
  const deploymentService = new DeploymentService(fastify);

  // Deploy an agent
  fastify.post('/agents/:id/deploy', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = deployAgentSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid agent ID',
          details: validation.error.flatten(),
        });
      }

      const deployment = await deploymentService.createDeployment(
        validation.data.id,
        session.user.id
      );

      return reply.send({
        id: deployment.id,
        agentId: deployment.agentId,
        deploymentUrl: deployment.deploymentUrl,
        apiKey: deployment.apiKey,
        status: deployment.status,
        createdAt: deployment.createdAt,
      });
    } catch (error) {
      if (error instanceof Error) {
        if (error.message === 'Agent not found') {
          return reply.status(404).send({ error: 'Agent not found' });
        }
        if (error.message.startsWith('Unauthorized')) {
          return reply.status(403).send({ error: error.message });
        }
        if (error.message === 'Project not found') {
          return reply.status(404).send({ error: 'Project not found' });
        }
      }
      fastify.log.error('Deploy agent error:', error);
      fastify.log.error('Error details:', { 
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        agentId: validation.data.id,
        userId: session?.user?.id
      });
      return reply.status(500).send({ 
        error: 'Failed to deploy agent',
        details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : 'Unknown error') : undefined
      });
    }
  });

  // Get deployments for an agent
  fastify.get('/agents/:id/deployments', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = deployAgentSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid agent ID',
          details: validation.error.flatten(),
        });
      }

      const deployments = await deploymentService.getDeploymentsByAgent(
        validation.data.id
      );

      return reply.send({ deployments });
    } catch (error) {
      fastify.log.error('Get deployments error:', error);
      return reply.status(500).send({ error: 'Failed to get deployments' });
    }
  });

  // Get deployment details
  fastify.get('/deployments/:id', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = updateDeploymentSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid deployment ID',
          details: validation.error.flatten(),
        });
      }

      const deployment = await deploymentService.getDeployment(validation.data.id);
      if (!deployment) {
        return reply.status(404).send({ error: 'Deployment not found' });
      }

      return reply.send(deployment);
    } catch (error) {
      fastify.log.error('Get deployment error:', error);
      return reply.status(500).send({ error: 'Failed to get deployment' });
    }
  });

  // Update deployment status
  fastify.patch('/deployments/:id', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const paramsValidation = updateDeploymentSchema.safeParse(request.params);
      if (!paramsValidation.success) {
        return reply.status(400).send({
          error: 'Invalid deployment ID',
          details: paramsValidation.error.flatten(),
        });
      }

      const bodyValidation = updateDeploymentBodySchema.safeParse(request.body);
      if (!bodyValidation.success) {
        return reply.status(400).send({
          error: 'Invalid request body',
          details: bodyValidation.error.flatten(),
        });
      }

      const deployment = await deploymentService.updateDeploymentStatus(
        paramsValidation.data.id,
        bodyValidation.data.status,
        session.user.id
      );

      return reply.send(deployment);
    } catch (error) {
      if (error instanceof Error) {
        if (error.message === 'Deployment not found') {
          return reply.status(404).send({ error: 'Deployment not found' });
        }
        if (error.message.startsWith('Unauthorized')) {
          return reply.status(403).send({ error: error.message });
        }
      }
      fastify.log.error('Update deployment error:', error);
      return reply.status(500).send({ error: 'Failed to update deployment' });
    }
  });

  // Delete deployment
  fastify.delete('/deployments/:id', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = updateDeploymentSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid deployment ID',
          details: validation.error.flatten(),
        });
      }

      await deploymentService.deleteDeployment(validation.data.id, session.user.id);

      return reply.status(204).send();
    } catch (error) {
      if (error instanceof Error) {
        if (error.message === 'Deployment not found') {
          return reply.status(404).send({ error: 'Deployment not found' });
        }
        if (error.message.startsWith('Unauthorized')) {
          return reply.status(403).send({ error: error.message });
        }
      }
      fastify.log.error('Delete deployment error:', error);
      return reply.status(500).send({ error: 'Failed to delete deployment' });
    }
  });
}