import { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { AgentService } from '../services/agent.service';
import { auth } from '../lib/auth';
import prisma from '../lib/prisma';

// Schema definitions
const createAgentSchema = z.object({
  title: z.string().min(1, "Title is required").max(255),
  workflowJson: z.any().refine(
    (data) => {
      const jsonString = JSON.stringify(data);
      return jsonString.length <= 1048576; // 1MB limit
    },
    { message: "Workflow data too large (max 1MB)" }
  ),
  projectId: z.string().cuid(),
  triggerType: z.enum(['api', 'web_app', 'external_event']).optional(),
});

const updateAgentSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  workflowJson: z.any().optional().refine(
    (data) => {
      if (!data) return true; // Optional field
      const jsonString = JSON.stringify(data);
      return jsonString.length <= 1048576; // 1MB limit
    },
    { message: "Workflow data too large (max 1MB)" }
  ),
  triggerType: z.enum(['api', 'web_app', 'external_event']).optional(),
});

const paramsSchema = z.object({
  id: z.string().cuid(),
});

const querySchema = z.object({
  projectId: z.string().cuid().optional(),
});

const executeWorkflowSchema = z.object({
  inputs: z.record(z.any()).optional(),
});

// Helper function to verify project access
async function verifyProjectAccess(
  userId: string,
  projectId: string
): Promise<boolean> {
  const project = await prisma.project.findFirst({
    where: { 
      id: projectId,
      OR: [
        { userId: userId },
        {
          members: {
            some: {
              userId: userId
            }
          }
        }
      ]
    }
  });
  return !!project;
}

export async function agentRoutes(fastify: FastifyInstance) {
  const agentService = new AgentService(prisma, fastify, fastify.log);

  // Create agent
  fastify.post('/agents', async (request, reply) => {
    try {
      // Verify session
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Validate request body
      const validation = createAgentSchema.safeParse(request.body);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: validation.error.flatten(),
        });
      }

      // Verify user has access to the project
      const hasAccess = await verifyProjectAccess(session.user.id, validation.data.projectId);
      if (!hasAccess) {
        return reply.status(403).send({ error: 'Access denied to this project' });
      }

      // Create agent
      const agent = await agentService.createAgent(validation.data);

      return reply.status(201).send(agent);
    } catch (error) {
      fastify.log.error('Create agent error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : '';
      fastify.log.error('Error details:', { message: errorMessage, stack: errorStack });
      return reply.status(500).send({ 
        error: 'Failed to create agent',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      });
    }
  });

  // Get agents by project
  fastify.get('/agents', async (request, reply) => {
    try {
      // Verify session
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Validate query params
      const validation = querySchema.safeParse(request.query);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid query parameters',
          details: validation.error.flatten(),
        });
      }

      if (!validation.data.projectId) {
        return reply.status(400).send({ error: 'projectId is required' });
      }

      // Verify user has access to the project
      const hasAccess = await verifyProjectAccess(session.user.id, validation.data.projectId);
      if (!hasAccess) {
        return reply.status(403).send({ error: 'Access denied to this project' });
      }

      // Get agents
      const agents = await agentService.getAgentsByProject(validation.data.projectId);

      return reply.send(agents);
    } catch (error) {
      fastify.log.error('Get agents error:', error);
      return reply.status(500).send({ error: 'Failed to get agents' });
    }
  });

  // Get single agent
  fastify.get('/agents/:id', async (request, reply) => {
    try {
      // Verify session
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Validate params
      const validation = paramsSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid agent ID',
          details: validation.error.flatten(),
        });
      }

      // Get agent
      const agent = await agentService.getAgent(validation.data.id);

      // Verify user has access to the agent's project
      const hasAccess = await verifyProjectAccess(session.user.id, agent.projectId);
      if (!hasAccess) {
        return reply.status(403).send({ error: 'Access denied to this agent' });
      }

      return reply.send(agent);
    } catch (error) {
      if (error instanceof Error && error.message === 'Agent not found') {
        return reply.status(404).send({ error: 'Agent not found' });
      }
      fastify.log.error('Get agent error:', error);
      return reply.status(500).send({ error: 'Failed to get agent' });
    }
  });

  // Update agent
  fastify.put('/agents/:id', async (request, reply) => {
    try {
      // Verify session
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Validate params
      const paramsValidation = paramsSchema.safeParse(request.params);
      if (!paramsValidation.success) {
        return reply.status(400).send({
          error: 'Invalid agent ID',
          details: paramsValidation.error.flatten(),
        });
      }

      // Validate body
      const bodyValidation = updateAgentSchema.safeParse(request.body);
      if (!bodyValidation.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: bodyValidation.error.flatten(),
        });
      }

      // Get agent to verify access
      const existingAgent = await agentService.getAgent(paramsValidation.data.id);
      
      // Verify user has access to the agent's project
      const hasAccess = await verifyProjectAccess(session.user.id, existingAgent.projectId);
      if (!hasAccess) {
        return reply.status(403).send({ error: 'Access denied to this agent' });
      }

      // Update agent
      const agent = await agentService.updateAgent(
        paramsValidation.data.id,
        bodyValidation.data
      );

      return reply.send(agent);
    } catch (error) {
      if (error instanceof Error && error.message === 'Agent not found') {
        return reply.status(404).send({ error: 'Agent not found' });
      }
      fastify.log.error('Update agent error:', error);
      return reply.status(500).send({ error: 'Failed to update agent' });
    }
  });

  // Delete agent
  fastify.delete('/agents/:id', async (request, reply) => {
    try {
      // Verify session
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Validate params
      const validation = paramsSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid agent ID',
          details: validation.error.flatten(),
        });
      }

      // Get agent to verify access
      const agent = await agentService.getAgent(validation.data.id);
      
      // Verify user has access to the agent's project
      const hasAccess = await verifyProjectAccess(session.user.id, agent.projectId);
      if (!hasAccess) {
        return reply.status(403).send({ error: 'Access denied to this agent' });
      }

      // Delete agent
      await agentService.deleteAgent(validation.data.id);

      return reply.status(204).send();
    } catch (error) {
      if (error instanceof Error && error.message === 'Agent not found') {
        return reply.status(404).send({ error: 'Agent not found' });
      }
      fastify.log.error('Delete agent error:', error);
      return reply.status(500).send({ error: 'Failed to delete agent' });
    }
  });

  // Execute agent workflow
  fastify.post('/agents/:id/execute', async (request, reply) => {
    try {
      // Verify session
      const session = await auth.api.getSession({ headers: request.headers });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Validate params
      const paramsValidation = paramsSchema.safeParse(request.params);
      if (!paramsValidation.success) {
        return reply.status(400).send({
          error: 'Invalid agent ID',
          details: paramsValidation.error.flatten(),
        });
      }

      // Validate body
      const bodyValidation = executeWorkflowSchema.safeParse(request.body);
      if (!bodyValidation.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: bodyValidation.error.flatten(),
        });
      }

      // Get agent to verify access and get projectId
      const agent = await agentService.getAgent(paramsValidation.data.id);
      
      // Verify user has access to the agent's project
      const hasAccess = await verifyProjectAccess(session.user.id, agent.projectId);
      if (!hasAccess) {
        return reply.status(403).send({ error: 'Access denied to this agent' });
      }

      // Execute workflow
      const result = await agentService.executeWorkflow({
        agentId: paramsValidation.data.id,
        projectId: agent.projectId,
        inputs: bodyValidation.data.inputs || {},
      });

      return reply.send(result);
    } catch (error) {
      if (error instanceof Error && error.message === 'Agent not found') {
        return reply.status(404).send({ error: 'Agent not found' });
      }
      fastify.log.error('Execute workflow error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return reply.status(500).send({ 
        error: 'Failed to execute workflow',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      });
    }
  });
}