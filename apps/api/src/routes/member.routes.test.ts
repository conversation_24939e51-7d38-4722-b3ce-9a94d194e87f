import { FastifyInstance } from 'fastify'
import { PrismaClient } from '@prisma/client'
import { mockDeep } from 'jest-mock-extended'
import Fastify from 'fastify'
import { memberRoutes } from './member.routes'

// Mock auth module
jest.mock('../lib/auth', () => ({
  auth: {
    api: {
      getSession: jest.fn().mockResolvedValue({
        user: { id: 'user-123' }
      })
    }
  }
}))

describe('Member Routes', () => {
  let app: FastifyInstance
  let prisma: any

  beforeEach(async () => {
    prisma = mockDeep<PrismaClient>() as any
    app = Fastify()
    
    // Mock auth decorator
    app.decorate('authenticate', async (request: any, reply: any) => {
      request.user = { id: 'user-123' }
    })
    
    // Register member routes
    await app.register(memberRoutes, { prisma })
  })

  afterEach(async () => {
    await app.close()
  })

  describe('GET /api/projects/:projectId/members', () => {
    it('should return members for a project when user has access', async () => {
      const userId = 'user-123'
      const projectId = 'project-123'
      const mockProject = {
        id: projectId,
        name: 'Test Project',
        description: null,
        accessType: 'PERSONAL',
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      const mockMembers = [
        {
          userId,
          projectId,
          role: 'ADMIN',
          invitedAt: new Date().toISOString(),
          joinedAt: new Date().toISOString(),
          user: {
            id: userId,
            email: '<EMAIL>',
            name: 'Admin User',
            emailVerified: true,
            image: null,
            phone: null,
            country: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        },
      ]

      prisma.project.findFirst.mockResolvedValue(mockProject as any)
      prisma.projectMember.findMany.mockResolvedValue(mockMembers as any)

      const response = await app.inject({
        method: 'GET',
        url: `/api/projects/${projectId}/members`,
        headers: {
          authorization: 'Bearer test-token',
        },
      })

      expect(response.statusCode).toBe(200)
      expect(JSON.parse(response.body)).toEqual(mockMembers)
    })

    it('should return 404 when project not found', async () => {
      prisma.project.findFirst.mockResolvedValue(null)

      const response = await app.inject({
        method: 'GET',
        url: '/api/projects/non-existent/members',
        headers: {
          authorization: 'Bearer test-token',
        },
      })

      expect(response.statusCode).toBe(404)
      expect(JSON.parse(response.body)).toEqual({ error: 'Project not found' })
    })

    it('should return 401 when not authenticated', async () => {
      // Mock auth to return null for this test
      const { auth } = require('../lib/auth')
      auth.api.getSession.mockResolvedValueOnce(null)
      
      const response = await app.inject({
        method: 'GET',
        url: '/api/projects/project-123/members',
      })

      expect(response.statusCode).toBe(401)
    })
  })

  describe('POST /api/projects/:projectId/members', () => {
    it('should add a member when user is admin', async () => {
      const userId = 'user-123'
      const newUserId = 'new-user-123'
      const projectId = 'project-123'
      const mockCurrentMember = {
        userId,
        projectId,
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: userId,
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }
      const mockNewUser = {
        id: newUserId,
        email: '<EMAIL>',
        name: 'New User',
        emailVerified: true,
        image: null,
        phone: null,
        country: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
      const mockNewMember = {
        userId: newUserId,
        projectId,
        role: 'MEMBER',
        invitedAt: new Date().toISOString(),
        joinedAt: new Date().toISOString(),
        user: mockNewUser,
      }

      prisma.projectMember.findUnique
        .mockResolvedValueOnce(mockCurrentMember as any) // Current user check
        .mockResolvedValueOnce(null) // Existing member check
      prisma.user.findUnique.mockResolvedValue(mockNewUser as any)
      prisma.projectMember.create.mockResolvedValue(mockNewMember as any)

      const response = await app.inject({
        method: 'POST',
        url: `/api/projects/${projectId}/members`,
        headers: {
          authorization: 'Bearer test-token',
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          userId: newUserId,
          role: 'MEMBER',
        }),
      })

      expect(response.statusCode).toBe(201)
      expect(JSON.parse(response.body)).toEqual(mockNewMember)
    })

    it('should return 403 when user is not admin', async () => {
      const mockMember = {
        userId: 'user-123',
        projectId: 'project-123',
        role: 'MEMBER',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Member User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }

      prisma.projectMember.findUnique.mockResolvedValue(mockMember as any)

      const response = await app.inject({
        method: 'POST',
        url: '/api/projects/project-123/members',
        headers: {
          authorization: 'Bearer test-token',
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          userId: 'new-user-123',
        }),
      })

      expect(response.statusCode).toBe(403)
      expect(JSON.parse(response.body)).toEqual({ error: 'Only project admins can add members' })
    })

    it('should return 404 when user not found', async () => {
      const mockCurrentMember = {
        userId: 'user-123',
        projectId: 'project-123',
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }

      prisma.projectMember.findUnique.mockResolvedValue(mockCurrentMember as any)
      prisma.user.findUnique.mockResolvedValue(null)

      const response = await app.inject({
        method: 'POST',
        url: '/api/projects/project-123/members',
        headers: {
          authorization: 'Bearer test-token',
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          userId: 'non-existent-user',
        }),
      })

      expect(response.statusCode).toBe(404)
      expect(JSON.parse(response.body)).toEqual({ error: 'User not found' })
    })

    it('should return 409 when user is already a member', async () => {
      const mockCurrentMember = {
        userId: 'user-123',
        projectId: 'project-123',
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }
      const mockExistingMember = {
        userId: 'existing-user',
        projectId: 'project-123',
        role: 'MEMBER',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: 'existing-user',
          email: '<EMAIL>',
          name: 'Existing User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }

      prisma.projectMember.findUnique
        .mockResolvedValueOnce(mockCurrentMember as any)
        .mockResolvedValueOnce(mockExistingMember as any)
      prisma.user.findUnique.mockResolvedValue(mockExistingMember.user as any)

      const response = await app.inject({
        method: 'POST',
        url: '/api/projects/project-123/members',
        headers: {
          authorization: 'Bearer test-token',
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          userId: 'existing-user',
        }),
      })

      expect(response.statusCode).toBe(409)
      expect(JSON.parse(response.body)).toEqual({ error: 'User is already a member of this project' })
    })
  })

  describe('PUT /api/projects/:projectId/members/:userId', () => {
    it('should update member role when user is admin', async () => {
      const adminId = 'admin-123'
      const targetId = 'target-123'
      const projectId = 'project-123'
      const mockAdminMember = {
        userId: adminId,
        projectId,
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: adminId,
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }
      const mockTargetMember = {
        userId: targetId,
        projectId,
        role: 'MEMBER',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: targetId,
          email: '<EMAIL>',
          name: 'Target User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }
      const mockUpdatedMember = {
        ...mockTargetMember,
        role: 'ADMIN',
        invitedAt: mockTargetMember.invitedAt.toISOString(),
        joinedAt: mockTargetMember.joinedAt.toISOString(),
        user: {
          ...mockTargetMember.user,
          createdAt: mockTargetMember.user.createdAt.toISOString(),
          updatedAt: mockTargetMember.user.updatedAt.toISOString(),
        },
      }

      prisma.projectMember.findUnique
        .mockResolvedValueOnce(mockAdminMember as any)
        .mockResolvedValueOnce(mockTargetMember as any)
      prisma.projectMember.update.mockResolvedValue(mockUpdatedMember as any)

      const response = await app.inject({
        method: 'PUT',
        url: `/api/projects/${projectId}/members/${targetId}`,
        headers: {
          authorization: 'Bearer test-token',
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          role: 'ADMIN',
        }),
      })

      expect(response.statusCode).toBe(200)
      expect(JSON.parse(response.body)).toEqual(mockUpdatedMember)
    })

    it('should prevent demoting last admin', async () => {
      const adminId = 'user-123' // Use the same ID as mocked auth
      const projectId = 'project-123'
      const mockAdminMember = {
        userId: adminId,
        projectId,
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: adminId,
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }

      prisma.projectMember.findUnique
        .mockResolvedValueOnce(mockAdminMember as any)
        .mockResolvedValueOnce(mockAdminMember as any)
      prisma.projectMember.count.mockResolvedValue(1)

      const response = await app.inject({
        method: 'PUT',
        url: `/api/projects/${projectId}/members/${adminId}`,
        headers: {
          authorization: 'Bearer test-token',
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          role: 'MEMBER',
        }),
      })

      expect(response.statusCode).toBe(400)
      expect(JSON.parse(response.body)).toEqual({ error: 'Cannot demote the last admin' })
    })
  })

  describe('DELETE /api/projects/:projectId/members/:userId', () => {
    it('should remove member when user is admin', async () => {
      const adminId = 'admin-123'
      const targetId = 'target-123'
      const projectId = 'project-123'
      const mockAdminMember = {
        userId: adminId,
        projectId,
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: adminId,
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }
      const mockTargetMember = {
        userId: targetId,
        projectId,
        role: 'MEMBER',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: targetId,
          email: '<EMAIL>',
          name: 'Target User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }

      prisma.projectMember.findUnique
        .mockResolvedValueOnce(mockAdminMember as any)
        .mockResolvedValueOnce(mockTargetMember as any)
      prisma.projectMember.delete.mockResolvedValue(mockTargetMember as any)

      const response = await app.inject({
        method: 'DELETE',
        url: `/api/projects/${projectId}/members/${targetId}`,
        headers: {
          authorization: 'Bearer test-token',
        },
      })

      expect(response.statusCode).toBe(204)
    })

    it('should prevent removing last admin', async () => {
      const adminId = 'user-123' // Use the same ID as mocked auth
      const projectId = 'project-123'
      const mockAdminMember = {
        userId: adminId,
        projectId,
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: adminId,
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }

      prisma.projectMember.findUnique
        .mockResolvedValueOnce(mockAdminMember as any)
        .mockResolvedValueOnce(mockAdminMember as any)
      prisma.projectMember.count.mockResolvedValue(1)

      const response = await app.inject({
        method: 'DELETE',
        url: `/api/projects/${projectId}/members/${adminId}`,
        headers: {
          authorization: 'Bearer test-token',
        },
      })

      expect(response.statusCode).toBe(400)
      expect(JSON.parse(response.body)).toEqual({ error: 'Cannot remove the last admin' })
    })

    it('should return 404 when member not found', async () => {
      const mockAdminMember = {
        userId: 'admin-123',
        projectId: 'project-123',
        role: 'ADMIN',
        invitedAt: new Date(),
        joinedAt: new Date(),
        user: {
          id: 'admin-123',
          email: '<EMAIL>',
          name: 'Admin User',
          emailVerified: true,
          image: null,
          phone: null,
          country: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      }

      prisma.projectMember.findUnique
        .mockResolvedValueOnce(mockAdminMember as any)
        .mockResolvedValueOnce(null)

      const response = await app.inject({
        method: 'DELETE',
        url: '/api/projects/project-123/members/non-existent',
        headers: {
          authorization: 'Bearer test-token',
        },
      })

      expect(response.statusCode).toBe(404)
      expect(JSON.parse(response.body)).toEqual({ error: 'Member not found' })
    })
  })
})