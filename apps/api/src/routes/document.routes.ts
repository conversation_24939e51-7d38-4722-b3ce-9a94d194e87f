import type { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { documentRepository } from '../repositories';
import { createAuditLog } from '../lib/audit-log';
import { auth } from '../lib/auth';
import prisma from '../lib/prisma';
import { promises as fs } from 'fs';
import path from 'path';
import { randomUUID } from 'crypto';
import { DocumentProcessorService } from '../services/document-processor.service';
import { PdfExtractor } from '../services/extractors/pdf.extractor';
import { DocxExtractor } from '../services/extractors/docx.extractor';
import { TxtExtractor } from '../services/extractors/txt.extractor';

const ParamsSchema = z.object({
  id: z.string()
});

const UpdateDocumentsBodySchema = z.object({
  documentIds: z.array(z.string())
});

const PaginationQuerySchema = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(50),
  offset: z.coerce.number().min(0).optional().default(0),
  search: z.string().optional()
});

export default async function documentRoutes(fastify: FastifyInstance) {
  // Ensure prisma is available
  if (!fastify.prisma) {
    fastify.decorate('prisma', prisma);
  }

  // Register multipart support for file uploads
  await fastify.register(import('@fastify/multipart'), {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB
      files: 10 // Max 10 files per request
    }
  });

  // Get all documents for the user's workspace
  fastify.get('/documents', async (request, reply) => {
    try {
      // Check authentication
      const session = await auth.api.getSession({ headers: new Headers(request.headers as Record<string, string>) });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Get user's workspace
      const user = await fastify.prisma.user.findUnique({
        where: { id: session.user.id },
        include: {
          workspaceMembers: {
            include: {
              workspace: true
            }
          }
        }
      });

      if (!user || !user.workspaceMembers || user.workspaceMembers.length === 0) {
        return reply.code(400).send({ error: 'User has no workspace' });
      }

      const workspaceId = user.workspaceMembers[0].workspace.id;

      // Parse pagination params
      const query = PaginationQuerySchema.safeParse(request.query);
      if (!query.success) {
        return reply.code(400).send({ error: 'Invalid query parameters', details: query.error });
      }

      // Get documents for the workspace (including all statuses)
      const [documents, total] = await fastify.prisma.$transaction([
        fastify.prisma.knowledgeBaseDocument.findMany({
          where: {
            workspaceId
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: query.data.limit,
          skip: query.data.offset
        }),
        fastify.prisma.knowledgeBaseDocument.count({
          where: {
            workspaceId
          }
        })
      ]);

      return reply.send({
        documents,
        total,
        limit: query.data.limit,
        offset: query.data.offset
      });
    } catch (error) {
      fastify.log.error({ error }, 'Failed to get workspace documents');
      return reply.code(500).send({ error: 'Failed to get workspace documents' });
    }
  });

  // Upload documents to knowledge base
  fastify.post('/documents/upload', async (request, reply) => {
    try {
      // Check authentication
      const session = await auth.api.getSession({ headers: new Headers(request.headers as Record<string, string>) });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Get user's workspace
      const user = await fastify.prisma.user.findUnique({
        where: { id: session.user.id },
        include: {
          workspaceMembers: {
            include: {
              workspace: true
            }
          }
        }
      });

      if (!user) {
        fastify.log.error('User not found for session:', session.user.id);
        return reply.code(400).send({ error: 'User not found' });
      }

      if (!user.workspaceMembers || user.workspaceMembers.length === 0) {
        fastify.log.error('User has no workspace memberships:', user.id);
        return reply.code(400).send({ error: 'User has no workspace' });
      }

      const workspaceId = user.workspaceMembers[0].workspace.id;
      
      // Ensure upload directory exists
      const uploadDir = path.join(process.cwd(), 'uploads', workspaceId);
      await fs.mkdir(uploadDir, { recursive: true });

      const uploadedDocuments = [];
      let fileCount = 0;

      try {
        const files = request.files();
        
        for await (const file of files) {
          fileCount++;
          // Validate file type
          const allowedTypes = [
            'application/pdf',
            'text/plain',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          ];
          
          if (!allowedTypes.includes(file.mimetype)) {
            continue; // Skip invalid file types
          }

          // Generate unique filename
          const fileId = randomUUID();
          const fileExtension = path.extname(file.filename);
          const storedFileName = `${fileId}${fileExtension}`;
          const filePath = path.join(uploadDir, storedFileName);

          // Save file to disk
          const fileData = await file.toBuffer();
          await fs.writeFile(filePath, fileData);

          // Create document record in database
          const document = await fastify.prisma.knowledgeBaseDocument.create({
            data: {
              workspaceId,
              fileName: file.filename,
              storedFileName,
              fileType: file.mimetype,
              fileSize: fileData.length,
              status: 'PENDING'
            }
          });

          uploadedDocuments.push(document);

          // Create audit log
          await createAuditLog({
            userId: session.user.id,
            resourceType: 'DOCUMENT',
            resourceId: document.id,
            action: 'UPLOAD_DOCUMENT',
            metadata: {
              fileName: file.filename,
              fileType: file.mimetype,
              fileSize: fileData.length,
              workspaceId
            }
          });
        }
      } catch (fileError) {
        fastify.log.error({ error: fileError }, 'Error processing files');
        throw fileError;
      }

      if (fileCount === 0) {
        return reply.code(400).send({ error: 'No files were provided in the request' });
      }

      if (uploadedDocuments.length === 0) {
        return reply.code(400).send({ error: 'No valid files were uploaded. Please ensure files are PDF, TXT, or DOCX format.' });
      }

      fastify.log.info({ uploadedCount: uploadedDocuments.length }, 'Files uploaded successfully');
      
      // Auto-process uploaded documents
      if (uploadedDocuments.length > 0) {
        fastify.log.info('Auto-processing uploaded documents...');
        
        // Initialize document processor service
        const processorService = new DocumentProcessorService(fastify);
        
        // Register extractors
        processorService.registerExtractor('application/pdf', new PdfExtractor());
        processorService.registerExtractor('application/vnd.openxmlformats-officedocument.wordprocessingml.document', new DocxExtractor());
        processorService.registerExtractor('text/plain', new TxtExtractor());
        
        // Process each document asynchronously (don't wait for completion)
        for (const document of uploadedDocuments) {
          processorService.processDocument(document.id, session.user.id)
            .then(async result => {
              if (result.success) {
                fastify.log.info({ documentId: document.id }, 'Document processed successfully');
                
                // Create audit log for auto-processing
                await createAuditLog({
                  userId: session.user.id,
                  resourceType: 'DOCUMENT',
                  resourceId: document.id,
                  action: 'AUTO_PROCESS_DOCUMENT',
                  metadata: {
                    chunksCreated: result.chunks?.length || 0,
                    triggeredBy: 'upload'
                  }
                });
              } else {
                fastify.log.error({ documentId: document.id, error: result.error }, 'Failed to process document');
              }
            })
            .catch(error => {
              fastify.log.error({ documentId: document.id, error }, 'Error processing document');
            });
        }
      }
      
      return reply.send({ documents: uploadedDocuments });
    } catch (error) {
      fastify.log.error({ error }, 'Failed to upload documents');
      return reply.code(500).send({ error: 'Failed to upload documents' });
    }
  });

  // Get all workspace documents with association status for a project
  fastify.get('/projects/:id/available-documents', async (request, reply) => {
    try {
      // Validate params
      const params = ParamsSchema.safeParse(request.params);
      if (!params.success) {
        return reply.code(400).send({ error: 'Invalid parameters', details: params.error });
      }

      // Check authentication
      const session = await auth.api.getSession({ headers: new Headers(request.headers as Record<string, string>) });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Check if user is an admin of the project
      const projectMember = await fastify.prisma.projectMember.findUnique({
        where: {
          userId_projectId: {
            userId: session.user.id,
            projectId: params.data.id
          }
        },
        include: {
          project: true
        }
      });

      if (!projectMember || projectMember.role !== 'ADMIN') {
        return reply.code(403).send({ error: 'Admin access required' });
      }

      // Parse pagination params
      const query = PaginationQuerySchema.safeParse(request.query);
      if (!query.success) {
        return reply.code(400).send({ error: 'Invalid query parameters', details: query.error });
      }
      
      // Use project's workspaceId
      const workspaceId = projectMember.project.workspaceId;
      
      const result = await documentRepository.findAvailableForProject(
        params.data.id,
        workspaceId,
        query.data.limit,
        query.data.offset,
        query.data.search
      );
      
      return reply.send(result);
    } catch (error) {
      fastify.log.error({ error }, 'Failed to get available documents');
      return reply.code(500).send({ error: 'Failed to get available documents' });
    }
  });

  // Update document associations for a project
  fastify.put('/projects/:id/documents', async (request, reply) => {
    try {
      // Validate params and body
      const params = ParamsSchema.safeParse(request.params);
      if (!params.success) {
        return reply.code(400).send({ error: 'Invalid parameters', details: params.error });
      }
      
      const body = UpdateDocumentsBodySchema.safeParse(request.body);
      if (!body.success) {
        return reply.code(400).send({ error: 'Invalid request body', details: body.error });
      }

      // Check authentication
      const session = await auth.api.getSession({ headers: new Headers(request.headers as Record<string, string>) });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Check if user is an admin of the project
      const projectMember = await fastify.prisma.projectMember.findUnique({
        where: {
          userId_projectId: {
            userId: session.user.id,
            projectId: params.data.id
          }
        },
        include: {
          project: true
        }
      });

      if (!projectMember || projectMember.role !== 'ADMIN') {
        return reply.code(403).send({ error: 'Admin access required' });
      }
      
      const { documentIds } = body.data;

      // Validate that all documents belong to the same workspace as the project
      if (documentIds.length > 0) {
        const documents = await fastify.prisma.knowledgeBaseDocument.findMany({
          where: {
            id: { in: documentIds }
          },
          select: {
            id: true,
            workspaceId: true
          }
        });

        // Use project's workspaceId
        const projectWorkspaceId = projectMember.project.workspaceId;
        const invalidDocs = documents.filter(doc => doc.workspaceId !== projectWorkspaceId);
        if (invalidDocs.length > 0) {
          return reply.code(400).send({ 
            error: 'Invalid documents', 
            details: 'Some documents do not belong to the project workspace' 
          });
        }

        // Ensure all requested document IDs exist
        const foundIds = new Set(documents.map(d => d.id));
        const missingIds = documentIds.filter(id => !foundIds.has(id));
        if (missingIds.length > 0) {
          return reply.code(400).send({ 
            error: 'Invalid documents', 
            details: `Documents not found: ${missingIds.join(', ')}` 
          });
        }
      }
      
      // Get current associations for audit log
      const currentDocsResult = await documentRepository.findByProject(params.data.id);
      const currentDocIds = currentDocsResult.documents.map(d => d.id);
      
      // Update associations
      const updatedDocuments = await documentRepository.updateProjectDocuments(
        params.data.id,
        documentIds
      );

      // Create audit log
      await createAuditLog({
        userId: session.user.id,
        resourceType: 'PROJECT',
        resourceId: params.data.id,
        action: 'UPDATE_PROJECT_DOCUMENTS',
        metadata: {
          projectName: projectMember.project.name,
          previousDocumentIds: currentDocIds,
          newDocumentIds: documentIds,
          addedDocuments: documentIds.filter(id => !currentDocIds.includes(id)),
          removedDocuments: currentDocIds.filter(id => !documentIds.includes(id))
        }
      });

      return reply.send({ documents: updatedDocuments });
    } catch (error) {
      fastify.log.error({ error }, 'Failed to update project documents');
      return reply.code(500).send({ error: 'Failed to update project documents' });
    }
  });

  // Process document (extract text, chunk, and generate embeddings)
  fastify.post('/documents/:id/process', async (request, reply) => {
    try {
      // Validate params
      const params = ParamsSchema.safeParse(request.params);
      if (!params.success) {
        return reply.code(400).send({ error: 'Invalid parameters', details: params.error });
      }

      // Check authentication
      const session = await auth.api.getSession({ headers: new Headers(request.headers as Record<string, string>) });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Initialize document processor service
      const processorService = new DocumentProcessorService(fastify);
      
      // Register extractors
      processorService.registerExtractor('application/pdf', new PdfExtractor());
      processorService.registerExtractor('application/vnd.openxmlformats-officedocument.wordprocessingml.document', new DocxExtractor());
      processorService.registerExtractor('text/plain', new TxtExtractor());

      // Process the document
      const result = await processorService.processDocument(params.data.id, session.user.id);

      if (!result.success) {
        return reply.code(400).send({ error: result.error });
      }

      // Create audit log
      await createAuditLog({
        userId: session.user.id,
        resourceType: 'DOCUMENT',
        resourceId: params.data.id,
        action: 'PROCESS_DOCUMENT',
        metadata: {
          chunksCreated: result.chunks?.length || 0
        }
      });

      // Get the updated document
      const updatedDocument = await fastify.prisma.knowledgeBaseDocument.findUnique({
        where: { id: params.data.id }
      });
      
      return reply.send({ 
        message: 'Document processing completed successfully',
        document: updatedDocument,
        chunksCreated: result.chunks?.length || 0
      });
    } catch (error) {
      fastify.log.error({ error }, 'Failed to process document');
      return reply.code(500).send({ error: 'Failed to process document' });
    }
  });

  // Delete a document
  fastify.delete('/documents/:id', async (request, reply) => {
    try {
      // Validate params
      const params = ParamsSchema.safeParse(request.params);
      if (!params.success) {
        return reply.code(400).send({ error: 'Invalid parameters', details: params.error });
      }

      // Check authentication
      const session = await auth.api.getSession({ headers: new Headers(request.headers as Record<string, string>) });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      // Get the document and verify ownership
      const document = await fastify.prisma.knowledgeBaseDocument.findUnique({
        where: { id: params.data.id }
      });

      if (!document) {
        return reply.code(404).send({ error: 'Document not found' });
      }

      // Verify user has access to the workspace
      const userWorkspace = await fastify.prisma.workspaceMembers.findFirst({
        where: {
          userId: session.user.id,
          workspaceId: document.workspaceId
        }
      });

      if (!userWorkspace) {
        return reply.code(403).send({ error: 'Access denied' });
      }

      // Start transaction to delete document and associated data
      await fastify.prisma.$transaction(async (tx) => {
        // Delete all associated chunks first (though cascade should handle this)
        await tx.documentChunk.deleteMany({
          where: { documentId: params.data.id }
        });

        // Delete project associations
        await tx.projectDocuments.deleteMany({
          where: { documentId: params.data.id }
        });

        // Delete the document
        await tx.knowledgeBaseDocument.delete({
          where: { id: params.data.id }
        });
      });

      // Delete the physical file if it exists
      if (document.storedFileName) {
        try {
          const filePath = path.join(process.cwd(), 'uploads', document.workspaceId, document.storedFileName);
          await fs.unlink(filePath);
        } catch (fileError) {
          // Log error but don't fail the request if file deletion fails
          fastify.log.warn({ error: fileError, documentId: params.data.id }, 'Failed to delete physical file');
        }
      }

      // Create audit log
      await createAuditLog({
        userId: session.user.id,
        resourceType: 'DOCUMENT',
        resourceId: params.data.id,
        action: 'DELETE_DOCUMENT',
        metadata: {
          fileName: document.fileName,
          fileType: document.fileType,
          fileSize: document.fileSize,
          workspaceId: document.workspaceId
        }
      });

      return reply.code(204).send();
    } catch (error) {
      fastify.log.error({ error }, 'Failed to delete document');
      return reply.code(500).send({ error: 'Failed to delete document' });
    }
  });
}