import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { DeploymentService } from '../services/deployment.service.js';
import { AgentService } from '../services/agent.service.js';
import { DocumentationService } from '../services/documentation.service.js';

const publicApiParamsSchema = z.object({
  deploymentId: z.string().cuid(),
});

const executeBodySchema = z.object({
  inputs: z.record(z.any()).optional(),
});

export default async function publicApiRoutes(fastify: FastifyInstance) {
  const deploymentService = new DeploymentService(fastify);
  const agentService = new AgentService(fastify.prisma);
  const documentationService = new DocumentationService();

  // Register rate limiting for public API endpoints
  await fastify.register(import('@fastify/rate-limit'), {
    max: 100,
    timeWindow: '1 minute',
    keyGenerator: (request: FastifyRequest) => {
      return request.headers.authorization || request.ip;
    },
  });

  // Middleware to verify API key
  async function verifyApiKey(request: FastifyRequest, reply: FastifyReply) {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header',
      });
    }

    const apiKey = authHeader.substring(7);
    const deployment = await deploymentService.getDeploymentByApiKey(apiKey);

    if (!deployment) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Invalid API key',
      });
    }

    if (deployment.status !== 'ACTIVE') {
      return reply.status(403).send({
        error: 'Forbidden',
        message: 'Deployment is not active',
      });
    }

    const paramsValidation = publicApiParamsSchema.safeParse(request.params);
    if (!paramsValidation.success) {
      return reply.status(400).send({
        error: 'Invalid deployment ID',
        details: paramsValidation.error.flatten(),
      });
    }

    if (deployment.id !== paramsValidation.data.deploymentId) {
      return reply.status(403).send({
        error: 'Forbidden',
        message: 'API key does not match deployment',
      });
    }

    (request as FastifyRequest & { deployment: typeof deployment }).deployment = deployment;
  }

  // Get API documentation
  fastify.get('/api/v1/:deploymentId', async (request, reply) => {
    try {
      const validation = publicApiParamsSchema.safeParse(request.params);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid deployment ID',
          details: validation.error.flatten(),
        });
      }

      const deployment = await deploymentService.getDeployment(
        validation.data.deploymentId
      );

      if (!deployment) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Deployment not found',
        });
      }

      if (deployment.status !== 'ACTIVE') {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Deployment is not active',
        });
      }

      const agent = await agentService.getAgent(deployment.agentId);

      const documentation = documentationService.generateApiDocumentation(
        agent,
        deployment.deploymentUrl
      );

      return reply.send(documentation);
    } catch (error) {
      fastify.log.error('Get API documentation error:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get API documentation',
      });
    }
  });

  // Execute deployed agent
  fastify.post(
    '/api/v1/:deploymentId/execute',
    { preHandler: verifyApiKey },
    async (request: FastifyRequest & { deployment?: any }, reply) => {
      try {
        const bodyValidation = executeBodySchema.safeParse(request.body);
        if (!bodyValidation.success) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: 'Invalid request body',
            details: bodyValidation.error.flatten(),
          });
        }

        const agent = await agentService.getAgent(request.deployment.agentId);

        const result = await agentService.executeWorkflow({
          agentId: agent.id,
          projectId: agent.projectId,
          inputs: bodyValidation.data.inputs || {},
        });

        return reply.send({
          success: true,
          result,
        });
      } catch (error) {
        fastify.log.error('Execute API error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return reply.status(500).send({
          error: 'Internal Server Error',
          message: 'Failed to execute workflow',
          details: process.env.NODE_ENV === 'development' ? errorMessage : undefined,
        });
      }
    }
  );
}