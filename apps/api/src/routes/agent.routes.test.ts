import { FastifyInstance } from 'fastify';
import { build } from '../test/helper';
import { auth } from '../lib/auth';
import prisma from '../lib/prisma';

jest.mock('../lib/auth');
jest.mock('../lib/prisma', () => ({
  default: {
    project: {
      findFirst: jest.fn(),
    },
    agent: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

// Mock services
jest.mock('../services/agent.service');
jest.mock('../services/generation.service');
jest.mock('../services/document-search.service');

describe('Agent Routes', () => {
  let app: FastifyInstance;

  beforeEach(async () => {
    app = await build();
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await app.close();
  });

  const mockSession = {
    user: { id: 'user-123', email: '<EMAIL>' },
  };

  describe('POST /api/agents', () => {
    it('should create an agent with valid data and project access', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue({
        id: 'project-123',
        userId: 'user-123',
      });
      (prisma.agent.create as jest.Mock).mockResolvedValue({
        id: 'agent-123',
        title: 'Test Agent',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
        triggerType: 'web_app',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents',
        payload: {
          title: 'Test Agent',
          workflowJson: { blocks: [] },
          projectId: 'project-123',
          triggerType: 'web_app',
        },
        headers: {
          authorization: 'Bearer token',
        },
      });

      expect(response.statusCode).toBe(201);
      expect(response.json()).toHaveProperty('id', 'agent-123');
    });

    it('should reject when user lacks project access', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue(null);

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents',
        payload: {
          title: 'Test Agent',
          workflowJson: { blocks: [] },
          projectId: 'project-123',
        },
        headers: {
          authorization: 'Bearer token',
        },
      });

      expect(response.statusCode).toBe(403);
      expect(response.json()).toHaveProperty('error', 'Access denied to this project');
    });

    it('should reject oversized workflowJson', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      
      // Create a large payload (> 1MB)
      const largeData = { data: 'x'.repeat(1048577) };

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents',
        payload: {
          title: 'Test Agent',
          workflowJson: largeData,
          projectId: 'project-123',
        },
        headers: {
          authorization: 'Bearer token',
        },
      });

      expect(response.statusCode).toBe(400);
      expect(response.json().error).toContain('Validation failed');
    });

    it('should be rate limited after 10 requests', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue({
        id: 'project-123',
        userId: 'user-123',
      });
      (prisma.agent.create as jest.Mock).mockResolvedValue({
        id: 'agent-123',
        title: 'Test Agent',
        workflowJson: { blocks: [] },
        projectId: 'project-123',
      });

      // Make 10 requests (should succeed)
      for (let i = 0; i < 10; i++) {
        const response = await app.inject({
          method: 'POST',
          url: '/api/agents',
          payload: {
            title: `Test Agent ${i}`,
            workflowJson: { blocks: [] },
            projectId: 'project-123',
          },
          headers: {
            authorization: 'Bearer token',
          },
        });
        expect(response.statusCode).toBe(201);
      }

      // 11th request should be rate limited
      const response = await app.inject({
        method: 'POST',
        url: '/api/agents',
        payload: {
          title: 'Test Agent 11',
          workflowJson: { blocks: [] },
          projectId: 'project-123',
        },
        headers: {
          authorization: 'Bearer token',
        },
      });

      expect(response.statusCode).toBe(429);
    });
  });

  describe('GET /api/agents', () => {
    it('should return agents for projects user has access to', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue({
        id: 'project-123',
        userId: 'user-123',
      });
      (prisma.agent.findMany as jest.Mock).mockResolvedValue([
        {
          id: 'agent-1',
          title: 'Agent 1',
          projectId: 'project-123',
        },
        {
          id: 'agent-2',
          title: 'Agent 2',
          projectId: 'project-123',
        },
      ]);

      const response = await app.inject({
        method: 'GET',
        url: '/api/agents?projectId=project-123',
        headers: {
          authorization: 'Bearer token',
        },
      });

      expect(response.statusCode).toBe(200);
      expect(response.json()).toHaveLength(2);
    });

    it('should deny access to projects user does not own', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue(null);

      const response = await app.inject({
        method: 'GET',
        url: '/api/agents?projectId=project-456',
        headers: {
          authorization: 'Bearer token',
        },
      });

      expect(response.statusCode).toBe(403);
      expect(response.json()).toHaveProperty('error', 'Access denied to this project');
    });
  });

  describe('Security Headers', () => {
    it('should not expose sensitive information in errors', async () => {
      (auth.api.getSession as jest.Mock).mockRejectedValue(new Error('Database connection failed'));

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents',
        payload: {
          title: 'Test',
          workflowJson: {},
          projectId: 'project-123',
        },
        headers: {
          authorization: 'Bearer token',
        },
      });

      expect(response.statusCode).toBe(500);
      expect(response.json()).toEqual({ error: 'Failed to create agent' });
      // Should not expose internal error details
      expect(JSON.stringify(response.json())).not.toContain('Database connection failed');
    });
  });

  describe('POST /api/agents/:id/execute', () => {
    const mockAgent = {
      id: 'agent-123',
      projectId: 'project-123',
      title: 'Test Agent',
      workflowJson: [
        {
          id: 'block-1',
          type: 'generation',
          props: {
            model: 'openai/gpt-3.5-turbo',
            temperature: 0.7,
          },
          content: [{ type: 'text', text: 'Generate test content' }],
        },
      ],
    };

    it('should execute agent workflow successfully', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue({
        id: 'project-123',
        userId: 'user-123',
      });

      const mockExecutionResult = {
        success: true,
        outputs: {
          'block-1': {
            content: 'Generated test response',
            model: 'openai/gpt-3.5-turbo',
          },
        },
      };

      // Mock the agent service methods
      const AgentService = require('../services/agent.service').AgentService;
      AgentService.prototype.getAgent = jest.fn().mockResolvedValue(mockAgent);
      AgentService.prototype.executeWorkflow = jest.fn().mockResolvedValue(mockExecutionResult);

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents/agent-123/execute',
        headers: {
          authorization: 'Bearer test-token',
        },
        payload: {
          inputs: {
            name: 'Test User',
            topic: 'Testing',
          },
        },
      });

      expect(response.statusCode).toBe(200);
      expect(response.json()).toEqual(mockExecutionResult);
      expect(AgentService.prototype.executeWorkflow).toHaveBeenCalledWith({
        agentId: 'agent-123',
        projectId: 'project-123',
        inputs: {
          name: 'Test User',
          topic: 'Testing',
        },
      });
    });

    it('should return 401 if unauthorized', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(null);

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents/agent-123/execute',
        headers: {
          authorization: 'Bearer invalid-token',
        },
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(401);
      expect(response.json()).toEqual({ error: 'Unauthorized' });
    });

    it('should return 403 if user does not have access', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue(null);

      const AgentService = require('../services/agent.service').AgentService;
      AgentService.prototype.getAgent = jest.fn().mockResolvedValue(mockAgent);

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents/agent-123/execute',
        headers: {
          authorization: 'Bearer test-token',
        },
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(403);
      expect(response.json()).toEqual({ error: 'Access denied to this agent' });
    });

    it('should return 404 if agent not found', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);

      const AgentService = require('../services/agent.service').AgentService;
      AgentService.prototype.getAgent = jest.fn().mockRejectedValue(new Error('Agent not found'));

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents/non-existent/execute',
        headers: {
          authorization: 'Bearer test-token',
        },
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(404);
      expect(response.json()).toEqual({ error: 'Agent not found' });
    });

    it('should handle execution errors gracefully', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue({
        id: 'project-123',
        userId: 'user-123',
      });

      const AgentService = require('../services/agent.service').AgentService;
      AgentService.prototype.getAgent = jest.fn().mockResolvedValue(mockAgent);
      AgentService.prototype.executeWorkflow = jest.fn()
        .mockRejectedValue(new Error('Execution failed'));

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents/agent-123/execute',
        headers: {
          authorization: 'Bearer test-token',
        },
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(500);
      expect(response.json()).toHaveProperty('error', 'Failed to execute workflow');
    });

    it('should handle partial execution failure', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      (prisma.project.findFirst as jest.Mock).mockResolvedValue({
        id: 'project-123',
        userId: 'user-123',
      });

      const mockPartialResult = {
        success: false,
        outputs: {
          'block-1': { content: 'Partial success' },
        },
        errors: ['Block block-2: Generation failed'],
      };

      const AgentService = require('../services/agent.service').AgentService;
      AgentService.prototype.getAgent = jest.fn().mockResolvedValue(mockAgent);
      AgentService.prototype.executeWorkflow = jest.fn().mockResolvedValue(mockPartialResult);

      const response = await app.inject({
        method: 'POST',
        url: '/api/agents/agent-123/execute',
        headers: {
          authorization: 'Bearer test-token',
        },
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(200);
      expect(response.json()).toEqual(mockPartialResult);
    });
  });
});