import { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { auth } from '../lib/auth';
import prisma from '../lib/prisma';

const searchSchema = z.object({
  q: z.string().min(2, "Search query must be at least 2 characters"),
});

export async function userRoutes(fastify: FastifyInstance) {
  // Search users
  fastify.get<{
    Querystring: { q: string }
  }>('/api/users/search', async (request, reply) => {
    try {
      const session = await auth.api.getSession({ headers: request.headers as any });
      if (!session?.user) {
        return reply.status(401).send({ error: 'Unauthorized' });
      }

      const validation = searchSchema.safeParse(request.query);
      if (!validation.success) {
        return reply.status(400).send({
          error: 'Invalid search query',
          details: validation.error.flatten(),
        });
      }

      const { q } = validation.data;
      
      // Search users by name or email
      const users = await prisma.user.findMany({
        where: {
          OR: [
            {
              name: {
                contains: q,
                mode: 'insensitive',
              },
            },
            {
              email: {
                contains: q,
                mode: 'insensitive',
              },
            },
          ],
        },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true,
          image: true,
          phone: true,
          country: true,
          createdAt: true,
          updatedAt: true,
        },
        take: 20, // Limit results
      });

      return reply.send(users);
    } catch (error) {
      fastify.log.error('Search users error:', error);
      return reply.status(500).send({ error: 'Failed to search users' });
    }
  });
}