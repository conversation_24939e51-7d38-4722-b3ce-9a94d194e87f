import type { FastifyInstance } from 'fastify';
import searchRoutes from './search.routes';
import { documentRepository } from '../repositories/document.repository';

// Mock dependencies
jest.mock('../repositories/document.repository');
jest.mock('../lib/auth', () => ({
  auth: {
    api: {
      getSession: jest.fn()
    }
  }
}));

describe('Search Routes', () => {
  let fastify: FastifyInstance;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock Fastify instance
    fastify = {
      register: jest.fn(),
      post: jest.fn(),
      log: {
        error: jest.fn()
      },
      prisma: {
        projectMember: {
          findUnique: jest.fn()
        },
        documentChunk: {
          findMany: jest.fn()
        }
      },
      decorateRequest: jest.fn(),
      addHook: jest.fn()
    } as any;

    // Call the routes registration
    searchRoutes(fastify);
  });

  describe('POST /search/documents', () => {
    it('should be registered', () => {
      expect(fastify.post).toHaveBeenCalledWith(
        '/search/documents',
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should return filtered documents for project member', async () => {
      const mockDocs = [
        { id: 'doc1', fileName: 'test1.pdf', fileType: 'application/pdf', fileSize: 1024, uploadedAt: new Date() },
        { id: 'doc2', fileName: 'test2.md', fileType: 'text/markdown', fileSize: 512, uploadedAt: new Date() },
        { id: 'doc3', fileName: 'other.txt', fileType: 'text/plain', fileSize: 256, uploadedAt: new Date() }
      ];

      (documentRepository.findByProject as jest.Mock).mockResolvedValue(mockDocs);

      // Get the registered handler
      const [, options, handler] = (fastify.post as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/search/documents'
      );

      const mockRequest = {
        body: { query: 'test', projectId: 'project123' },
        getSession: jest.fn().mockResolvedValue({ user: { id: 'user123' } })
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      // Mock preHandler to pass
      (fastify.prisma.projectMember.findUnique as jest.Mock).mockResolvedValue({
        userId: 'user123',
        projectId: 'project123',
        role: 'MEMBER'
      });

      // Execute preHandler
      await options.preHandler(mockRequest, mockReply);

      // Execute handler
      await handler(mockRequest, mockReply);

      expect(documentRepository.findByProject).toHaveBeenCalledWith('project123');
      expect(mockReply.send).toHaveBeenCalledWith({
        documents: [
          expect.objectContaining({ id: 'doc1', fileName: 'test1.pdf' }),
          expect.objectContaining({ id: 'doc2', fileName: 'test2.md' })
        ],
        total: 2
      });
    });

    it('should reject non-project members', async () => {
      const [, options] = (fastify.post as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/search/documents'
      );

      const mockRequest = {
        body: { query: 'test', projectId: 'project123' },
        getSession: jest.fn().mockResolvedValue({ user: { id: 'user123' } })
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      // Mock preHandler to return no membership
      (fastify.prisma.projectMember.findUnique as jest.Mock).mockResolvedValue(null);

      // Execute preHandler
      await options.preHandler(mockRequest, mockReply);

      expect(mockReply.code).toHaveBeenCalledWith(403);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Access denied' });
    });
  });

  describe('POST /search/chunks', () => {
    it('should be registered', () => {
      expect(fastify.post).toHaveBeenCalledWith(
        '/search/chunks',
        expect.any(Object),
        expect.any(Function)
      );
    });

    it('should return chunks from project documents only', async () => {
      const mockDocs = [
        { id: 'doc1', fileName: 'test1.pdf' },
        { id: 'doc2', fileName: 'test2.md' }
      ];

      const mockChunks = [
        {
          id: 'chunk1',
          content: 'This is a test chunk',
          documentId: 'doc1',
          document: { id: 'doc1', fileName: 'test1.pdf', fileType: 'application/pdf' },
          metadata: {},
          createdAt: new Date()
        }
      ];

      (documentRepository.findByProject as jest.Mock).mockResolvedValue(mockDocs);
      (fastify.prisma.documentChunk.findMany as jest.Mock).mockResolvedValue(mockChunks);

      const [, options, handler] = (fastify.post as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/search/chunks'
      );

      const mockRequest = {
        body: { query: 'test', projectId: 'project123', limit: 10 },
        getSession: jest.fn().mockResolvedValue({ user: { id: 'user123' } })
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      // Mock preHandler to pass
      (fastify.prisma.projectMember.findUnique as jest.Mock).mockResolvedValue({
        userId: 'user123',
        projectId: 'project123',
        role: 'MEMBER'
      });

      // Execute preHandler
      await options.preHandler(mockRequest, mockReply);

      // Execute handler
      await handler(mockRequest, mockReply);

      expect(documentRepository.findByProject).toHaveBeenCalledWith('project123');
      expect(fastify.prisma.documentChunk.findMany).toHaveBeenCalledWith({
        where: {
          documentId: { in: ['doc1', 'doc2'] },
          content: { contains: 'test', mode: 'insensitive' }
        },
        include: {
          document: {
            select: { id: true, fileName: true, fileType: true }
          }
        },
        take: 10,
        orderBy: { createdAt: 'desc' }
      });
      expect(mockReply.send).toHaveBeenCalledWith({
        chunks: expect.arrayContaining([
          expect.objectContaining({
            id: 'chunk1',
            content: 'This is a test chunk',
            documentId: 'doc1'
          })
        ]),
        total: 1
      });
    });

    it('should return empty results if project has no documents', async () => {
      (documentRepository.findByProject as jest.Mock).mockResolvedValue([]);

      const [, options, handler] = (fastify.post as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/search/chunks'
      );

      const mockRequest = {
        body: { query: 'test', projectId: 'project123' },
        getSession: jest.fn().mockResolvedValue({ user: { id: 'user123' } })
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      // Mock preHandler to pass
      (fastify.prisma.projectMember.findUnique as jest.Mock).mockResolvedValue({
        userId: 'user123',
        projectId: 'project123',
        role: 'MEMBER'
      });

      // Execute preHandler
      await options.preHandler(mockRequest, mockReply);

      // Execute handler
      await handler(mockRequest, mockReply);

      expect(mockReply.send).toHaveBeenCalledWith({ chunks: [], total: 0 });
      expect(fastify.prisma.documentChunk.findMany).not.toHaveBeenCalled();
    });
  });
});