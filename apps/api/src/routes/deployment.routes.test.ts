import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import fastify, { FastifyInstance } from 'fastify';
import deploymentRoutes from './deployment.routes';
import { auth } from '../lib/auth';

jest.mock('../lib/auth');
jest.mock('../services/deployment.service');

describe('Deployment Routes', () => {
  let app: FastifyInstance;

  beforeEach(async () => {
    app = fastify({ logger: false });
    await app.register(deploymentRoutes);
    await app.ready();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('POST /agents/:id/deploy', () => {
    it('should deploy an agent successfully', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      };
      
      const mockDeployment = {
        id: 'deploy123',
        agentId: 'agent123',
        deploymentUrl: 'http://localhost:3001/api/v1/deploy123',
        apiKey: 'sk_test123',
        status: 'ACTIVE',
        createdAt: new Date(),
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      
      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.createDeployment = jest.fn().mockResolvedValue(mockDeployment);

      const response = await app.inject({
        method: 'POST',
        url: '/agents/agent123/deploy',
        headers: {
          cookie: 'better-auth.session_token=test123',
        },
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body)).toEqual({
        id: mockDeployment.id,
        agentId: mockDeployment.agentId,
        deploymentUrl: mockDeployment.deploymentUrl,
        apiKey: mockDeployment.apiKey,
        status: mockDeployment.status,
        createdAt: mockDeployment.createdAt.toISOString(),
      });
    });

    it('should return 401 if not authenticated', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(null);

      const response = await app.inject({
        method: 'POST',
        url: '/agents/agent123/deploy',
      });

      expect(response.statusCode).toBe(401);
      expect(JSON.parse(response.body)).toEqual({ error: 'Unauthorized' });
    });

    it('should return 404 if agent not found', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      
      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.createDeployment = jest.fn()
        .mockRejectedValue(new Error('Agent not found'));

      const response = await app.inject({
        method: 'POST',
        url: '/agents/agent123/deploy',
        headers: {
          cookie: 'better-auth.session_token=test123',
        },
      });

      expect(response.statusCode).toBe(404);
      expect(JSON.parse(response.body)).toEqual({ error: 'Agent not found' });
    });

    it('should return 403 if user lacks access', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      
      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.createDeployment = jest.fn()
        .mockRejectedValue(new Error('Unauthorized: User does not have access'));

      const response = await app.inject({
        method: 'POST',
        url: '/agents/agent123/deploy',
        headers: {
          cookie: 'better-auth.session_token=test123',
        },
      });

      expect(response.statusCode).toBe(403);
      expect(JSON.parse(response.body)).toEqual({ 
        error: 'Unauthorized: User does not have access' 
      });
    });
  });

  describe('GET /agents/:id/deployments', () => {
    it('should get deployments for an agent', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      };
      
      const mockDeployments = [
        {
          id: 'deploy123',
          agentId: 'agent123',
          deploymentUrl: 'http://localhost:3001/api/v1/deploy123',
          status: 'ACTIVE',
        },
        {
          id: 'deploy456',
          agentId: 'agent123',
          deploymentUrl: 'http://localhost:3001/api/v1/deploy456',
          status: 'INACTIVE',
        },
      ];

      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      
      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeploymentsByAgent = jest.fn()
        .mockResolvedValue(mockDeployments);

      const response = await app.inject({
        method: 'GET',
        url: '/agents/agent123/deployments',
        headers: {
          cookie: 'better-auth.session_token=test123',
        },
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body)).toEqual({ deployments: mockDeployments });
    });
  });

  describe('PATCH /deployments/:id', () => {
    it('should update deployment status', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      };
      
      const mockDeployment = {
        id: 'deploy123',
        status: 'INACTIVE',
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      
      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.updateDeploymentStatus = jest.fn()
        .mockResolvedValue(mockDeployment);

      const response = await app.inject({
        method: 'PATCH',
        url: '/deployments/deploy123',
        headers: {
          cookie: 'better-auth.session_token=test123',
        },
        payload: {
          status: 'INACTIVE',
        },
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body)).toEqual(mockDeployment);
    });

    it('should return 400 for invalid status', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);

      const response = await app.inject({
        method: 'PATCH',
        url: '/deployments/deploy123',
        headers: {
          cookie: 'better-auth.session_token=test123',
        },
        payload: {
          status: 'INVALID_STATUS',
        },
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.body)).toHaveProperty('error', 'Invalid request body');
    });
  });

  describe('DELETE /deployments/:id', () => {
    it('should delete deployment successfully', async () => {
      const mockSession = {
        user: { id: 'user123', email: '<EMAIL>' },
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue(mockSession);
      
      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.deleteDeployment = jest.fn().mockResolvedValue(undefined);

      const response = await app.inject({
        method: 'DELETE',
        url: '/deployments/deploy123',
        headers: {
          cookie: 'better-auth.session_token=test123',
        },
      });

      expect(response.statusCode).toBe(204);
    });
  });
});