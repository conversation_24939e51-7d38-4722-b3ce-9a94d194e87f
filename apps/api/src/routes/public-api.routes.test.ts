import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import fastify, { FastifyInstance } from 'fastify';
import publicApiRoutes from './public-api.routes';

jest.mock('../services/deployment.service');
jest.mock('../services/agent.service');
jest.mock('../services/documentation.service');

describe('Public API Routes', () => {
  let app: FastifyInstance;

  beforeEach(async () => {
    app = fastify({ logger: false });
    await app.register(publicApiRoutes);
    await app.ready();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /api/v1/:deploymentId', () => {
    it('should return API documentation for active deployment', async () => {
      const mockDeployment = {
        id: 'deploy123',
        agentId: 'agent123',
        deploymentUrl: 'http://localhost:3001/api/v1/deploy123',
        status: 'ACTIVE',
      };

      const mockAgent = {
        id: 'agent123',
        title: 'Test Agent',
        workflowJson: { blocks: [] },
      };

      const mockDocumentation = {
        title: 'Test Agent',
        description: 'API for Test Agent',
        version: '1.0.0',
        endpoint: {
          url: 'http://localhost:3001/api/v1/deploy123/execute',
          method: 'POST',
          headers: {
            'Authorization': 'Bearer YOUR_API_KEY',
            'Content-Type': 'application/json',
          },
        },
        inputs: [],
        examples: {
          curl: 'curl example',
          javascript: 'js example',
          python: 'python example',
        },
      };

      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeployment = jest.fn().mockResolvedValue(mockDeployment);

      const { AgentService } = jest.requireMock('../services/agent.service') as { AgentService: any };
      AgentService.prototype.getAgent = jest.fn().mockResolvedValue(mockAgent);

      const { DocumentationService } = jest.requireMock('../services/documentation.service') as { DocumentationService: any };
      DocumentationService.prototype.generateApiDocumentation = jest.fn()
        .mockReturnValue(mockDocumentation);

      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/deploy123',
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body)).toEqual(mockDocumentation);
    });

    it('should return 404 if deployment not found', async () => {
      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeployment = jest.fn().mockResolvedValue(null);

      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/deploy123',
      });

      expect(response.statusCode).toBe(404);
      expect(JSON.parse(response.body)).toEqual({
        error: 'Not Found',
        message: 'Deployment not found',
      });
    });

    it('should return 403 if deployment is inactive', async () => {
      const mockDeployment = {
        id: 'deploy123',
        status: 'INACTIVE',
      };

      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeployment = jest.fn().mockResolvedValue(mockDeployment);

      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/deploy123',
      });

      expect(response.statusCode).toBe(403);
      expect(JSON.parse(response.body)).toEqual({
        error: 'Forbidden',
        message: 'Deployment is not active',
      });
    });
  });

  describe('POST /api/v1/:deploymentId/execute', () => {
    it('should execute workflow with valid API key', async () => {
      const mockDeployment = {
        id: 'deploy123',
        agentId: 'agent123',
        apiKey: 'sk_test123',
        status: 'ACTIVE',
        agent: {
          projectId: 'project123',
        },
      };

      const mockAgent = {
        id: 'agent123',
        projectId: 'project123',
      };

      const mockResult = {
        output: 'Workflow executed successfully',
      };

      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeploymentByApiKey = jest.fn()
        .mockResolvedValue(mockDeployment);

      const { AgentService } = jest.requireMock('../services/agent.service') as { AgentService: any };
      AgentService.prototype.getAgent = jest.fn().mockResolvedValue(mockAgent);
      AgentService.prototype.executeWorkflow = jest.fn().mockResolvedValue(mockResult);

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/deploy123/execute',
        headers: {
          'Authorization': 'Bearer sk_test123',
          'Content-Type': 'application/json',
        },
        payload: {
          inputs: {
            name: 'test',
          },
        },
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body)).toEqual({
        success: true,
        result: mockResult,
      });
    });

    it('should return 401 if API key is missing', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/deploy123/execute',
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(401);
      expect(JSON.parse(response.body)).toEqual({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header',
      });
    });

    it('should return 401 if API key is invalid', async () => {
      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeploymentByApiKey = jest.fn().mockResolvedValue(null);

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/deploy123/execute',
        headers: {
          'Authorization': 'Bearer invalid_key',
        },
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(401);
      expect(JSON.parse(response.body)).toEqual({
        error: 'Unauthorized',
        message: 'Invalid API key',
      });
    });

    it('should return 403 if deployment is inactive', async () => {
      const mockDeployment = {
        id: 'deploy123',
        status: 'INACTIVE',
      };

      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeploymentByApiKey = jest.fn()
        .mockResolvedValue(mockDeployment);

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/deploy123/execute',
        headers: {
          'Authorization': 'Bearer sk_test123',
        },
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(403);
      expect(JSON.parse(response.body)).toEqual({
        error: 'Forbidden',
        message: 'Deployment is not active',
      });
    });

    it('should return 403 if API key does not match deployment', async () => {
      const mockDeployment = {
        id: 'deploy456', // Different from requested deployment
        status: 'ACTIVE',
      };

      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeploymentByApiKey = jest.fn()
        .mockResolvedValue(mockDeployment);

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/deploy123/execute',
        headers: {
          'Authorization': 'Bearer sk_test123',
        },
        payload: {
          inputs: {},
        },
      });

      expect(response.statusCode).toBe(403);
      expect(JSON.parse(response.body)).toEqual({
        error: 'Forbidden',
        message: 'API key does not match deployment',
      });
    });

    it('should return 400 for invalid request body', async () => {
      const mockDeployment = {
        id: 'deploy123',
        agentId: 'agent123',
        status: 'ACTIVE',
      };

      const { DeploymentService } = jest.requireMock('../services/deployment.service') as { DeploymentService: any };
      DeploymentService.prototype.getDeploymentByApiKey = jest.fn()
        .mockResolvedValue(mockDeployment);

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/deploy123/execute',
        headers: {
          'Authorization': 'Bearer sk_test123',
        },
        payload: {
          inputs: 'invalid', // Should be an object
        },
      });

      expect(response.statusCode).toBe(400);
      expect(JSON.parse(response.body)).toHaveProperty('error', 'Bad Request');
    });
  });
});