import type { FastifyInstance } from 'fastify'
import documentRoutes from './document.routes'
import { auth } from '../lib/auth'
import { createAuditLog } from '../lib/audit-log'
import { DocumentProcessorService } from '../services/document-processor.service'

jest.mock('../lib/auth', () => ({
  auth: {
    api: {
      getSession: jest.fn()
    }
  }
}))
jest.mock('../lib/audit-log')
jest.mock('../services/document-processor.service')

describe('Document Routes - Process Endpoint', () => {
  let fastify: FastifyInstance
  let mockSession: any
  let mockUser: any
  let processHandler: any

  beforeEach(async () => {
    jest.clearAllMocks()

    mockUser = {
      id: 'user123',
      email: '<EMAIL>',
      workspaceMemberships: [{
        workspaceId: 'ws123',
        role: 'ADMIN'
      }]
    }

    mockDocument = {
      id: 'doc123',
      workspaceId: 'ws123',
      fileName: 'test.pdf',
      storedFileName: 'stored-file.pdf',
      fileType: 'application/pdf',
      status: 'pending'
    }

    mockSession = {
      user: { id: mockUser.id }
    }

    ;(auth.api.getSession as jest.Mock).mockResolvedValue(mockSession)
    ;(createAuditLog as jest.Mock).mockResolvedValue(undefined)
    
    // Create mock fastify instance
    fastify = {
      register: jest.fn(),
      get: jest.fn(),
      put: jest.fn(),
      post: jest.fn((path: string, handler: any) => {
        if (path === '/documents/:id/process') {
          processHandler = handler
        }
      }),
      log: {
        error: jest.fn(),
        info: jest.fn(),
        warn: jest.fn()
      },
      prisma: {
        projectMember: {
          findUnique: jest.fn()
        },
        knowledgeBaseDocument: {
          findMany: jest.fn(),
          create: jest.fn()
        },
        user: {
          findUnique: jest.fn()
        }
      },
      decorate: jest.fn()
    } as unknown as FastifyInstance

    // Register routes
    await documentRoutes(fastify)
  })

  describe('POST /documents/:id/process', () => {
    it('should process document successfully', async () => {
      const mockProcessResult = {
        success: true,
        chunks: [
          { content: 'chunk1', embedding: new Array(384).fill(0.1), metadata: {} },
          { content: 'chunk2', embedding: new Array(384).fill(0.2), metadata: {} }
        ]
      }

      ;(DocumentProcessorService.prototype.processDocument as jest.Mock).mockResolvedValue(mockProcessResult)
      ;(DocumentProcessorService.prototype.registerExtractor as jest.Mock).mockImplementation(() => {})

      const mockRequest = {
        params: { id: 'doc123' },
        headers: { 'content-type': 'application/json' }
      }
      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      }

      await processHandler(mockRequest, mockReply)

      expect(mockReply.send).toHaveBeenCalledWith({
        message: 'Document processing completed successfully',
        documentId: 'doc123',
        chunksCreated: 2
      })
      expect(createAuditLog).toHaveBeenCalledWith({
        userId: 'user123',
        resourceType: 'DOCUMENT',
        resourceId: 'doc123',
        action: 'PROCESS_DOCUMENT',
        metadata: {
          chunksCreated: 2
        }
      })
    })

    it('should return 401 if not authenticated', async () => {
      ;(auth.api.getSession as jest.Mock).mockResolvedValue(null)

      const mockRequest = {
        params: { id: 'doc123' },
        headers: { 'content-type': 'application/json' }
      }
      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      }

      await processHandler(mockRequest, mockReply)

      expect(mockReply.status).toHaveBeenCalledWith(401)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized' })
    })

    it('should return 400 if document not found', async () => {
      const mockProcessResult = {
        success: false,
        error: 'Document not found'
      }

      ;(DocumentProcessorService.prototype.processDocument as jest.Mock).mockResolvedValue(mockProcessResult)
      ;(DocumentProcessorService.prototype.registerExtractor as jest.Mock).mockImplementation(() => {})

      const mockRequest = {
        params: { id: 'doc123' },
        headers: { 'content-type': 'application/json' }
      }
      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      }

      await processHandler(mockRequest, mockReply)

      expect(mockReply.code).toHaveBeenCalledWith(400)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Document not found' })
    })

    it('should return 400 if processing fails', async () => {
      const mockProcessResult = {
        success: false,
        error: 'Failed to extract text from document'
      }

      ;(DocumentProcessorService.prototype.processDocument as jest.Mock).mockResolvedValue(mockProcessResult)
      ;(DocumentProcessorService.prototype.registerExtractor as jest.Mock).mockImplementation(() => {})

      const mockRequest = {
        params: { id: 'doc123' },
        headers: { 'content-type': 'application/json' }
      }
      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      }

      await processHandler(mockRequest, mockReply)

      expect(mockReply.code).toHaveBeenCalledWith(400)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Failed to extract text from document' })
    })

    it('should handle server errors gracefully', async () => {
      ;(DocumentProcessorService.prototype.processDocument as jest.Mock).mockRejectedValue(new Error('Server error'))
      ;(DocumentProcessorService.prototype.registerExtractor as jest.Mock).mockImplementation(() => {})

      const mockRequest = {
        params: { id: 'doc123' },
        headers: { 'content-type': 'application/json' }
      }
      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      }

      await processHandler(mockRequest, mockReply)

      expect(mockReply.code).toHaveBeenCalledWith(500)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Failed to process document' })
      expect(fastify.log.error).toHaveBeenCalled()
    })
  })
})