import { describe, it, expect, beforeEach, vi } from 'vitest'
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { projectRoutes } from './project.routes'
import { ProjectRepository } from '../repositories/project.repository'
import { DocumentRepository } from '../repositories/document.repository'
import { auth } from '../lib/auth'

// Mock dependencies
vi.mock('../lib/auth', () => ({
  auth: {
    api: {
      getSession: vi.fn(),
    },
  },
}))

vi.mock('../repositories/project.repository')
vi.mock('../repositories/document.repository')

describe('Project Routes', () => {
  let fastify: FastifyInstance
  let mockProjectRepository: any
  let mockDocumentRepository: any
  let mockRequest: Partial<FastifyRequest>
  let mockReply: Partial<FastifyReply>

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock Fastify instance
    fastify = {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
      log: {
        error: vi.fn(),
      },
    } as any

    // Mock repositories
    mockProjectRepository = {
      findByUserId: vi.fn(),
      findByIdAndUserId: vi.fn(),
      create: vi.fn(),
      updateByIdAndUserId: vi.fn(),
      deleteByIdAndUserId: vi.fn(),
    }
    ;(ProjectRepository as any).mockImplementation(() => mockProjectRepository)

    mockDocumentRepository = {
      findByProject: vi.fn(),
    }
    ;(DocumentRepository as any).mockImplementation(() => mockDocumentRepository)

    // Mock request and reply
    mockRequest = {
      headers: {},
      body: {},
      params: {},
    }

    mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn().mockReturnThis(),
    }
  })

  describe('Route Registration', () => {
    it('should register all project routes', async () => {
      await projectRoutes(fastify)

      expect(fastify.get).toHaveBeenCalledWith('/projects', expect.any(Function))
      expect(fastify.post).toHaveBeenCalledWith('/projects', expect.any(Function))
      expect(fastify.get).toHaveBeenCalledWith('/projects/:id', expect.any(Function))
      expect(fastify.put).toHaveBeenCalledWith('/projects/:id', expect.any(Function))
      expect(fastify.delete).toHaveBeenCalledWith('/projects/:id', expect.any(Function))
      expect(fastify.get).toHaveBeenCalledWith('/projects/:id/documents', expect.any(Function))
    })
  })

  describe('GET /api/projects', () => {
    let handler: Function

    beforeEach(async () => {
      await projectRoutes(fastify)
      handler = (fastify.get as any).mock.calls[0][1]
    })

    it('should return projects for authenticated user', async () => {
      const mockSession = { user: { id: 'user123' } }
      const mockProjects = [
        { id: 'proj1', name: 'Project 1' },
        { id: 'proj2', name: 'Project 2' },
      ]

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.findByUserId.mockResolvedValue(mockProjects)

      await handler(mockRequest, mockReply)

      expect(auth.api.getSession).toHaveBeenCalledWith({ headers: mockRequest.headers })
      expect(mockProjectRepository.findByUserId).toHaveBeenCalledWith('user123')
      expect(mockReply.send).toHaveBeenCalledWith(mockProjects)
    })

    it('should return 401 when not authenticated', async () => {
      ;(auth.api.getSession as any).mockResolvedValue(null)

      await handler(mockRequest, mockReply)

      expect(mockReply.status).toHaveBeenCalledWith(401)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized' })
    })

    it('should handle errors gracefully', async () => {
      const mockSession = { user: { id: 'user123' } }
      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.findByUserId.mockRejectedValue(new Error('Database error'))

      await handler(mockRequest, mockReply)

      expect(fastify.log.error).toHaveBeenCalledWith('Get projects error:', expect.any(Error))
      expect(mockReply.status).toHaveBeenCalledWith(500)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Failed to get projects' })
    })
  })

  describe('POST /api/projects', () => {
    let handler: Function

    beforeEach(async () => {
      await projectRoutes(fastify)
      handler = (fastify.post as any).mock.calls[0][1]
    })

    it('should create a project successfully', async () => {
      const mockSession = { user: { id: 'user123' } }
      const mockBody = {
        name: 'New Project',
        description: 'Project description',
        accessType: 'PERSONAL',
      }
      const mockCreatedProject = {
        id: 'proj1',
        ...mockBody,
        userId: 'user123',
      }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.create.mockResolvedValue(mockCreatedProject)
      mockRequest.body = mockBody

      await handler(mockRequest, mockReply)

      expect(mockProjectRepository.create).toHaveBeenCalledWith({
        ...mockBody,
        userId: 'user123',
      })
      expect(mockReply.status).toHaveBeenCalledWith(201)
      expect(mockReply.send).toHaveBeenCalledWith(mockCreatedProject)
    })

    it('should return 400 for invalid input', async () => {
      const mockSession = { user: { id: 'user123' } }
      mockRequest.body = { description: 'No name provided' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)

      await handler(mockRequest, mockReply)

      expect(mockReply.status).toHaveBeenCalledWith(400)
      expect(mockReply.send).toHaveBeenCalledWith({
        error: 'Validation failed',
        details: expect.any(Object),
      })
    })
  })

  describe('GET /api/projects/:id', () => {
    let handler: Function

    beforeEach(async () => {
      await projectRoutes(fastify)
      handler = (fastify.get as any).mock.calls[1][1]
    })

    it('should return a specific project', async () => {
      const mockSession = { user: { id: 'user123' } }
      const mockProject = { id: 'proj1', name: 'Project 1' }
      mockRequest.params = { id: 'proj1' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.findByIdAndUserId.mockResolvedValue(mockProject)

      await handler(mockRequest, mockReply)

      expect(mockProjectRepository.findByIdAndUserId).toHaveBeenCalledWith('proj1', 'user123')
      expect(mockReply.send).toHaveBeenCalledWith(mockProject)
    })

    it('should return 404 when project not found', async () => {
      const mockSession = { user: { id: 'user123' } }
      mockRequest.params = { id: 'proj1' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.findByIdAndUserId.mockResolvedValue(null)

      await handler(mockRequest, mockReply)

      expect(mockReply.status).toHaveBeenCalledWith(404)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Project not found' })
    })
  })

  describe('PUT /api/projects/:id', () => {
    let handler: Function

    beforeEach(async () => {
      await projectRoutes(fastify)
      handler = (fastify.put as any).mock.calls[0][1]
    })

    it('should update a project successfully', async () => {
      const mockSession = { user: { id: 'user123' } }
      const mockUpdatedProject = {
        id: 'proj1',
        name: 'Updated Name',
        description: 'Updated description',
      }
      mockRequest.params = { id: 'proj1' }
      mockRequest.body = {
        name: 'Updated Name',
        description: 'Updated description',
      }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.updateByIdAndUserId.mockResolvedValue(mockUpdatedProject)

      await handler(mockRequest, mockReply)

      expect(mockProjectRepository.updateByIdAndUserId).toHaveBeenCalledWith(
        'proj1',
        'user123',
        mockRequest.body
      )
      expect(mockReply.send).toHaveBeenCalledWith(mockUpdatedProject)
    })

    it('should return 404 when project not found', async () => {
      const mockSession = { user: { id: 'user123' } }
      mockRequest.params = { id: 'proj1' }
      mockRequest.body = { name: 'Updated Name' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.updateByIdAndUserId.mockResolvedValue(null)

      await handler(mockRequest, mockReply)

      expect(mockReply.status).toHaveBeenCalledWith(404)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Project not found' })
    })
  })

  describe('DELETE /api/projects/:id', () => {
    let handler: Function

    beforeEach(async () => {
      await projectRoutes(fastify)
      handler = (fastify.delete as any).mock.calls[0][1]
    })

    it('should delete a project successfully', async () => {
      const mockSession = { user: { id: 'user123' } }
      mockRequest.params = { id: 'proj1' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.deleteByIdAndUserId.mockResolvedValue(true)

      await handler(mockRequest, mockReply)

      expect(mockProjectRepository.deleteByIdAndUserId).toHaveBeenCalledWith('proj1', 'user123')
      expect(mockReply.status).toHaveBeenCalledWith(204)
      expect(mockReply.send).toHaveBeenCalled()
    })

    it('should return 404 when project not found', async () => {
      const mockSession = { user: { id: 'user123' } }
      mockRequest.params = { id: 'proj1' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.deleteByIdAndUserId.mockResolvedValue(false)

      await handler(mockRequest, mockReply)

      expect(mockReply.status).toHaveBeenCalledWith(404)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Project not found' })
    })
  })

  describe('GET /api/projects/:id/documents', () => {
    let handler: Function

    beforeEach(async () => {
      await projectRoutes(fastify)
      // Find the handler for the documents endpoint (should be the 3rd GET route)
      handler = (fastify.get as any).mock.calls[2][1]
    })

    it('should return documents for a project', async () => {
      const mockSession = { user: { id: 'user123' } }
      const mockProject = { id: 'proj1', name: 'Project 1', userId: 'user123' }
      const mockDocuments = [
        {
          id: 'doc1',
          fileName: 'document1.pdf',
          fileType: 'application/pdf',
          status: 'COMPLETED',
          workspaceId: 'workspace1',
          uploadedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'doc2',
          fileName: 'document2.txt',
          fileType: 'text/plain',
          status: 'COMPLETED',
          workspaceId: 'workspace1',
          uploadedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]
      mockRequest.params = { id: 'proj1' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.findByIdAndUserId.mockResolvedValue(mockProject)
      mockDocumentRepository.findByProject.mockResolvedValue({
        documents: mockDocuments,
        total: 2,
        limit: 50,
        offset: 0,
      })

      await handler(mockRequest, mockReply)

      expect(mockProjectRepository.findByIdAndUserId).toHaveBeenCalledWith('proj1', 'user123')
      expect(mockDocumentRepository.findByProject).toHaveBeenCalledWith('proj1')
      expect(mockReply.send).toHaveBeenCalledWith(mockDocuments)
    })

    it('should return 401 when not authenticated', async () => {
      ;(auth.api.getSession as any).mockResolvedValue(null)
      mockRequest.params = { id: 'proj1' }

      await handler(mockRequest, mockReply)

      expect(mockReply.status).toHaveBeenCalledWith(401)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized' })
    })

    it('should return 404 when project not found', async () => {
      const mockSession = { user: { id: 'user123' } }
      mockRequest.params = { id: 'proj1' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.findByIdAndUserId.mockResolvedValue(null)

      await handler(mockRequest, mockReply)

      expect(mockProjectRepository.findByIdAndUserId).toHaveBeenCalledWith('proj1', 'user123')
      expect(mockDocumentRepository.findByProject).not.toHaveBeenCalled()
      expect(mockReply.status).toHaveBeenCalledWith(404)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Project not found' })
    })

    it('should return empty array when no documents are associated', async () => {
      const mockSession = { user: { id: 'user123' } }
      const mockProject = { id: 'proj1', name: 'Project 1', userId: 'user123' }
      mockRequest.params = { id: 'proj1' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.findByIdAndUserId.mockResolvedValue(mockProject)
      mockDocumentRepository.findByProject.mockResolvedValue({
        documents: [],
        total: 0,
        limit: 50,
        offset: 0,
      })

      await handler(mockRequest, mockReply)

      expect(mockReply.send).toHaveBeenCalledWith([])
    })

    it('should handle errors gracefully', async () => {
      const mockSession = { user: { id: 'user123' } }
      const mockProject = { id: 'proj1', name: 'Project 1', userId: 'user123' }
      mockRequest.params = { id: 'proj1' }

      ;(auth.api.getSession as any).mockResolvedValue(mockSession)
      mockProjectRepository.findByIdAndUserId.mockResolvedValue(mockProject)
      mockDocumentRepository.findByProject.mockRejectedValue(new Error('Database error'))

      await handler(mockRequest, mockReply)

      expect(fastify.log.error).toHaveBeenCalledWith('Get project documents error:', expect.any(Error))
      expect(mockReply.status).toHaveBeenCalledWith(500)
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Failed to get project documents' })
    })
  })
})