import type { FastifyInstance } from 'fastify';
import documentRoutes from './document.routes';
import { documentRepository } from '../repositories/document.repository';
import { createAuditLog } from '../lib/audit-log';
import { auth } from '../lib/auth';

// Mock dependencies
jest.mock('../repositories/document.repository');
jest.mock('../lib/audit-log');
jest.mock('../lib/auth', () => ({
  auth: {
    api: {
      getSession: jest.fn()
    }
  }
}));
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  promises: {
    mkdir: jest.fn().mockResolvedValue(undefined),
    writeFile: jest.fn().mockResolvedValue(undefined),
    unlink: jest.fn().mockResolvedValue(undefined)
  }
}));
jest.mock('@fastify/multipart');

describe('Document Routes', () => {
  let fastify: FastifyInstance;

  beforeEach(async () => {
    jest.clearAllMocks();
    
    fastify = {
      register: jest.fn().mockResolvedValue(undefined),
      get: jest.fn(),
      put: jest.fn(),
      post: jest.fn(),
      delete: jest.fn(),
      log: {
        error: jest.fn(),
        warn: jest.fn(),
        info: jest.fn()
      },
      prisma: {
        projectMember: {
          findUnique: jest.fn()
        },
        knowledgeBaseDocument: {
          findMany: jest.fn(),
          create: jest.fn(),
          findUnique: jest.fn(),
          delete: jest.fn(),
          count: jest.fn()
        },
        user: {
          findUnique: jest.fn()
        },
        workspaceMembers: {
          findFirst: jest.fn()
        },
        documentChunk: {
          deleteMany: jest.fn()
        },
        projectDocuments: {
          deleteMany: jest.fn()
        },
        $transaction: jest.fn()
      },
      decorate: jest.fn(),
      decorateRequest: jest.fn(),
      addHook: jest.fn()
    } as unknown as FastifyInstance;

    // Call the routes registration
    await documentRoutes(fastify);
  });

  describe('POST /documents/upload', () => {
    it('should be registered', () => {
      expect(fastify.post).toHaveBeenCalledWith(
        '/documents/upload',
        expect.any(Function)
      );
    });

    it('should upload files successfully', async () => {
      const mockUser = {
        id: 'user123',
        workspaceMembers: [{
          workspace: { id: 'workspace123' }
        }]
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (fastify.prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      (fastify.prisma.knowledgeBaseDocument.create as jest.Mock).mockResolvedValue({
        id: 'doc123',
        workspaceId: 'workspace123',
        fileName: 'test.pdf',
        fileType: 'application/pdf',
        status: 'PENDING'
      });

      const handler = (fastify.post as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/documents/upload'
      )[1];

      const mockFile = {
        filename: 'test.pdf',
        mimetype: 'application/pdf',
        toBuffer: jest.fn().mockResolvedValue(Buffer.from('test content'))
      };

      const mockRequest = {
        headers: {},
        files: jest.fn().mockReturnValue({
          async *[Symbol.asyncIterator]() {
            yield mockFile;
          }
        })
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      await handler(mockRequest, mockReply);

      expect(fastify.prisma.knowledgeBaseDocument.create).toHaveBeenCalled();
      expect(createAuditLog).toHaveBeenCalledWith({
        userId: 'user123',
        resourceType: 'DOCUMENT',
        resourceId: 'doc123',
        action: 'UPLOAD_DOCUMENT',
        metadata: expect.objectContaining({
          fileName: 'test.pdf',
          fileType: 'application/pdf'
        })
      });
      expect(mockReply.send).toHaveBeenCalledWith({
        documents: [expect.objectContaining({
          id: 'doc123',
          fileName: 'test.pdf'
        })]
      });
    });

    it('should reject unauthorized requests', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue(null);

      const handler = (fastify.post as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/documents/upload'
      )[1];

      const mockRequest = {
        headers: {}
      };

      const mockReply = {
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      await handler(mockRequest, mockReply);

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized' });
    });
  });

  describe('GET /projects/:id/available-documents', () => {
    it('should be registered', () => {
      expect(fastify.get).toHaveBeenCalledWith(
        '/projects/:id/available-documents',
        expect.any(Function)
      );
    });

    it('should return available documents with association status for admin', async () => {
      const mockDocs = [
        { document: { id: 'doc1', fileName: 'test1.pdf' }, isAssociated: true },
        { document: { id: 'doc2', fileName: 'test2.md' }, isAssociated: false }
      ];

      (documentRepository.findAvailableForProject as jest.Mock).mockResolvedValue(mockDocs);
      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (fastify.prisma.projectMember.findUnique as jest.Mock).mockResolvedValue({
        userId: 'user123',
        projectId: 'project123',
        role: 'ADMIN',
        project: { workspaceId: 'workspace123' }
      });

      const handler = (fastify.get as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/projects/:id/available-documents'
      )[1];

      const mockRequest = {
        params: { id: 'project123' },
        headers: {},
        query: {}
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      await handler(mockRequest, mockReply);

      expect(documentRepository.findAvailableForProject).toHaveBeenCalledWith(
        'project123',
        'workspace123',
        50,
        0,
        undefined
      );
      expect(mockReply.send).toHaveBeenCalledWith(mockDocs);
    });

    it('should reject non-admin users', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (fastify.prisma.projectMember.findUnique as jest.Mock).mockResolvedValue({
        userId: 'user123',
        projectId: 'project123',
        role: 'MEMBER'
      });

      const handler = (fastify.get as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/projects/:id/available-documents'
      )[1];

      const mockRequest = {
        params: { id: 'project123' },
        headers: {}
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      await handler(mockRequest, mockReply);

      expect(mockReply.code).toHaveBeenCalledWith(403);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Admin access required' });
    });
  });

  describe('PUT /projects/:id/documents', () => {
    it('should be registered', () => {
      expect(fastify.put).toHaveBeenCalledWith(
        '/projects/:id/documents',
        expect.any(Function)
      );
    });

    it('should update document associations for admin', async () => {
      const currentDocs = { documents: [{ id: 'doc1' }] };
      const updatedDocs = [{ id: 'doc2' }, { id: 'doc3' }];

      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (documentRepository.findByProject as jest.Mock).mockResolvedValue(currentDocs);
      (documentRepository.updateProjectDocuments as jest.Mock).mockResolvedValue(updatedDocs);
      (createAuditLog as jest.Mock).mockResolvedValue(undefined);
      (fastify.prisma.projectMember.findUnique as jest.Mock).mockResolvedValue({
        userId: 'user123',
        projectId: 'project123',
        role: 'ADMIN',
        project: { name: 'Test Project', workspaceId: 'workspace123' }
      });
      (fastify.prisma.knowledgeBaseDocument.findMany as jest.Mock).mockResolvedValue([
        { id: 'doc2', workspaceId: 'workspace123' },
        { id: 'doc3', workspaceId: 'workspace123' }
      ]);

      const handler = (fastify.put as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/projects/:id/documents'
      )[1];

      const mockRequest = {
        params: { id: 'project123' },
        body: { documentIds: ['doc2', 'doc3'] },
        headers: {}
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      await handler(mockRequest, mockReply);

      expect(documentRepository.updateProjectDocuments).toHaveBeenCalledWith(
        'project123',
        ['doc2', 'doc3']
      );
      expect(createAuditLog).toHaveBeenCalledWith({
        userId: 'user123',
        resourceType: 'PROJECT',
        resourceId: 'project123',
        action: 'UPDATE_PROJECT_DOCUMENTS',
        metadata: expect.objectContaining({
          projectName: 'Test Project',
          previousDocumentIds: ['doc1'],
          newDocumentIds: ['doc2', 'doc3']
        })
      });
      expect(mockReply.send).toHaveBeenCalledWith({ documents: updatedDocs });
    });
  });

  describe('GET /documents', () => {
    it('should be registered', () => {
      expect(fastify.get).toHaveBeenCalledWith(
        '/documents',
        expect.any(Function)
      );
    });

    it('should return workspace documents', async () => {
      const mockUser = {
        id: 'user123',
        workspaceMembers: [{
          workspace: { id: 'workspace123' }
        }]
      };

      const mockDocuments = [
        { id: 'doc1', fileName: 'test1.pdf', status: 'COMPLETED' },
        { id: 'doc2', fileName: 'test2.txt', status: 'PENDING' }
      ];

      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (fastify.prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      (fastify.prisma.$transaction as jest.Mock).mockResolvedValue([mockDocuments, 2]);

      const handler = (fastify.get as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/documents'
      )[1];

      const mockRequest = {
        headers: {},
        query: { limit: '50', offset: '0' }
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      await handler(mockRequest, mockReply);

      expect(mockReply.send).toHaveBeenCalledWith({
        documents: mockDocuments,
        total: 2,
        limit: 50,
        offset: 0
      });
    });
  });

  describe('DELETE /documents/:id', () => {
    it('should be registered', () => {
      expect(fastify.delete).toHaveBeenCalledWith(
        '/documents/:id',
        expect.any(Function)
      );
    });

    it('should delete document successfully', async () => {
      const mockDocument = {
        id: 'doc123',
        workspaceId: 'workspace123',
        fileName: 'test.pdf',
        fileType: 'application/pdf',
        fileSize: 1024,
        storedFileName: 'stored-file.pdf'
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (fastify.prisma.knowledgeBaseDocument.findUnique as jest.Mock).mockResolvedValue(mockDocument);
      (fastify.prisma.workspaceMembers.findFirst as jest.Mock).mockResolvedValue({ userId: 'user123', workspaceId: 'workspace123' });
      (fastify.prisma.$transaction as jest.Mock).mockImplementation(async (callback) => {
        return callback({
          documentChunk: { deleteMany: jest.fn().mockResolvedValue({}) },
          projectDocuments: { deleteMany: jest.fn().mockResolvedValue({}) },
          knowledgeBaseDocument: { delete: jest.fn().mockResolvedValue({}) }
        });
      });
      (createAuditLog as jest.Mock).mockResolvedValue(undefined);

      const handler = (fastify.delete as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/documents/:id'
      )[1];

      const mockRequest = {
        headers: {},
        params: { id: 'doc123' }
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      const fs = require('fs');
      (fs.promises.unlink as jest.Mock).mockClear();
      (fs.promises.unlink as jest.Mock).mockResolvedValue(undefined);

      await handler(mockRequest, mockReply);

      expect(fastify.prisma.$transaction).toHaveBeenCalled();
      expect(fs.promises.unlink).toHaveBeenCalledWith(expect.stringContaining('stored-file.pdf'));
      expect(createAuditLog).toHaveBeenCalledWith({
        userId: 'user123',
        resourceType: 'DOCUMENT',
        resourceId: 'doc123',
        action: 'DELETE_DOCUMENT',
        metadata: {
          fileName: 'test.pdf',
          fileType: 'application/pdf',
          fileSize: 1024,
          workspaceId: 'workspace123'
        }
      });
      expect(mockReply.code).toHaveBeenCalledWith(204);
      expect(mockReply.send).toHaveBeenCalled();
    });

    it('should return 404 if document not found', async () => {
      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (fastify.prisma.knowledgeBaseDocument.findUnique as jest.Mock).mockResolvedValue(null);

      const handler = (fastify.delete as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/documents/:id'
      )[1];

      const mockRequest = {
        headers: {},
        params: { id: 'doc123' }
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      await handler(mockRequest, mockReply);

      expect(mockReply.code).toHaveBeenCalledWith(404);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Document not found' });
    });

    it('should return 403 if user does not have access', async () => {
      const mockDocument = {
        id: 'doc123',
        workspaceId: 'workspace123'
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (fastify.prisma.knowledgeBaseDocument.findUnique as jest.Mock).mockResolvedValue(mockDocument);
      (fastify.prisma.workspaceMembers.findFirst as jest.Mock).mockResolvedValue(null); // User not a member

      const handler = (fastify.delete as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/documents/:id'
      )[1];

      const mockRequest = {
        headers: {},
        params: { id: 'doc123' }
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      await handler(mockRequest, mockReply);

      expect(mockReply.code).toHaveBeenCalledWith(403);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Access denied' });
    });

    it('should handle file deletion errors gracefully', async () => {
      const mockDocument = {
        id: 'doc123',
        workspaceId: 'workspace123',
        fileName: 'test.pdf',
        storedFileName: 'stored-file.pdf'
      };

      (auth.api.getSession as jest.Mock).mockResolvedValue({ user: { id: 'user123' } });
      (fastify.prisma.knowledgeBaseDocument.findUnique as jest.Mock).mockResolvedValue(mockDocument);
      (fastify.prisma.workspaceMembers.findFirst as jest.Mock).mockResolvedValue({ userId: 'user123', workspaceId: 'workspace123' });
      (fastify.prisma.$transaction as jest.Mock).mockImplementation(async (callback) => {
        return callback({
          documentChunk: { deleteMany: jest.fn().mockResolvedValue({}) },
          projectDocuments: { deleteMany: jest.fn().mockResolvedValue({}) },
          knowledgeBaseDocument: { delete: jest.fn().mockResolvedValue({}) }
        });
      });

      const handler = (fastify.delete as jest.Mock).mock.calls.find(
        ([path]: [string]) => path === '/documents/:id'
      )[1];

      const mockRequest = {
        headers: {},
        params: { id: 'doc123' }
      };

      const mockReply = {
        code: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        send: jest.fn()
      };

      const fs = require('fs');
      (fs.promises.unlink as jest.Mock).mockRejectedValue(new Error('File not found'));

      await handler(mockRequest, mockReply);

      // Should still return 204 even if file deletion fails
      expect(mockReply.code).toHaveBeenCalledWith(204);
      expect(fastify.log.warn).toHaveBeenCalledWith(
        expect.objectContaining({ documentId: 'doc123' }),
        'Failed to delete physical file'
      );
    });
  });
});