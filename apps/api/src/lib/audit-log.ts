import { FastifyRequest } from 'fastify'
import pino from 'pino'

const auditLogger = pino({
  name: 'audit',
  level: 'info',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
      messageFormat: '[AUDIT] {msg}'
    }
  }
})

export interface AuditLogEntry {
  action: string
  userId?: string
  resourceType: string
  resourceId?: string
  metadata?: Record<string, any>
  ip?: string
  userAgent?: string
  timestamp: Date
}

export async function logAuditEvent(
  request: FastifyRequest & { session?: any },
  action: string,
  resourceType: string,
  resourceId?: string,
  metadata?: Record<string, any>
): Promise<void>

export async function logAuditEvent(
  data: {
    action: string
    userId: string
    resourceType: string
    resourceId?: string
    metadata?: Record<string, any>
  }
): Promise<void>

export async function logAuditEvent(
  requestOrData: FastifyRequest & { session?: any } | {
    action: string
    userId: string
    resourceType: string
    resourceId?: string
    metadata?: Record<string, any>
  },
  action?: string,
  resourceType?: string,
  resourceId?: string,
  metadata?: Record<string, any>
) {
  let entry: AuditLogEntry

  if ('headers' in requestOrData) {
    // Old signature with request
    const request = requestOrData
    const session = request.session
    const userId = session?.user?.id

    entry = {
      action: action!,
      userId,
      resourceType: resourceType!,
      resourceId,
      metadata,
      ip: request.headers['x-forwarded-for'] as string || request.ip,
      userAgent: request.headers['user-agent'],
      timestamp: new Date()
    }
  } else {
    // New signature with direct data
    entry = {
      ...requestOrData,
      timestamp: new Date()
    }
  }

  auditLogger.info(entry, `${entry.action} ${entry.resourceType}`)
  
  // TODO: In production, this should also write to a persistent audit log database table
  // For now, we're just logging to the console/log files
}

export const AuditActions = {
  PROJECT_CREATED: 'PROJECT_CREATED',
  PROJECT_UPDATED: 'PROJECT_UPDATED',
  PROJECT_DELETED: 'PROJECT_DELETED',
  PROJECT_ACCESSED: 'PROJECT_ACCESSED',
  MEMBER_ADDED: 'MEMBER_ADDED',
  MEMBER_ROLE_UPDATED: 'MEMBER_ROLE_UPDATED',
  MEMBER_REMOVED: 'MEMBER_REMOVED',
  UPDATE_PROJECT_DOCUMENTS: 'UPDATE_PROJECT_DOCUMENTS',
} as const

export const createAuditLog = logAuditEvent