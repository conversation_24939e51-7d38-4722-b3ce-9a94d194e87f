import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { emailOTP } from "better-auth/plugins";
import { Resend } from "resend";
import prisma from "./prisma";
import { env, getAllowedOrigins } from "../config/env";

// Initialize Resend only if API key is provided
const resend = env.RESEND_API_KEY ? new Resend(env.RESEND_API_KEY) : null;

export const auth = betterAuth({
  database: prismaAdapter(prisma, { 
    provider: "postgresql" 
  }),
  emailAndPassword: { 
    enabled: true 
  },
  secret: env.AUTH_SECRET,
  trustedOrigins: getAllowedOrigins(),
  baseURL: env.AUTH_URL,
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60 // 5 minutes
    },
    fetchUser: async (userId) => {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          emailVerified: true,
          name: true,
          image: true,
          phone: true,
          country: true,
          onboardingCompleted: true,
          createdAt: true,
          updatedAt: true
        }
      })
      return user
    }
  },
  advanced: {
    useSecureCookies: env.NODE_ENV === 'production',
    defaultCookieAttributes: {
      httpOnly: true,
      secure: env.NODE_ENV === 'production',
      sameSite: "lax",
      path: "/"
    }
  },
  plugins: [
    emailOTP({
      async sendVerificationOTP({ email, otp, type }) {
        try {
          const subject = type === "sign-in" 
            ? "Your login code" 
            : type === "email-verification"
            ? "Verify your email"
            : "Reset your password";
          
          if (!resend) {
            throw new Error("Email service not configured. Please set RESEND_API_KEY.");
          }
          
          await resend.emails.send({
            from: env.RESEND_FROM_EMAIL,
            to: email,
            subject,
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>${subject}</h2>
                <p>Use this code to continue:</p>
                <div style="background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 32px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
                  ${otp}
                </div>
                <p>This code will expire in 5 minutes.</p>
                <p>If you didn't request this code, please ignore this email.</p>
              </div>
            `
          });
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          console.error("Failed to send OTP email:", {
            error: errorMessage,
            details: error
          });
          throw new Error(`Failed to send verification email: ${errorMessage}`);
        }
      },
      expiresIn: 300, // 5 minutes
      otpLength: 6
    })
  ]
});