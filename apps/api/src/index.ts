import Fastify from 'fastify'
import cors from '@fastify/cors'
import helmet from '@fastify/helmet'
import rateLimit from '@fastify/rate-limit'
import { auth } from './lib/auth'
import { UserRepository } from './repositories/user.repository'
import prisma from './lib/prisma'
import { z } from 'zod'
import { agentRoutes } from './routes/agent.routes'
import { projectRoutes } from './routes/project.routes'
import { memberRoutes } from './routes/member.routes'
import { userRoutes } from './routes/user.routes'
import documentRoutes from './routes/document.routes'
import searchRoutes from './routes/search.routes'
import deploymentRoutes from './routes/deployment.routes'
import publicApiRoutes from './routes/public-api.routes'
import { env, validateEnv, getAllowedOrigins } from './config/env'

const fastify = Fastify({
  logger: {
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true
      }
    }
  },
  bodyLimit: 1048576, // 1MB limit for request body
  maxParamLength: 5000 // Limit URL parameter length
})

// Validate environment variables on startup
validateEnv();

// Decorate fastify with prisma
fastify.decorate('prisma', prisma);

// Register plugins
fastify.register(cors, {
  origin: (origin, cb) => {
    const allowedOrigins = getAllowedOrigins();
    
    if (!origin || allowedOrigins.includes(origin)) {
      cb(null, true)
    } else {
      cb(new Error('Not allowed by CORS'))
    }
  },
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With", "Cookie"],
  credentials: true,
  maxAge: 86400
})

fastify.register(helmet)

// Rate limiting for non-auth routes only
// Better-auth handles its own rate limiting for auth endpoints
fastify.register(rateLimit, {
  max: 100,
  timeWindow: '1 minute'
})

// Better Auth route handler
fastify.all("/api/auth/*", async (request, reply) => {
  try {
    const url = new URL(request.url, `http://${request.headers.host}`);
    
    // Create Web Request
    const webRequest = new Request(url, {
      method: request.method,
      headers: new Headers(request.headers as HeadersInit),
      body: request.body ? JSON.stringify(request.body) : undefined,
    });
    
    // Call better-auth handler
    const response = await auth.handler(webRequest);
    
    // Set status code
    reply.code(response.status);
    
    // Copy headers
    response.headers.forEach((value, key) => {
      reply.header(key, value);
    });
    
    // Send body
    const body = await response.text();
    return reply.send(body);
    
  } catch (error) {
    fastify.log.error("Auth handler error:", error);
    return reply.code(500).send({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

// Health check route  
fastify.get('/api/health', async () => {
  return { status: 'ok', timestamp: new Date().toISOString() }
})

// API routes
fastify.get('/api/v1/status', async () => {
  return { 
    message: 'sflow API is running',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  }
})

// Profile update schema
const profileUpdateSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, "Invalid phone number format"),
  country: z.string().length(2, "Country must be ISO 2-letter code").regex(/^[A-Z]{2}$/, "Country must be uppercase ISO code")
})

// Register agent routes
fastify.register(agentRoutes, { prefix: '/api' })

// Register project routes
fastify.register(projectRoutes, { prefix: '/api' })

// Register member routes
fastify.register(memberRoutes, { prefix: '/api', prisma })

// Register user routes
fastify.register(userRoutes, { prefix: '/api' })

// Register document routes
fastify.register(documentRoutes, { prefix: '/api' })

// Register search routes
fastify.register(searchRoutes, { prefix: '/api' })

// Register deployment routes
fastify.register(deploymentRoutes, { prefix: '/api' })

// Register public API routes (no prefix - they have their own /api/v1 prefix)
fastify.register(publicApiRoutes)

// User profile GET endpoint
fastify.get('/api/v1/user/profile', async (request, reply) => {
  try {
    // Verify session with better-auth using cookies
    const session = await auth.api.getSession({ headers: request.headers })
    if (!session?.user) {
      return reply.status(401).send({ error: 'Unauthorized' })
    }

    // Get user profile from database
    const userRepository = new UserRepository(prisma)
    const user = await userRepository.findById(session.user.id)
    
    if (!user) {
      return reply.status(404).send({ error: 'User not found' })
    }

    return reply.send({
      id: user.id,
      email: user.email,
      name: user.name,
      phone: user.phone,
      country: user.country,
      image: user.image,
      onboardingCompleted: user.onboardingCompleted
    })
  } catch (err) {
    fastify.log.error('Profile fetch error:', err)
    return reply.status(500).send({ error: 'Failed to fetch profile' })
  }
})

// User profile update endpoint
fastify.post('/api/v1/user/profile', async (request, reply) => {
  try {
    // Verify session with better-auth using cookies
    const session = await auth.api.getSession({ headers: request.headers })
    if (!session?.user) {
      return reply.status(401).send({ error: 'Unauthorized' })
    }

    // Validate request body
    const validation = profileUpdateSchema.safeParse(request.body)
    if (!validation.success) {
      return reply.status(400).send({ 
        error: 'Validation failed', 
        details: validation.error.flatten() 
      })
    }

    // Update user profile
    const userRepository = new UserRepository(prisma)
    const updatedUser = await userRepository.updateProfile(
      session.user.id,
      validation.data
    )

    return reply.send({
      id: updatedUser.id,
      email: updatedUser.email,
      name: updatedUser.name,
      phone: updatedUser.phone,
      country: updatedUser.country
    })
  } catch (err) {
    fastify.log.error('Profile update error:', err)
    return reply.status(500).send({ error: 'Failed to update profile' })
  }
})

// Start server
const start = async () => {
  try {
    const port = parseInt(env.PORT)
    const host = env.HOST
    
    await fastify.listen({ port, host })
    fastify.log.info(`Server listening on ${host}:${port}`)
  } catch (err) {
    fastify.log.error(err)
    process.exit(1)
  }
}

start()