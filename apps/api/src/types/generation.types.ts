import { z } from 'zod';

export const GenerationBlockPropsSchema = z.object({
  provider: z.string().default('openrouter'),
  model: z.string().default('openai/gpt-3.5-turbo'),
  temperature: z.number().min(0).max(1).default(0.7),
  prompt: z.string().optional(),
  output: z.string().optional(),
  isGenerating: z.boolean().optional(),
  error: z.string().optional(),
});

export type GenerationBlockProps = z.infer<typeof GenerationBlockPropsSchema>;

export const GenerationRequestSchema = z.object({
  blockId: z.string(),
  projectId: z.string(),
  agentId: z.string(),
  config: GenerationBlockPropsSchema,
  prompt: z.string(),
  context: z.record(z.any()).optional(), // For input variables
});

export type GenerationRequest = z.infer<typeof GenerationRequestSchema>;

export const KnowledgeBaseReferenceSchema = z.object({
  documentName: z.string(),
  documentId: z.string().optional(),
});

export type KnowledgeBaseReference = z.infer<typeof KnowledgeBaseReferenceSchema>;

export const GenerationResponseSchema = z.object({
  content: z.string(),
  usage: z.object({
    prompt_tokens: z.number(),
    completion_tokens: z.number(),
    total_tokens: z.number(),
  }).optional(),
  model: z.string(),
});

export type GenerationResponse = z.infer<typeof GenerationResponseSchema>;

// OpenRouter specific types
export const OpenRouterRequestSchema = z.object({
  model: z.string(),
  messages: z.array(z.object({
    role: z.enum(['system', 'user', 'assistant']),
    content: z.string(),
  })),
  temperature: z.number().optional(),
  max_tokens: z.number().optional(),
  stream: z.boolean().optional(),
});

export type OpenRouterRequest = z.infer<typeof OpenRouterRequestSchema>;

export const OpenRouterResponseSchema = z.object({
  id: z.string(),
  choices: z.array(z.object({
    index: z.number(),
    message: z.object({
      role: z.string(),
      content: z.string(),
    }),
    finish_reason: z.string(),
  })),
  model: z.string(),
  usage: z.object({
    prompt_tokens: z.number(),
    completion_tokens: z.number(),
    total_tokens: z.number(),
  }).optional(),
});

export type OpenRouterResponse = z.infer<typeof OpenRouterResponseSchema>;

// LLM Provider Interface
export interface LLMProvider {
  generateCompletion(
    prompt: string,
    model: string,
    temperature: number,
    systemPrompt?: string
  ): Promise<GenerationResponse>;
}