generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                  String             @id @default(cuid())
  name                String?
  email               String             @unique
  emailVerified       Boolean            @default(false)
  image               String?
  phone               String?
  country             String?
  onboardingCompleted Boolean            @default(false)
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt
  accounts            Account[]
  projects            Project[]
  projectMembers      ProjectMember[]
  sessions            Session[]
  workspaceMembers    WorkspaceMembers[]

  @@map("user")
}

model Session {
  id        String   @id @default(cuid())
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                    String    @id @default(cuid())
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Verification {
  id         String    @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime? @default(now())
  updatedAt  DateTime? @updatedAt

  @@map("verification")
}

model Workspace {
  id          String             @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  projects    Project[]
  members     WorkspaceMembers[]

  @@map("workspace")
}

model WorkspaceMembers {
  userId      String
  workspaceId String
  role        String    @default("MEMBER")
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@id([userId, workspaceId])
  @@map("workspace_members")
}

model Project {
  id          String             @id @default(cuid())
  name        String
  description String?
  accessType  AccessType         @default(PERSONAL)
  userId      String
  workspaceId String
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  agents      Agent[]
  user        User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  workspace   Workspace          @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  documents   ProjectDocuments[]
  members     ProjectMember[]

  @@map("project")
}

model Agent {
  id           String       @id @default(cuid())
  title        String
  workflowJson Json
  triggerType  String?
  projectId    String
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  project      Project      @relation(fields: [projectId], references: [id], onDelete: Cascade)
  deployments  Deployment[]

  @@map("agent")
}

model Deployment {
  id             String           @id @default(cuid())
  agentId        String
  deploymentUrl  String           @unique
  apiKey         String           @unique
  status         DeploymentStatus @default(ACTIVE)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  agent          Agent            @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("deployment")
}

model ProjectMember {
  userId    String
  projectId String
  role      MemberRole @default(MEMBER)
  invitedAt DateTime   @default(now())
  joinedAt  DateTime?
  project   Project    @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, projectId])
  @@map("project_member")
}

model KnowledgeBaseDocument {
  id             String             @id @default(cuid())
  workspaceId    String
  fileName       String
  storedFileName String?
  fileType       String
  fileSize       Int?
  status         DocumentStatus     @default(PENDING)
  uploadedAt     DateTime           @default(now())
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  chunks         DocumentChunk[]
  projects       ProjectDocuments[]

  @@map("knowledge_base_document")
}

model ProjectDocuments {
  projectId  String
  documentId String
  document   KnowledgeBaseDocument @relation(fields: [documentId], references: [id], onDelete: Cascade)
  project    Project               @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@id([projectId, documentId])
  @@map("project_documents")
}

model DocumentChunk {
  id         String                @id @default(cuid())
  documentId String
  content    String
  embedding  Json?
  metadata   Json?
  createdAt  DateTime              @default(now())
  document   KnowledgeBaseDocument @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@map("document_chunk")
}

enum AccessType {
  PERSONAL
  COMPANY_WIDE

  @@map("access_type")
}

enum MemberRole {
  ADMIN
  MEMBER

  @@map("member_role")
}

enum DocumentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED

  @@map("document_status")
}

enum DeploymentStatus {
  ACTIVE
  INACTIVE

  @@map("deployment_status")
}
