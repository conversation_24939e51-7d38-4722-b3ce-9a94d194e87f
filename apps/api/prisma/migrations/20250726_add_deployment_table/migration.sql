-- CreateEnum
CREATE TYPE "deployment_status" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateTable
CREATE TABLE "deployment" (
    "id" TEXT NOT NULL,
    "agentId" TEXT NOT NULL,
    "deploymentUrl" TEXT NOT NULL,
    "apiKey" TEXT NOT NULL,
    "status" "deployment_status" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "deployment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "deployment_deploymentUrl_key" ON "deployment"("deploymentUrl");

-- CreateIndex
CREATE UNIQUE INDEX "deployment_apiKey_key" ON "deployment"("apiKey");

-- AddForeignKey
ALTER TABLE "deployment" ADD CONSTRAINT "deployment_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "agent"("id") ON DELETE CASCADE ON UPDATE CASCADE;