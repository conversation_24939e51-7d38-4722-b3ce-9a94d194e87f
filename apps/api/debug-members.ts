import prisma from './src/lib/prisma';

async function debugMembers() {
  try {
    // Get the most recent project
    const project = await prisma.project.findFirst({
      orderBy: { createdAt: 'desc' },
      include: {
        user: true,
        members: {
          include: {
            user: true
          }
        }
      }
    });
    
    if (!project) {
      console.log('No projects found');
      return;
    }
    
    console.log('\n=== PROJECT DEBUG INFO ===');
    console.log('Project ID:', project.id);
    console.log('Project Name:', project.name);
    console.log('Owner ID:', project.userId);
    console.log('Owner Email:', project.user.email);
    console.log('\n=== MEMBERS ===');
    console.log('Total Members:', project.members.length);
    
    project.members.forEach((member, index) => {
      console.log(`\nMember ${index + 1}:`);
      console.log('  User ID:', member.userId);
      console.log('  Email:', member.user.email);
      console.log('  Role:', member.role);
      console.log('  Joined At:', member.joinedAt);
      console.log('  Is Owner:', member.userId === project.userId ? 'YES' : 'NO');
    });
    
    // Check current user session
    console.log('\n=== CHECKING AUTH ===');
    console.log('To debug auth issues, check if the user ID in your session matches the member user ID');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugMembers();