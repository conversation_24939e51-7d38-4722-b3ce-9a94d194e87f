import { test, expect } from '@playwright/test'
import path from 'path'

test.describe('Knowledge Base Delete Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.goto('/')
    // Add authentication steps here based on your auth flow
  })

  test('should upload document, view in list, delete, and confirm gone', async ({ page }) => {
    // Navigate to Knowledge Base page
    await page.goto('/knowledge-base')
    
    // Wait for page to load
    await expect(page.getByRole('heading', { name: 'Knowledge Base' })).toBeVisible()
    
    // Upload a document
    const fileInput = await page.locator('input[type="file"]')
    const testFilePath = path.join(__dirname, 'fixtures', 'test-document.pdf')
    await fileInput.setInputFiles(testFilePath)
    
    // Wait for upload button and click
    const uploadButton = await page.getByRole('button', { name: /upload files/i })
    await uploadButton.click()
    
    // Wait for success message
    await expect(page.getByText(/successfully uploaded/i)).toBeVisible()
    
    // Verify document appears in list
    await expect(page.getByText('test-document.pdf')).toBeVisible()
    await expect(page.getByText('Completed')).toBeVisible()
    
    // Click delete button
    const deleteButton = await page.getByRole('button', { name: /delete document/i })
    await deleteButton.click()
    
    // Confirm deletion in dialog
    await expect(page.getByText('Delete Document')).toBeVisible()
    await expect(page.getByText(/Are you sure you want to delete "test-document.pdf"/)).toBeVisible()
    
    const confirmDeleteButton = await page.getByRole('button', { name: 'Delete' })
    await confirmDeleteButton.click()
    
    // Wait for deletion success message
    await expect(page.getByText(/document deleted successfully/i)).toBeVisible()
    
    // Verify document is gone from list
    await expect(page.getByText('test-document.pdf')).not.toBeVisible()
    
    // Verify empty state is shown if no other documents
    await expect(page.getByText(/no documents uploaded yet/i)).toBeVisible()
  })

  test('should handle delete with network error', async ({ page }) => {
    // Navigate to Knowledge Base page with existing document
    await page.goto('/knowledge-base')
    
    // Mock network error for delete request
    await page.route('**/api/documents/*', route => {
      if (route.request().method() === 'DELETE') {
        route.abort('failed')
      } else {
        route.continue()
      }
    })
    
    // Assuming a document is already in the list
    await expect(page.getByText('existing-document.pdf')).toBeVisible()
    
    // Click delete button
    const deleteButton = await page.getByRole('button', { name: /delete document/i })
    await deleteButton.click()
    
    // Confirm deletion
    const confirmDeleteButton = await page.getByRole('button', { name: 'Delete' })
    await confirmDeleteButton.click()
    
    // Verify error message
    await expect(page.getByText(/failed to delete document/i)).toBeVisible()
    
    // Verify document is still in list (optimistic update reverted)
    await expect(page.getByText('existing-document.pdf')).toBeVisible()
  })

  test('should show different status badges', async ({ page }) => {
    // Navigate to Knowledge Base page
    await page.goto('/knowledge-base')
    
    // Mock API to return documents with different statuses
    await page.route('**/api/documents', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          documents: [
            {
              id: '1',
              fileName: 'pending-doc.pdf',
              fileType: 'application/pdf',
              fileSize: 1024,
              status: 'PENDING',
              uploadedAt: new Date().toISOString()
            },
            {
              id: '2',
              fileName: 'processing-doc.pdf',
              fileType: 'application/pdf',
              fileSize: 2048,
              status: 'PROCESSING',
              uploadedAt: new Date().toISOString()
            },
            {
              id: '3',
              fileName: 'completed-doc.pdf',
              fileType: 'application/pdf',
              fileSize: 3072,
              status: 'COMPLETED',
              uploadedAt: new Date().toISOString()
            },
            {
              id: '4',
              fileName: 'failed-doc.pdf',
              fileType: 'application/pdf',
              fileSize: 4096,
              status: 'FAILED',
              uploadedAt: new Date().toISOString()
            }
          ],
          total: 4,
          limit: 50,
          offset: 0
        })
      })
    })
    
    // Reload to fetch mocked data
    await page.reload()
    
    // Verify all status badges are displayed correctly
    await expect(page.getByText('Pending')).toBeVisible()
    await expect(page.getByText('Processing')).toBeVisible()
    await expect(page.getByText('Completed')).toBeVisible()
    await expect(page.getByText('Failed')).toBeVisible()
  })

  test('should handle empty state correctly', async ({ page }) => {
    // Navigate to Knowledge Base page
    await page.goto('/knowledge-base')
    
    // Mock API to return no documents
    await page.route('**/api/documents', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          documents: [],
          total: 0,
          limit: 50,
          offset: 0
        })
      })
    })
    
    // Reload to fetch mocked data
    await page.reload()
    
    // Verify empty state is displayed
    await expect(page.getByText(/no documents uploaded yet/i)).toBeVisible()
    await expect(page.getByText(/start by uploading your first document/i)).toBeVisible()
  })

  test('should not allow deletion of documents without permission', async ({ page }) => {
    // Navigate to Knowledge Base page
    await page.goto('/knowledge-base')
    
    // Mock API to return 403 for delete request
    await page.route('**/api/documents/*', route => {
      if (route.request().method() === 'DELETE') {
        route.fulfill({
          status: 403,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Access denied' })
        })
      } else {
        route.continue()
      }
    })
    
    // Assuming a document is in the list
    await expect(page.getByText('restricted-document.pdf')).toBeVisible()
    
    // Click delete button
    const deleteButton = await page.getByRole('button', { name: /delete document/i })
    await deleteButton.click()
    
    // Confirm deletion
    const confirmDeleteButton = await page.getByRole('button', { name: 'Delete' })
    await confirmDeleteButton.click()
    
    // Verify error message
    await expect(page.getByText(/failed to delete document/i)).toBeVisible()
  })
})