import { test, expect } from '@playwright/test'
import { login } from '../helpers/auth'

test.describe('Knowledge Base Upload Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await login(page)
  })

  test('should navigate to knowledge base and upload files', async ({ page }) => {
    // Navigate to knowledge base
    await page.click('text=Knowledge Base')
    await expect(page).toHaveURL('/knowledge-base')
    
    // Verify page content
    await expect(page.locator('h1')).toContainText('Knowledge Base')
    await expect(page.locator('text=Upload and manage documents')).toBeVisible()
    
    // Verify upload area is present
    await expect(page.locator('text=Drag & drop files here')).toBeVisible()
    await expect(page.locator('text=Supports PDF, TXT, and DOCX files')).toBeVisible()
  })

  test('should upload a file and show progress', async ({ page }) => {
    await page.goto('/knowledge-base')
    
    // Create test file content
    const fileContent = Buffer.from('Test PDF content')
    
    // Upload file using file chooser
    const fileChooserPromise = page.waitForEvent('filechooser')
    await page.click('text=Drag & drop files here')
    const fileChooser = await fileChooserPromise
    
    await fileChooser.setFiles({
      name: 'test-document.pdf',
      mimeType: 'application/pdf',
      buffer: fileContent
    })
    
    // Verify file appears in list
    await expect(page.locator('text=test-document.pdf')).toBeVisible()
    await expect(page.locator('text=16 Bytes')).toBeVisible() // File size
    
    // Click upload button
    await page.click('button:has-text("Upload Files")')
    
    // Wait for upload to complete
    await expect(page.locator('[role="progressbar"]')).toBeVisible()
    
    // Verify success state (mocked in test environment)
    await expect(page.locator('text=Successfully uploaded')).toBeVisible({ timeout: 10000 })
  })

  test('should validate file types', async ({ page }) => {
    await page.goto('/knowledge-base')
    
    // Try to upload invalid file type
    const fileChooserPromise = page.waitForEvent('filechooser')
    await page.click('text=Drag & drop files here')
    const fileChooser = await fileChooserPromise
    
    await fileChooser.setFiles({
      name: 'invalid.exe',
      mimeType: 'application/x-msdownload',
      buffer: Buffer.from('Invalid file')
    })
    
    // Verify rejection alert
    page.on('dialog', async dialog => {
      expect(dialog.message()).toContain('Some files were rejected')
      await dialog.accept()
    })
  })

  test('should remove files before upload', async ({ page }) => {
    await page.goto('/knowledge-base')
    
    // Upload a file
    const fileChooserPromise = page.waitForEvent('filechooser')
    await page.click('text=Drag & drop files here')
    const fileChooser = await fileChooserPromise
    
    await fileChooser.setFiles({
      name: 'removable.txt',
      mimeType: 'text/plain',
      buffer: Buffer.from('Test content')
    })
    
    // Verify file appears
    await expect(page.locator('text=removable.txt')).toBeVisible()
    
    // Remove the file
    await page.click('button[aria-label="Remove file"]')
    
    // Verify file is removed
    await expect(page.locator('text=removable.txt')).not.toBeVisible()
  })

  test('should handle multiple file uploads', async ({ page }) => {
    await page.goto('/knowledge-base')
    
    // Upload multiple files
    const fileChooserPromise = page.waitForEvent('filechooser')
    await page.click('text=Drag & drop files here')
    const fileChooser = await fileChooserPromise
    
    await fileChooser.setFiles([
      {
        name: 'document1.pdf',
        mimeType: 'application/pdf',
        buffer: Buffer.from('PDF content 1')
      },
      {
        name: 'document2.txt',
        mimeType: 'text/plain',
        buffer: Buffer.from('Text content 2')
      },
      {
        name: 'document3.docx',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        buffer: Buffer.from('DOCX content 3')
      }
    ])
    
    // Verify all files appear
    await expect(page.locator('text=document1.pdf')).toBeVisible()
    await expect(page.locator('text=document2.txt')).toBeVisible()
    await expect(page.locator('text=document3.docx')).toBeVisible()
    
    // Verify file count
    await expect(page.locator('text=Selected Files (3)')).toBeVisible()
  })

  test('should handle upload errors gracefully', async ({ page }) => {
    await page.goto('/knowledge-base')
    
    // Mock API to return error
    await page.route('**/api/documents/upload', route => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Upload failed' })
      })
    })
    
    // Upload a file
    const fileChooserPromise = page.waitForEvent('filechooser')
    await page.click('text=Drag & drop files here')
    const fileChooser = await fileChooserPromise
    
    await fileChooser.setFiles({
      name: 'error-test.pdf',
      mimeType: 'application/pdf',
      buffer: Buffer.from('Test content')
    })
    
    // Try to upload
    await page.click('button:has-text("Upload Files")')
    
    // Verify error message
    await expect(page.locator('text=Failed to upload files')).toBeVisible()
  })
})