import { test, expect } from '@playwright/test';

test.describe('Project Dashboard Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.goto('/login');
    // Assuming login flow is handled elsewhere, we navigate directly
    await page.goto('/projects');
  });

  test('should navigate from projects list to project dashboard', async ({ page }) => {
    // Wait for projects list to load
    await page.waitForSelector('[role="button"][aria-label^="Open project:"]');
    
    // Click on the first project
    const firstProject = page.locator('[role="button"][aria-label^="Open project:"]').first();
    const projectName = await firstProject.getAttribute('aria-label');
    await firstProject.click();
    
    // Verify we're on the project dashboard
    await expect(page).toHaveURL(/\/projects\/[^\/]+$/);
    
    // Verify project name is displayed
    await expect(page.locator('h1')).toContainText(projectName?.replace('Open project: ', '') || '');
    
    // Verify breadcrumb navigation
    await expect(page.locator('nav')).toContainText('Dashboard');
    await expect(page.locator('nav')).toContainText('Projects');
    
    // Verify agents section is visible
    await expect(page.locator('h2')).toContainText('Agents');
    
    // Verify Create New Agent button is visible
    await expect(page.getByRole('button', { name: /create new agent/i })).toBeVisible();
  });

  test('should navigate to project settings', async ({ page }) => {
    // Navigate to a project first
    await page.waitForSelector('[role="button"][aria-label^="Open project:"]');
    await page.locator('[role="button"][aria-label^="Open project:"]').first().click();
    
    // Wait for project dashboard to load
    await page.waitForSelector('h1');
    
    // Click on settings button
    await page.getByRole('link', { name: /settings/i }).click();
    
    // Verify we're on the settings page
    await expect(page).toHaveURL(/\/projects\/[^\/]+\/settings$/);
    
    // Verify settings page content
    await expect(page.locator('h1')).toContainText('Project Settings');
    
    // Verify tabs are present
    await expect(page.getByRole('tab', { name: /general/i })).toBeVisible();
    await expect(page.getByRole('tab', { name: /members/i })).toBeVisible();
    await expect(page.getByRole('tab', { name: /knowledge base/i })).toBeVisible();
  });

  test('should navigate back to projects list using breadcrumb', async ({ page }) => {
    // Navigate to a project first
    await page.waitForSelector('[role="button"][aria-label^="Open project:"]');
    await page.locator('[role="button"][aria-label^="Open project:"]').first().click();
    
    // Wait for project dashboard to load
    await page.waitForSelector('nav');
    
    // Click on Projects in breadcrumb
    await page.locator('nav').getByRole('link', { name: 'Projects' }).click();
    
    // Verify we're back on the projects list
    await expect(page).toHaveURL('/projects');
  });

  test('should handle empty agents list', async ({ page }) => {
    // Mock empty agents response
    await page.route('**/api/projects/*/agents', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([]),
      });
    });
    
    // Navigate to a project
    await page.waitForSelector('[role="button"][aria-label^="Open project:"]');
    await page.locator('[role="button"][aria-label^="Open project:"]').first().click();
    
    // Verify empty state is displayed
    await expect(page.locator('text=No agents yet')).toBeVisible();
    await expect(page.locator('text=Click "Create New Agent"')).toBeVisible();
  });

  test('should display agents when present', async ({ page }) => {
    // Mock agents response
    await page.route('**/api/projects/*/agents', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'agent-1',
            title: 'Test Agent 1',
            workflowJson: {},
            triggerType: 'click',
            projectId: 'project-123',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: 'agent-2',
            title: 'Test Agent 2',
            workflowJson: {},
            triggerType: 'schedule',
            projectId: 'project-123',
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ]),
      });
    });
    
    // Navigate to a project
    await page.waitForSelector('[role="button"][aria-label^="Open project:"]');
    await page.locator('[role="button"][aria-label^="Open project:"]').first().click();
    
    // Verify agents are displayed
    await expect(page.locator('text=Test Agent 1')).toBeVisible();
    await expect(page.locator('text=Test Agent 2')).toBeVisible();
    await expect(page.locator('text=click')).toBeVisible();
    await expect(page.locator('text=schedule')).toBeVisible();
  });

  test('should handle project loading error', async ({ page }) => {
    // Mock project API error
    await page.route('**/api/projects/*', async route => {
      if (route.request().method() === 'GET' && !route.request().url().includes('/agents')) {
        await route.fulfill({
          status: 404,
          contentType: 'application/json',
          body: JSON.stringify({ message: 'Project not found' }),
        });
      } else {
        await route.continue();
      }
    });
    
    // Navigate directly to a project that doesn't exist
    await page.goto('/projects/non-existent-project');
    
    // Should redirect back to projects list
    await expect(page).toHaveURL('/projects');
  });
});