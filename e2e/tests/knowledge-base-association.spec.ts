import { test, expect } from '@playwright/test';

test.describe('Knowledge Base Document Association', () => {
  let projectId: string;

  test.beforeEach(async ({ page }) => {
    // TODO: Replace with actual auth flow when authentication is implemented
    // For now, we'll navigate directly assuming auth is handled
    await page.goto('/projects');
    
    // Wait for projects to load
    await page.waitForSelector('[data-testid="project-card"]', { timeout: 10000 });
    
    // Click on first project
    const firstProject = page.locator('[data-testid="project-card"]').first();
    await firstProject.click();
    
    // Wait for project dashboard
    await page.waitForURL(/\/projects\/[^\/]+$/);
    
    // Extract project ID from URL
    const url = page.url();
    projectId = url.split('/').pop() || '';
  });

  test('should navigate to Knowledge Base tab in project settings', async ({ page }) => {
    // Navigate to project settings
    await page.click('[data-testid="project-settings-link"], text=Settings');
    await page.waitForURL(`/projects/${projectId}/settings`);
    
    // Click on Knowledge Base tab
    await page.click('button[role="tab"]:has-text("Knowledge Base")');
    
    // Verify Knowledge Base content is visible
    await expect(page.locator('h2:has-text("Knowledge Base")')).toBeVisible();
    await expect(page.locator('text=Select which documents from the central Knowledge Base')).toBeVisible();
  });

  test('should display available documents with checkboxes', async ({ page }) => {
    // Navigate to settings and Knowledge Base tab
    await page.goto(`/projects/${projectId}/settings`);
    await page.click('button[role="tab"]:has-text("Knowledge Base")');
    
    // Wait for documents to load
    await page.waitForSelector('[data-testid="document-list-item"], .skeleton, text=No documents available', { timeout: 10000 });
    
    // Check if documents are available
    const documentsExist = await page.locator('[data-testid="document-list-item"]').count() > 0;
    
    if (documentsExist) {
      // Verify document items have checkboxes
      const firstDocument = page.locator('[data-testid="document-list-item"]').first();
      await expect(firstDocument.locator('input[type="checkbox"]')).toBeVisible();
      
      // Verify document displays file name and metadata
      await expect(firstDocument.locator('text=/\\.pdf$|\\.md$|\\.txt$/')).toBeVisible();
      await expect(firstDocument.locator('text=/\\d+ (KB|MB)/')).toBeVisible();
    } else {
      // Verify empty state message
      await expect(page.locator('text=No documents available')).toBeVisible();
    }
  });

  test('admin should be able to select/deselect documents', async ({ page }) => {
    // Navigate to settings and Knowledge Base tab
    await page.goto(`/projects/${projectId}/settings`);
    await page.click('button[role="tab"]:has-text("Knowledge Base")');
    
    // Wait for documents to load
    await page.waitForSelector('[data-testid="document-list-item"], text=No documents available', { timeout: 10000 });
    
    const documentsExist = await page.locator('[data-testid="document-list-item"]').count() > 0;
    
    if (documentsExist) {
      // Get first checkbox
      const firstCheckbox = page.locator('[data-testid="document-list-item"]').first().locator('input[type="checkbox"]');
      
      // Check if it's enabled (user is admin)
      const isDisabled = await firstCheckbox.isDisabled();
      
      if (!isDisabled) {
        // Toggle checkbox
        const wasChecked = await firstCheckbox.isChecked();
        await firstCheckbox.click();
        
        // Verify checkbox state changed
        await expect(firstCheckbox).toHaveProperty('checked', !wasChecked);
        
        // Verify save button appears
        await expect(page.locator('button:has-text("Save Changes")')).toBeVisible();
      }
    }
  });

  test('should save document associations successfully', async ({ page }) => {
    // Navigate to settings and Knowledge Base tab
    await page.goto(`/projects/${projectId}/settings`);
    await page.click('button[role="tab"]:has-text("Knowledge Base")');
    
    // Wait for documents to load
    await page.waitForSelector('[data-testid="document-list-item"], text=No documents available', { timeout: 10000 });
    
    const documentsExist = await page.locator('[data-testid="document-list-item"]').count() > 0;
    
    if (documentsExist) {
      // Toggle first document
      const firstCheckbox = page.locator('[data-testid="document-list-item"]').first().locator('input[type="checkbox"]');
      const isDisabled = await firstCheckbox.isDisabled();
      
      if (!isDisabled) {
        await firstCheckbox.click();
        
        // Click save button
        await page.click('button:has-text("Save Changes")');
        
        // Wait for success message
        await expect(page.locator('text=Document associations updated successfully')).toBeVisible({ timeout: 5000 });
        
        // Verify save button disappears
        await expect(page.locator('button:has-text("Save Changes")')).not.toBeVisible();
      }
    }
  });

  test('non-admin users should see read-only view', async ({ page }) => {
    // This test would require logging in as a non-admin user
    // For now, we'll check if the permission message is displayed when checkboxes are disabled
    
    await page.goto(`/projects/${projectId}/settings`);
    await page.click('button[role="tab"]:has-text("Knowledge Base")');
    
    // Wait for documents to load
    await page.waitForSelector('[data-testid="document-list-item"], text=No documents available, text=You need admin permissions', { timeout: 10000 });
    
    // Check if permission message is visible
    const permissionMessage = page.locator('text=You need admin permissions to modify document associations');
    const isPermissionMessageVisible = await permissionMessage.isVisible();
    
    if (isPermissionMessageVisible) {
      // Verify checkboxes are disabled
      const checkboxes = page.locator('[data-testid="document-list-item"] input[type="checkbox"]');
      const count = await checkboxes.count();
      
      for (let i = 0; i < count; i++) {
        await expect(checkboxes.nth(i)).toBeDisabled();
      }
    }
  });

  test('should display document count summary', async ({ page }) => {
    await page.goto(`/projects/${projectId}/settings`);
    await page.click('button[role="tab"]:has-text("Knowledge Base")');
    
    // Wait for documents to load
    await page.waitForSelector('[data-testid="document-list-item"], text=No documents available', { timeout: 10000 });
    
    // Check for count summary
    const countSummary = page.locator('text=/\\d+ of \\d+ documents selected/');
    if (await countSummary.isVisible()) {
      // Verify format
      await expect(countSummary).toMatch(/\d+ of \d+ documents selected/);
    }
  });
});

test.describe('Agent File Selection with Project Scope', () => {
  test('agent file search should only return project-associated documents', async ({ page }) => {
    // This test would require:
    // 1. Setting up document associations for a project
    // 2. Creating or opening an agent
    // 3. Using the /file command
    // 4. Verifying only associated documents appear
    
    // TODO: Implement when agent UI supports file selection
    test.skip();
  });
});