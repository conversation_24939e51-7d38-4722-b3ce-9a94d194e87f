import { test, expect } from '@playwright/test';

test.describe('Project Member Management', () => {
  let projectId: string;

  test.beforeEach(async ({ page }) => {
    // Login as admin user
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('/dashboard');
    
    // Navigate to projects
    await page.click('text=Projects');
    await page.waitForURL('/projects');
    
    // Create a new project or use existing
    const projectCards = await page.locator('.project-card').count();
    if (projectCards === 0) {
      await page.click('text=Create New Project');
      await page.fill('input[name="name"]', 'Test Project');
      await page.fill('textarea[name="description"]', 'Test project for member management');
      await page.click('button:has-text("Create")');
      await page.waitForSelector('text=Project created successfully');
    }
    
    // Click on first project
    await page.locator('.project-card').first().click();
    projectId = page.url().match(/projects\/([^/]+)/)?.[1] || '';
    
    // Navigate to settings
    await page.click('text=Settings');
    await page.waitForURL(`/projects/${projectId}/settings`);
  });

  test('should display members tab and show current user as admin', async ({ page }) => {
    // Click on Members tab
    await page.click('text=Members');
    
    // Should see the admin user in the list
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    
    // Should see admin badge
    await expect(page.locator('svg.text-primary')).toBeVisible();
    
    // Should see "You" indicator
    await expect(page.locator('text=You')).toBeVisible();
  });

  test('should add a new member to the project', async ({ page }) => {
    // Click on Members tab
    await page.click('text=Members');
    
    // Click Add Member button
    await page.click('button:has-text("Add Member")');
    
    // Wait for dialog
    await expect(page.locator('text=Add Team Member')).toBeVisible();
    
    // Search for a user
    await page.fill('input[placeholder*="Search by name or email"]', 'test');
    
    // Wait for search results
    await page.waitForTimeout(500); // Wait for debounce
    
    // Select first user from results
    await page.locator('button:has-text("<EMAIL>")').first().click();
    
    // Select role
    await page.click('button[role="combobox"]');
    await page.click('text=Member');
    
    // Add member
    await page.click('button:has-text("Add Member")');
    
    // Should see success message
    await expect(page.locator('text=Member added successfully')).toBeVisible();
    
    // New member should appear in list
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });

  test('should change member role', async ({ page }) => {
    // Ensure there's a member to manage
    await test.step('Add a member first', async () => {
      await page.click('text=Members');
      await page.click('button:has-text("Add Member")');
      await page.fill('input[placeholder*="Search by name or email"]', 'member');
      await page.waitForTimeout(500);
      await page.locator('button:has-text("<EMAIL>")').first().click();
      await page.click('button:has-text("Add Member")');
      await page.waitForSelector('text=Member added successfully');
    });
    
    // Find the member row and click action menu
    const memberRow = page.locator('div:has-text("<EMAIL>")').filter({ hasText: 'Member' });
    await memberRow.locator('button').last().click();
    
    // Click Make Admin
    await page.click('text=Make Admin');
    
    // Should see success message
    await expect(page.locator('text=is now an admin')).toBeVisible();
    
    // Should see admin badge
    await expect(memberRow.locator('svg.text-primary')).toBeVisible();
  });

  test('should remove a member from the project', async ({ page }) => {
    // Ensure there's a member to remove
    await test.step('Add a member first', async () => {
      await page.click('text=Members');
      await page.click('button:has-text("Add Member")');
      await page.fill('input[placeholder*="Search by name or email"]', 'remove');
      await page.waitForTimeout(500);
      await page.locator('button:has-text("<EMAIL>")').first().click();
      await page.click('button:has-text("Add Member")');
      await page.waitForSelector('text=Member added successfully');
    });
    
    // Find the member row and click action menu
    const memberRow = page.locator('div:has-text("<EMAIL>")');
    await memberRow.locator('button').last().click();
    
    // Click Remove from Project
    await page.click('text=Remove from Project');
    
    // Confirm removal in dialog
    await expect(page.locator('text=Remove Member')).toBeVisible();
    await page.click('button:has-text("Remove"):not(:has-text("from"))');
    
    // Should see success message
    await expect(page.locator('text=has been removed from the project')).toBeVisible();
    
    // Member should no longer be in list
    await expect(page.locator('text=<EMAIL>')).not.toBeVisible();
  });

  test('should prevent removing last admin', async ({ page }) => {
    // Click on Members tab
    await page.click('text=Members');
    
    // As the only admin, action menu should not be available
    const adminRow = page.locator('div:has-text("<EMAIL>")');
    
    // Should not have an action button (only "You" text)
    await expect(adminRow.locator('text=You')).toBeVisible();
    await expect(adminRow.locator('button[role="button"]')).not.toBeVisible();
  });

  test('should handle search with no results', async ({ page }) => {
    // Click on Members tab
    await page.click('text=Members');
    
    // Click Add Member button
    await page.click('button:has-text("Add Member")');
    
    // Search for non-existent user
    await page.fill('input[placeholder*="Search by name or email"]', 'nonexistentuser12345');
    
    // Wait for search
    await page.waitForTimeout(500);
    
    // Should show no results message
    await expect(page.locator('text=No users found')).toBeVisible();
  });

  test('non-admin user cannot manage members', async ({ page }) => {
    // First, add current user as member (not admin) to another project
    // This would require logging in as a different user or having a project where current user is just a member
    
    // For this test, we'll simulate by checking UI elements
    // In a real scenario, you'd have a separate non-admin user account
    
    // Navigate to a project where user is a member
    // The UI should not show Add Member button or action menus
    
    // This is a placeholder - in real implementation, you'd need:
    // 1. Multiple test users
    // 2. Projects with different membership roles
    // 3. Proper test data setup
    
    expect(true).toBe(true); // Placeholder assertion
  });
});