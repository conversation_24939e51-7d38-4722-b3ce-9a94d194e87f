import { test, expect } from '@playwright/test'

test.describe('Project Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Assuming user is already authenticated
    // You may need to add authentication steps here
    await page.goto('/dashboard')
  })

  test('complete project management flow', async ({ page }) => {
    // Step 1: Verify dashboard loads with projects or empty state
    await expect(page).toHaveTitle(/Dashboard/)
    
    // Check if we're in empty state or have projects
    const emptyState = page.locator('text=No projects yet')
    const projectList = page.locator('[role="button"][aria-label*="Open project"]')
    
    if (await emptyState.isVisible()) {
      // We're in empty state
      await expect(emptyState).toBeVisible()
      await expect(page.locator('text=Create your first project to get started')).toBeVisible()
    }

    // Step 2: Create a new project
    const createButton = page.locator('button:has-text("New Project"), button:has-text("Create Project")')
    await createButton.click()

    // Fill in project details
    const dialog = page.locator('[role="dialog"]')
    await expect(dialog).toBeVisible()
    await expect(dialog.locator('text=Create New Project')).toBeVisible()

    const projectName = `Test Project ${Date.now()}`
    const projectDescription = 'This is a test project created by E2E tests'

    await dialog.locator('input#project-name').fill(projectName)
    await dialog.locator('textarea#project-description').fill(projectDescription)

    // Verify character count
    await expect(dialog.locator('text=/\\d+\\/500/')).toBeVisible()

    // Submit the form
    await dialog.locator('button:has-text("Create Project")').click()

    // Step 3: Verify project was created and appears in the list
    await expect(page.locator(`text=${projectName}`)).toBeVisible({ timeout: 10000 })
    await expect(page.locator(`text=${projectDescription}`)).toBeVisible()

    // Step 4: Navigate to the project
    const projectCard = page.locator(`[role="button"][aria-label="Open project: ${projectName}"]`)
    await projectCard.click()

    // Verify navigation to project agents page
    await expect(page).toHaveURL(/\/projects\/[^\/]+\/agents/)
    
    // Step 5: Go back to dashboard
    await page.goto('/dashboard')

    // Step 6: Test project deletion
    const projectToDelete = page.locator(`text=${projectName}`).locator('..')
    const moreButton = projectToDelete.locator('button[aria-label="Project options"]')
    await moreButton.click()

    // Click delete option
    await page.locator('text=Delete Project').click()

    // Verify confirmation dialog
    const deleteDialog = page.locator('[role="dialog"]:has-text("Delete Project")')
    await expect(deleteDialog).toBeVisible()
    await expect(deleteDialog.locator(`text=/Are you sure you want to delete "${projectName}"/`)).toBeVisible()
    await expect(deleteDialog.locator('text=/This action cannot be undone/')).toBeVisible()

    // Confirm deletion
    await deleteDialog.locator('button:has-text("Delete")').click()

    // Step 7: Verify project was deleted
    await expect(page.locator(`text=${projectName}`)).not.toBeVisible({ timeout: 10000 })
  })

  test('create project with validation errors', async ({ page }) => {
    // Open create dialog
    const createButton = page.locator('button:has-text("New Project"), button:has-text("Create Project")')
    await createButton.click()

    const dialog = page.locator('[role="dialog"]')
    await expect(dialog).toBeVisible()

    // Try to submit with empty name
    await dialog.locator('button:has-text("Create Project")').click()

    // Should not close dialog
    await expect(dialog).toBeVisible()

    // Fill name with too many characters
    const longName = 'a'.repeat(101)
    await dialog.locator('input#project-name').fill(longName)

    // Should show validation error
    await expect(dialog.locator('text=Project name is too long')).toBeVisible()

    // Fill description with too many characters
    const longDescription = 'a'.repeat(501)
    await dialog.locator('textarea#project-description').fill(longDescription)

    // Should show validation error
    await expect(dialog.locator('text=Description is too long')).toBeVisible()

    // Cancel dialog
    await dialog.locator('button:has-text("Cancel")').click()
    await expect(dialog).not.toBeVisible()
  })

  test('project list displays correctly', async ({ page }) => {
    // Create multiple projects first
    const projects = [
      { name: `Alpha Project ${Date.now()}`, description: 'First test project' },
      { name: `Beta Project ${Date.now()}`, description: 'Second test project' },
      { name: `Gamma Project ${Date.now()}`, description: null },
    ]

    for (const project of projects) {
      const createButton = page.locator('button:has-text("New Project"), button:has-text("Create Project")')
      await createButton.click()

      const dialog = page.locator('[role="dialog"]')
      await dialog.locator('input#project-name').fill(project.name)
      
      if (project.description) {
        await dialog.locator('textarea#project-description').fill(project.description)
      }

      await dialog.locator('button:has-text("Create Project")').click()
      await expect(page.locator(`text=${project.name}`)).toBeVisible({ timeout: 10000 })
    }

    // Verify all projects are displayed
    for (const project of projects) {
      const projectCard = page.locator(`[role="button"][aria-label="Open project: ${project.name}"]`)
      await expect(projectCard).toBeVisible()
      
      if (project.description) {
        await expect(projectCard.locator(`text=${project.description}`)).toBeVisible()
      } else {
        await expect(projectCard.locator('text=No description')).toBeVisible()
      }

      // Verify date is displayed
      await expect(projectCard.locator('time')).toBeVisible()
    }

    // Clean up - delete all test projects
    for (const project of projects) {
      const projectCard = page.locator(`text=${project.name}`).locator('..')
      const moreButton = projectCard.locator('button[aria-label="Project options"]')
      await moreButton.click()
      await page.locator('text=Delete Project').click()
      
      const deleteDialog = page.locator('[role="dialog"]:has-text("Delete Project")')
      await deleteDialog.locator('button:has-text("Delete")').click()
      await expect(page.locator(`text=${project.name}`)).not.toBeVisible({ timeout: 10000 })
    }
  })

  test('responsive design', async ({ page, viewport }) => {
    // Test on mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Create a project
    const createButton = page.locator('button:has-text("New Project"), button:has-text("Create Project")')
    await createButton.click()

    const dialog = page.locator('[role="dialog"]')
    await dialog.locator('input#project-name').fill(`Mobile Test ${Date.now()}`)
    await dialog.locator('button:has-text("Create Project")').click()

    // Verify grid layout on mobile (should be single column)
    const grid = page.locator('.grid')
    await expect(grid).toHaveClass(/grid/)
    
    // Test on tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(grid).toHaveClass(/md:grid-cols-2/)

    // Test on desktop viewport
    await page.setViewportSize({ width: 1280, height: 800 })
    await expect(grid).toHaveClass(/lg:grid-cols-3/)
  })
})