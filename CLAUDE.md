# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

sflow is a visual AI workflow builder that allows users to create, customize, and deploy AI-powered agents through a block-based interface (similar to Notion). The platform enables businesses to build AI workflows visually and deploy them as APIs or web pages.

## Tech Stack

**Frontend (apps/web/)**
- Next.js 15+ with App Router
- TypeScript, React 18
- Zustand for state management
- shadcn/ui components with Radix UI
- BlockNote.js for the visual editor
- Tailwind CSS 4 for styling
- React Hook Form with Zod validation

**Backend (apps/api/)**
- Fastify 5 (high-performance Node.js framework)
- Prisma 6 ORM with PostgreSQL
- better-auth for authentication (OTP-based)
- Repository pattern for data access
- Zod for validation
- <PERSON><PERSON> for logging

## Development Commands

```bash
# Install dependencies (run from root)
pnpm install

# Run everything in dev mode
pnpm dev

# Run specific app
pnpm dev --filter=api
pnpm dev --filter=web

# Run tests
pnpm test
pnpm test --filter=api
pnpm test --filter=web

# Run a single test file
pnpm test --filter=api -- src/routes/auth.routes.test.ts

# Build everything
pnpm build

# Linting
pnpm lint

# Type checking
pnpm typecheck

# Database commands (from apps/api/)
cd apps/api
pnpm db:migrate      # Apply migrations
pnpm db:generate     # Generate Prisma client
pnpm db:studio       # Open Prisma Studio
pnpm db:seed         # Seed database
```

## Architecture

### Monorepo Structure
- Uses Turborepo with pnpm workspaces
- `apps/api/` - Backend API server
- `apps/web/` - Frontend Next.js application
- Shared TypeScript configuration

### Backend Architecture

**Repository Pattern**: All data access goes through repositories
```
src/
├── repositories/     # Data access layer
├── routes/          # API endpoints
├── services/        # Business logic
├── lib/            # Utilities
└── middleware/      # Request processing
```

**Key Design Patterns**:
- Repository pattern for clean data access separation
- Service layer for business logic
- Fastify plugins for modular route organization
- Zod schemas for runtime validation
- Dependency injection via Fastify decorators

### Frontend Architecture

**Component Structure**:
```
src/
├── app/            # Next.js app router pages
├── components/     # Reusable components
├── services/       # API client services
├── lib/           # Utilities
├── hooks/         # Custom React hooks
└── stores/        # Zustand state stores
```

**State Management**:
- Zustand for global state (auth, projects, etc.)
- React Query for server state
- Local component state for UI

### Data Models

**Core Entities**:
- `User` - Platform users
- `Workspace` - Top-level organization unit
- `Project` - Container for agents and documents
- `Agent` - AI workflow configuration
- `Document` - Knowledge base documents
- `Block` - Individual components in the visual editor

**Relationships**:
- Users belong to Workspaces through memberships
- Projects belong to Workspaces
- Agents belong to Projects
- Documents belong to Projects
- Blocks form a tree structure within Agents

### API Routes

**Authentication** (`/auth/*`):
- OTP-based login system
- Session management

**Core Resources**:
- `/workspaces/*` - Workspace management
- `/projects/*` - Project CRUD and settings
- `/agents/*` - Agent configuration
- `/documents/*` - Document upload and management
- `/search/*` - Document search with embeddings

### Security

- Authentication via better-auth with sessions
- Workspace-level isolation
- Role-based access control (owner, admin, member)
- Request validation with Zod
- SQL injection prevention via Prisma

### Authentication & Session Management

**Better-Auth Cookie Configuration**

Better-auth handles all authentication including session management through cookies. Here's how it works:

1. **Default Cookie Names**: Better-auth uses these default cookie names:
   - `better-auth.session_token` or `better-auth.session-token` (both formats are checked in middleware)
   
2. **⚠️ IMPORTANT: Do NOT use custom cookie names**
   - Always use better-auth's default cookie names
   - Do NOT add `cookieName` to the session configuration
   - The middleware is configured to check for the default names only

3. **Middleware Configuration**: The Next.js middleware (`apps/web/src/middleware.ts`) checks for:
   ```typescript
   const sessionCookie = request.cookies.get('better-auth.session_token') || 
                        request.cookies.get('better-auth.session-token')
   ```

4. **Cookie Settings**: Automatically configured by better-auth with:
   - HttpOnly: true (prevents JavaScript access)
   - Secure: true (in production only)
   - SameSite: "lax" (CSRF protection)
   - Path: "/" (available across the entire app)

5. **Session Duration** (configured in `apps/api/src/lib/auth.ts`):
   - Session expires in 7 days
   - Session updates every 24 hours of activity
   - Cookie cache for 5 minutes (performance optimization)

**Important**: Never modify the cookie configuration. Always use better-auth's default settings to ensure authentication works correctly across the application.

### Testing

- Jest for unit and integration tests
- Playwright for E2E tests
- Test database separation
- Repository mocking for isolated testing

### Environment Variables

**API** (apps/api/.env):
```
DATABASE_URL=
BETTER_AUTH_SECRET=
BETTER_AUTH_URL=
ENCRYPTION_KEY=
OPENAI_API_KEY=
PIPEDREAM_API_KEY=
```

**Web** (apps/web/.env.local):
```
NEXT_PUBLIC_API_URL=
NEXT_PUBLIC_APP_URL=
```

## Key Features to Understand

1. **Visual Block Editor**: The core feature - a Notion-like interface for building AI workflows
2. **Multi-tenant Architecture**: Strict workspace isolation for security
3. **Knowledge Base**: RAG system with document embeddings for context
4. **Deployment**: Agents can be deployed as APIs or embedded web pages
5. **Integrations**: Pipedream integration for connecting to external services

## Common Tasks

### Adding a New API Route
1. Create route file in `apps/api/src/routes/`
2. Add Zod schemas for validation
3. Create/update repository if needed
4. Register route in `apps/api/src/index.ts`
5. Write tests in parallel test file

### Adding a New UI Component
1. Check if shadcn/ui has the component: `pnpm dlx shadcn@latest add [component]`
2. If custom, create in `apps/web/src/components/`
3. Use Tailwind CSS 4 for styling
4. Follow existing component patterns

### Database Changes
1. Update schema in `apps/api/prisma/schema.prisma`
2. Create migration: `pnpm db:migrate`
3. Update repositories and types
4. Update seeds if needed

### Working with the Block Editor
- BlockNote.js powers the editor
- Blocks are stored as JSON in the database
- Custom blocks extend the base BlockNote schema
- Each block type has its own React component