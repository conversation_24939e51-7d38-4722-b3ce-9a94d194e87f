{"permissions": {"allow": ["Bash(md-tree:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm create:*)", "Bash(npm install:*)", "Bash(npx create-next-app:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(npx shadcn-ui:*)", "Bash(npm update:*)", "Bash(npm run dev:*)", "Bash(find:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run prisma:migrate:*)", "Bash(npm run typecheck:*)", "Bash(npm run lint)", "Bash(npm run lint)", "Bash(npm run test:*)", "Bash(npx:*)", "Bash(npm test:*)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npx next lint:*)", "<PERSON><PERSON>(cat:*)", "Bash(ls:*)", "WebFetch(domain:github.com)", "WebFetch(domain:www.better-auth.com)", "WebFetch(domain:fastify.dev)", "Bash(grep:*)", "Bash(pnpm run dev:*)", "<PERSON><PERSON>(test:*)", "Bash(pnpm add:*)", "Bash(pnpm db:push:*)", "Bash(node:*)", "Bash(git reset:*)", "WebFetch(domain:ui.shadcn.com)", "mcp__ide__executeCode", "Bash(pnpm list:*)", "<PERSON><PERSON>(touch:*)", "Bash(pnpm db:generate:*)", "Bash(pnpm lint:*)", "Bash(pnpm test:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(pnpm dev:*)", "Bash(kill:*)", "Bash(pnpm ls:*)", "Bash(psql:*)", "Bash(pnpm tsc:*)", "Bash(pnpm remove:*)", "Bash(pnpm build:*)", "Bash(pnpm update:*)", "Bash(pnpm prisma migrate dev:*)", "Bash(pnpm prisma migrate resolve:*)", "Bash(pnpm prisma generate:*)", "Bash(pnpm prisma:*)", "Bash(pnpm dlx:*)", "WebFetch(domain:www.blocknotejs.org)", "WebFetch(domain:www.blocknotejs.org)", "Bash(npm run:*)", "Bash(npm ls:*)", "Bash(pnpm --filter api prisma migrate status)", "Bash(TEST_COOKIE=\"u7cOVhQtFq75QxUVSK2iMnR328pOyadc\" npx tsx test-member-api.ts)", "Bash(npm exec:*)", "Bash(./node_modules/.bin/prisma db:*)", "Bash(./apps/api/node_modules/.bin/prisma db push:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_install", "Bash(pnpm db:studio:*)", "Bash(pnpm db:migrate:*)", "Bash(pnpm typecheck:*)", "Bash(pnpm run:*)"], "deny": []}}